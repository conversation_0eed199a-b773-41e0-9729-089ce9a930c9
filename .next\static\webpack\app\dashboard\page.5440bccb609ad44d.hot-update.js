"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useAdvancedSearch.ts":
/*!****************************************!*\
  !*** ./src/hooks/useAdvancedSearch.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAdvancedSearch: function() { return /* binding */ useAdvancedSearch; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useAdvancedSearch(models) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { debounceMs = 300, enableSuggestions = true, cacheResults = true, fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n    const [searchTerm, setSearchTermState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [hasSearched, setHasSearched] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [debounceTimer, setDebounceTimer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Cache para resultados de busca\n    const [searchCache] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Map());\n    // Função de busca principal\n    const performSearch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (term)=>{\n        if (!term.trim()) {\n            setSearchResults([]);\n            setHasSearched(false);\n            return;\n        }\n        setIsSearching(true);\n        try {\n            // Verificar cache se habilitado\n            if (cacheResults && searchCache.has(term)) {\n                setSearchResults(searchCache.get(term));\n                setHasSearched(true);\n                setIsSearching(false);\n                return;\n            }\n            // Realizar busca\n            const results = searchModels(models, term, {\n                fuzzyThreshold,\n                maxResults,\n                boostFavorites\n            });\n            // Salvar no cache se habilitado\n            if (cacheResults) {\n                searchCache.set(term, results);\n            }\n            setSearchResults(results);\n            setHasSearched(true);\n        } catch (error) {\n            console.error(\"Error performing search:\", error);\n            setSearchResults([]);\n        } finally{\n            setIsSearching(false);\n        }\n    }, [\n        models,\n        fuzzyThreshold,\n        maxResults,\n        boostFavorites,\n        cacheResults,\n        searchCache,\n        trackAnalytics,\n        userId\n    ]);\n    // Função para definir termo de busca com debounce\n    const setSearchTerm = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((term)=>{\n        setSearchTermState(term);\n        // Limpar timer anterior\n        if (debounceTimer) {\n            clearTimeout(debounceTimer);\n        }\n        // Configurar novo timer\n        const timer = setTimeout(()=>{\n            performSearch(term);\n        }, debounceMs);\n        setDebounceTimer(timer);\n    }, [\n        debounceTimer,\n        debounceMs,\n        performSearch\n    ]);\n    // Carregar sugestões quando o termo muda\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (enableSuggestions && searchTerm.length > 0 && userId) {\n            searchAnalyticsService.getSearchSuggestions(userId, searchTerm, 5).then(setSuggestions).catch((error)=>{\n                console.error(\"Error loading suggestions:\", error);\n                setSuggestions([]);\n            });\n        } else {\n            setSuggestions([]);\n        }\n    }, [\n        searchTerm,\n        enableSuggestions,\n        userId\n    ]);\n    // Limpar busca\n    const clearSearch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setSearchTermState(\"\");\n        setSearchResults([]);\n        setHasSearched(false);\n        setSuggestions([]);\n        if (debounceTimer) {\n            clearTimeout(debounceTimer);\n            setDebounceTimer(null);\n        }\n    }, [\n        debounceTimer\n    ]);\n    // Rastrear seleção de modelo\n    const trackModelSelection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (modelId)=>{\n        if (trackAnalytics && userId) {\n            const model = models.find((m)=>m.id === modelId);\n            if (model) {\n                await searchAnalyticsService.trackModelSelection(userId, modelId, model.name, hasSearched ? searchTerm : undefined);\n            }\n        }\n    }, [\n        trackAnalytics,\n        userId,\n        models,\n        hasSearched,\n        searchTerm\n    ]);\n    // Limpar timer ao desmontar\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            if (debounceTimer) {\n                clearTimeout(debounceTimer);\n            }\n        };\n    }, [\n        debounceTimer\n    ]);\n    return {\n        searchTerm,\n        setSearchTerm,\n        searchResults,\n        suggestions,\n        isSearching,\n        hasSearched,\n        clearSearch,\n        trackModelSelection\n    };\n}\n// Função de busca com fuzzy matching\nfunction searchModels(models, searchTerm) {\n    let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n    const { fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n    if (!searchTerm.trim()) {\n        return [];\n    }\n    const term = searchTerm.toLowerCase();\n    const results = [];\n    for (const model of models){\n        let score = 0;\n        const matchedFields = [];\n        let highlightedName = model.name;\n        let highlightedDescription = model.description || \"\";\n        // Busca no nome (peso maior)\n        if (model.name.toLowerCase().includes(term)) {\n            score += 10;\n            matchedFields.push(\"name\");\n            highlightedName = highlightText(model.name, term);\n        }\n        // Busca no ID (peso médio)\n        if (model.id.toLowerCase().includes(term)) {\n            score += 7;\n            matchedFields.push(\"id\");\n        }\n        // Busca na descrição (peso menor)\n        if (model.description && model.description.toLowerCase().includes(term)) {\n            score += 3;\n            matchedFields.push(\"description\");\n            highlightedDescription = highlightText(model.description, term);\n        }\n        // Boost para favoritos\n        if (boostFavorites && model.isFavorite) {\n            score *= 1.5;\n        }\n        // Boost para modelos gratuitos se buscar por \"free\" ou \"grátis\"\n        if ((term.includes(\"free\") || term.includes(\"gr\\xe1tis\")) && isModelFree(model)) {\n            score += 5;\n        }\n        // Boost para modelos caros se buscar por \"expensive\" ou \"caro\"\n        if ((term.includes(\"expensive\") || term.includes(\"caro\")) && getTotalPrice(model) > 0.00002) {\n            score += 5;\n        }\n        if (score > 0) {\n            results.push({\n                model,\n                score,\n                matchedFields,\n                highlightedName,\n                highlightedDescription\n            });\n        }\n    }\n    // Ordenar por score e limitar resultados\n    return results.sort((a, b)=>b.score - a.score).slice(0, maxResults);\n}\n// Função auxiliar para destacar texto\nfunction highlightText(text, term) {\n    const regex = new RegExp(\"(\".concat(term, \")\"), \"gi\");\n    return text.replace(regex, '<mark class=\"bg-yellow-300 text-black px-1 rounded\">$1</mark>');\n}\n// Função auxiliar para verificar se modelo é gratuito\nfunction isModelFree(model) {\n    const promptPrice = parseFloat(model.pricing.prompt);\n    const completionPrice = parseFloat(model.pricing.completion);\n    return promptPrice === 0 && completionPrice === 0;\n}\n// Função auxiliar para obter preço total\nfunction getTotalPrice(model) {\n    const promptPrice = parseFloat(model.pricing.prompt);\n    const completionPrice = parseFloat(model.pricing.completion);\n    return promptPrice + completionPrice;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\n"));

/***/ })

});