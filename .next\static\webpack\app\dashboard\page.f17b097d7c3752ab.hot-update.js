"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AppearanceContext */ \"(app-pages-browser)/./src/contexts/AppearanceContext.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./AttachmentsModal */ \"(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\");\n/* harmony import */ var _StatisticsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./StatisticsModal */ \"(app-pages-browser)/./src/components/dashboard/StatisticsModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ChatArea = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { currentChat, onChatCreated, onUpdateOpenRouterBalance, onChatSelect, sidebarRef } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { settings: appearanceSettings } = (0,_contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_6__.useAppearance)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStatisticsModalOpen, setIsStatisticsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Estados para drag-n-drop\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragCounter, setDragCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dragTimeout, setDragTimeout] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Detectar quando o drag sai da janela para resetar o estado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleWindowDragLeave = (e)=>{\n            // Se o drag sai da janela (relatedTarget é null ou é o documento), resetar o estado\n            if (!e.relatedTarget || e.relatedTarget === document.documentElement) {\n                resetDragState();\n            }\n        };\n        const handleWindowDragEnd = ()=>{\n            // Resetar estado quando o drag termina\n            resetDragState();\n        };\n        const handleWindowDrop = (e)=>{\n            // Resetar estado quando há drop em qualquer lugar\n            resetDragState();\n        };\n        const handleVisibilityChange = ()=>{\n            // Resetar estado quando a aba perde o foco\n            if (document.hidden) {\n                resetDragState();\n            }\n        };\n        const handleWindowBlur = ()=>{\n            // Resetar estado quando a janela perde o foco\n            resetDragState();\n        };\n        // Adicionar listeners globais\n        document.addEventListener(\"dragleave\", handleWindowDragLeave);\n        document.addEventListener(\"dragend\", handleWindowDragEnd);\n        document.addEventListener(\"drop\", handleWindowDrop);\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n        window.addEventListener(\"blur\", handleWindowBlur);\n        return ()=>{\n            document.removeEventListener(\"dragleave\", handleWindowDragLeave);\n            document.removeEventListener(\"dragend\", handleWindowDragEnd);\n            document.removeEventListener(\"drop\", handleWindowDrop);\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n            window.removeEventListener(\"blur\", handleWindowBlur);\n        };\n    }, [\n        dragTimeout\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado para um chat específico\n    const saveLastUsedModelForChat = async (modelId, chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            // Atualizar o lastUsedModel no documento do chat\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(chatRef, {\n                lastUsedModel: modelId,\n                lastModelUpdateAt: Date.now()\n            });\n        } catch (error) {\n            console.error(\"Error saving last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado de um chat específico\n    const loadLastUsedModelForChat = async (chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(chatRef);\n            if (chatDoc.exists()) {\n                const data = chatDoc.data();\n                if (data.lastUsedModel) {\n                    // Verificar se o modelo salvo ainda é válido\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (isValid) {\n                        setSelectedModel(data.lastUsedModel);\n                    } else {\n                        // Limpar o modelo inválido do chat\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(chatRef, {\n                            lastUsedModel: \"\"\n                        });\n                        // Carregar o modelo padrão do endpoint ativo\n                        loadDefaultModelFromActiveEndpoint();\n                    }\n                } else {\n                    // Se o chat não tem modelo salvo, carregar o modelo padrão do endpoint ativo\n                    loadDefaultModelFromActiveEndpoint();\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o modelo padrão do endpoint ativo\n    const loadDefaultModelFromActiveEndpoint = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                // Primeiro, tentar carregar o último modelo usado globalmente\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    return;\n                }\n                // Se não há último modelo usado, buscar o modelo padrão do endpoint ativo\n                if (data.endpoints) {\n                    const activeEndpoint = Object.values(data.endpoints).find((endpoint)=>endpoint.ativo);\n                    if (activeEndpoint && activeEndpoint.modeloPadrao) {\n                        setSelectedModel(activeEndpoint.modeloPadrao);\n                        return;\n                    }\n                }\n            }\n            // Fallback para o modelo padrão hardcoded\n            setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n        } catch (error) {\n            console.error(\"Error loading default model from active endpoint:\", error);\n            // Fallback para o modelo padrão hardcoded em caso de erro\n            setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n        }\n    };\n    // Função para validar se um modelo ainda existe/é válido\n    const isValidModel = async (modelId)=>{\n        // Lista de modelos conhecidos como inválidos ou removidos\n        const invalidModels = [\n            \"qwen/qwen3-235b-a22b-thinking-2507\"\n        ];\n        return !invalidModels.includes(modelId);\n    };\n    // Função para limpar modelos inválidos de todos os chats do usuário\n    const cleanupInvalidModelsFromAllChats = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\");\n            const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(chatsRef);\n            const updatePromises = [];\n            for (const chatDoc of chatsSnapshot.docs){\n                const data = chatDoc.data();\n                if (data.lastUsedModel) {\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (!isValid) {\n                        updatePromises.push((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatDoc.id), {\n                            lastUsedModel: \"\"\n                        }));\n                    }\n                }\n            }\n            if (updatePromises.length > 0) {\n                await Promise.all(updatePromises);\n            }\n        } catch (error) {\n            console.error(\"Error cleaning invalid models from chats:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado globalmente (fallback)\n    const loadGlobalLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    // Verificar se o modelo salvo ainda é válido\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (isValid) {\n                        setSelectedModel(data.lastUsedModel);\n                    } else {\n                        // Limpar o modelo inválido das configurações\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(userRef, {\n                            lastUsedModel: null\n                        });\n                        // Carregar o modelo padrão do endpoint ativo\n                        loadDefaultModelFromActiveEndpoint();\n                    }\n                } else {\n                    // Se não há último modelo usado, carregar o modelo padrão do endpoint ativo\n                    loadDefaultModelFromActiveEndpoint();\n                }\n            } else {\n                // Se não há configurações, carregar o modelo padrão do endpoint ativo\n                loadDefaultModelFromActiveEndpoint();\n            }\n        } catch (error) {\n            console.error(\"Error loading global last used model:\", error);\n            // Fallback para carregar o modelo padrão do endpoint ativo\n            loadDefaultModelFromActiveEndpoint();\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        // Salvar no chat específico se houver um chat ativo\n        if (actualChatId) {\n            saveLastUsedModelForChat(modelId, actualChatId);\n        }\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const firebaseStorageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(firebaseStorageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            // Definir o nome do chat imediatamente após criação\n            setChatName(finalChatName);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments, webSearchEnabled)=>{\n        // Obter anexos históricos ativos\n        const historicalAttachments = getAllChatAttachments().filter((att)=>att.isActive !== false);\n        // Combinar anexos novos com anexos históricos ativos\n        const allAttachmentsToSend = [\n            ...attachments || [],\n            ...historicalAttachments // Anexos históricos ativos\n        ];\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachmentsToSend.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // O nome já foi definido na função createAutoChat, mas vamos garantir carregando também\n                loadChatName(chatIdToUse);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Mover chat para o topo com animação\n        if (chatIdToUse && (sidebarRef === null || sidebarRef === void 0 ? void 0 : sidebarRef.current)) {\n            sidebarRef.current.moveToTop(chatIdToUse);\n        }\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA (incluindo anexos históricos ativos + anexos novos)\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: uniqueAttachments,\n            webSearchEnabled: webSearchEnabled,\n            userMessageId: userMessage.id\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString(),\n                        hasWebSearch: webSearchEnabled\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            // Salvar o modelo usado no chat específico\n            if (chatIdToUse) {\n                saveLastUsedModelForChat(selectedModel, chatIdToUse);\n            }\n            // Atualizar saldo do OpenRouter após a resposta com delay de 5 segundos\n            if (onUpdateOpenRouterBalance) {\n                setTimeout(()=>{\n                    onUpdateOpenRouterBalance();\n                }, 5000);\n            }\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                const chatName = chatData.name || \"Conversa sem nome\";\n                setChatName(chatName);\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, chatId);\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(chatMessages);\n            setMessages(convertedMessages);\n        } catch (error) {\n            console.error(\"❌ Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado globalmente quando o componente montar (apenas se não há chat)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && !currentChat) {\n            // Limpar modelos inválidos uma vez quando o usuário faz login\n            cleanupInvalidModelsFromAllChats();\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        user,\n        currentChat\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n            // Carregar o modelo específico do chat\n            loadLastUsedModelForChat(currentChat);\n        } else if (!currentChat && actualChatId) {\n            // Só resetar se realmente não há chat e havia um chat antes\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n            // Carregar modelo global quando não há chat específico\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // ✅ CORREÇÃO: Recarregar mensagens do Firebase Storage para garantir estado atualizado\n        console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o para garantir estado atualizado...\");\n        try {\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                setIsLoading(false);\n                setIsStreaming(false);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // Deletar apenas as mensagens POSTERIORES do Firebase Storage (não a mensagem atual)\n            console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n            for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                const msgToDelete = convertedFreshMessages[i];\n                console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true,\n                userMessageId: messageToRegenerate.id\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração com delay de 5 segundos\n                if (onUpdateOpenRouterBalance) {\n                    setTimeout(()=>{\n                        onUpdateOpenRouterBalance();\n                    }, 5000);\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return false;\n        console.log(\"✏️ Iniciando edi\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            console.log(\"\\uD83D\\uDCE4 Enviando atualiza\\xe7\\xe3o para o servidor...\");\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                console.error(\"❌ Falha ao atualizar mensagem no servidor\");\n                loadChatMessages(actualChatId);\n                return false;\n            } else {\n                console.log(\"✅ Mensagem editada e salva com sucesso no Firebase Storage:\", {\n                    messageId,\n                    timestamp: new Date().toISOString()\n                });\n                return true;\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            console.error(\"❌ Erro ao atualizar mensagem:\", error);\n            loadChatMessages(actualChatId);\n            return false;\n        }\n    };\n    const handleEditAndRegenerate = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        console.log(\"✏️\\uD83D\\uDD04 Iniciando edi\\xe7\\xe3o e regenera\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        try {\n            // 1. Primeiro, salvar a edição\n            const editSuccess = await handleEditMessage(messageId, newContent);\n            if (!editSuccess) {\n                console.error(\"❌ Falha ao editar mensagem, cancelando regenera\\xe7\\xe3o\");\n                return;\n            }\n            console.log(\"✅ Mensagem editada com sucesso, iniciando regenera\\xe7\\xe3o...\");\n            // 2. Aguardar um pouco para garantir que a edição foi salva\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            // 3. Recarregar mensagens do Firebase Storage para garantir estado atualizado\n            console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o...\");\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // 4. Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // 5. Verificar se há mensagens após esta mensagem\n            const hasMessagesAfter = messageIndex < convertedFreshMessages.length - 1;\n            console.log(\"\\uD83D\\uDCCA Mensagens ap\\xf3s esta: \".concat(convertedFreshMessages.length - messageIndex - 1));\n            // 6. Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // 7. Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // 8. Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // 9. Deletar apenas as mensagens POSTERIORES do Firebase Storage (se houver)\n            if (hasMessagesAfter) {\n                console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n                for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                    const msgToDelete = convertedFreshMessages[i];\n                    console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                    await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n                }\n            }\n            // 10. Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true,\n                userMessageId: messageToRegenerate.id\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração com delay de 5 segundos\n                if (onUpdateOpenRouterBalance) {\n                    setTimeout(()=>{\n                        onUpdateOpenRouterBalance();\n                    }, 5000);\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao editar e regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: 0,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: scrollContainer.scrollHeight,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = (chatId)=>{\n        // Se um chatId específico foi fornecido e é diferente do atual, muda para esse chat primeiro\n        if (chatId && chatId !== currentChat && onChatSelect) {\n            onChatSelect(chatId);\n            // Abre o modal após um pequeno delay para garantir que o chat foi carregado\n            setTimeout(()=>{\n                setIsDownloadModalOpen(true);\n            }, 100);\n        } else {\n            setIsDownloadModalOpen(true);\n        }\n    };\n    // Função para abrir o modal de anexos\n    const handleAttachmentsModal = (chatId)=>{\n        // Se um chatId específico foi fornecido e é diferente do atual, muda para esse chat primeiro\n        if (chatId && chatId !== currentChat && onChatSelect) {\n            onChatSelect(chatId);\n            setTimeout(()=>{\n                setIsAttachmentsModalOpen(true);\n            }, 100);\n        } else {\n            setIsAttachmentsModalOpen(true);\n        }\n    };\n    // Função para abrir o modal de estatísticas\n    const handleStatisticsModal = (chatId)=>{\n        // Se um chatId específico foi fornecido e é diferente do atual, muda para esse chat primeiro\n        if (chatId && chatId !== currentChat && onChatSelect) {\n            onChatSelect(chatId);\n            setTimeout(()=>{\n                setIsStatisticsModalOpen(true);\n            }, 100);\n        } else {\n            setIsStatisticsModalOpen(true);\n        }\n    };\n    // Expor as funções para o componente pai\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            handleDownloadModal,\n            handleAttachmentsModal,\n            handleStatisticsModal\n        }));\n    // Função para obter todos os anexos do chat\n    const getAllChatAttachments = ()=>{\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                allAttachments.push(...message.attachments);\n            }\n        });\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachments.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        return uniqueAttachments;\n    };\n    // Função para salvar o estado dos anexos no Firebase Storage\n    const saveAttachmentStates = async (updatedMessages)=>{\n        if (!currentUsername || !actualChatId) return;\n        try {\n            // Preparar dados do chat para salvar\n            const chatData = {\n                id: actualChatId,\n                name: chatName || \"Chat\",\n                messages: updatedMessages.map((msg)=>({\n                        id: msg.id,\n                        content: msg.content,\n                        role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                        timestamp: msg.timestamp,\n                        isFavorite: msg.isFavorite,\n                        attachments: msg.attachments\n                    })),\n                createdAt: new Date().toISOString(),\n                lastUpdated: new Date().toISOString()\n            };\n            // Salvar no Firebase Storage\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(currentUsername, \"/conversas/\").concat(actualChatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(chatJsonRef, chatJsonBlob);\n            console.log(\"✅ Estado dos anexos salvo no Firebase Storage\");\n            console.log(\"\\uD83D\\uDCC1 Dados salvos:\", {\n                chatId: actualChatId,\n                totalMessages: chatData.messages.length,\n                messagesWithAttachments: chatData.messages.filter((msg)=>msg.attachments && msg.attachments.length > 0).length\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao salvar estado dos anexos:\", error);\n        }\n    };\n    // Função para alternar o estado ativo de um anexo\n    const handleToggleAttachment = (attachmentId)=>{\n        setMessages((prevMessages)=>{\n            const updatedMessages = prevMessages.map((message)=>{\n                if (message.attachments && message.attachments.length > 0) {\n                    const updatedAttachments = message.attachments.map((attachment)=>{\n                        if (attachment.id === attachmentId) {\n                            // Se isActive não está definido, considerar como true (ativo por padrão)\n                            const currentState = attachment.isActive !== false;\n                            return {\n                                ...attachment,\n                                isActive: !currentState\n                            };\n                        }\n                        return attachment;\n                    });\n                    return {\n                        ...message,\n                        attachments: updatedAttachments\n                    };\n                }\n                return message;\n            });\n            // Salvar o estado atualizado no Firebase Storage\n            saveAttachmentStates(updatedMessages);\n            return updatedMessages;\n        });\n    };\n    // Função para filtrar anexos ativos para envio à IA\n    const getActiveAttachments = (attachments)=>{\n        if (!attachments) return [];\n        // Para anexos novos (sem isActive definido), incluir por padrão\n        // Para anexos existentes, verificar se isActive não é false\n        return attachments.filter((attachment)=>{\n            // Se isActive não está definido (anexo novo), incluir\n            if (attachment.isActive === undefined) return true;\n            // Se isActive está definido, incluir apenas se não for false\n            return attachment.isActive !== false;\n        });\n    };\n    // Função para obter IDs dos anexos ativos\n    const getActiveAttachmentIds = ()=>{\n        const allAttachments = getAllChatAttachments();\n        return allAttachments.filter((att)=>att.isActive !== false).map((att)=>att.id);\n    };\n    // Função para resetar estado de drag\n    const resetDragState = ()=>{\n        setIsDragOver(false);\n        setDragCounter(0);\n        if (dragTimeout) {\n            clearTimeout(dragTimeout);\n            setDragTimeout(null);\n        }\n    };\n    // Funções para drag-n-drop\n    const handleDragEnter = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragCounter((prev)=>prev + 1);\n        // Verificar se há arquivos sendo arrastados\n        if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n            // Verificar se pelo menos um item é um arquivo\n            const hasFiles = Array.from(e.dataTransfer.items).some((item)=>item.kind === \"file\");\n            if (hasFiles) {\n                setIsDragOver(true);\n                // Limpar timeout anterior se existir\n                if (dragTimeout) {\n                    clearTimeout(dragTimeout);\n                }\n                // Definir timeout de segurança para resetar o estado após 3 segundos\n                const timeout = setTimeout(()=>{\n                    resetDragState();\n                }, 3000);\n                setDragTimeout(timeout);\n            }\n        }\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragCounter((prev)=>{\n            const newCount = prev - 1;\n            // Só remove o overlay quando o contador chega a 0\n            if (newCount <= 0) {\n                resetDragState();\n                return 0;\n            }\n            return newCount;\n        });\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        // Verificar se há arquivos sendo arrastados\n        if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n            const hasFiles = Array.from(e.dataTransfer.items).some((item)=>item.kind === \"file\");\n            if (hasFiles) {\n                e.dataTransfer.dropEffect = \"copy\";\n            } else {\n                e.dataTransfer.dropEffect = \"none\";\n            }\n        }\n    };\n    const handleDrop = async (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        // Sempre resetar o estado de drag, independentemente do que acontecer\n        resetDragState();\n        const files = Array.from(e.dataTransfer.files);\n        // Se não há arquivos, apenas sair\n        if (files.length === 0) {\n            return;\n        }\n        // Verificar se temos username necessário\n        if (!currentUsername) {\n            console.error(\"Username n\\xe3o dispon\\xedvel para upload de anexos\");\n            return;\n        }\n        // Se não há chat atual, criar um automaticamente para poder fazer upload dos anexos\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = files.length === 1 ? \"Arquivo anexado: \".concat(files[0].name) : \"\".concat(files.length, \" arquivos anexados\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                loadChatName(chatIdToUse);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID para anexos\");\n            return;\n        }\n        try {\n            // Importar o attachmentService dinamicamente\n            const { default: attachmentService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/services/attachmentService */ \"(app-pages-browser)/./src/lib/services/attachmentService.ts\"));\n            // Fazer upload dos arquivos\n            const uploadedAttachments = await attachmentService.uploadMultipleAttachments(files, currentUsername, chatIdToUse);\n            // Em vez de enviar a mensagem, vamos notificar o InputBar sobre os novos anexos\n            // Isso será feito através de um evento customizado\n            const attachmentMetadata = uploadedAttachments.map((att)=>att.metadata);\n            // Disparar evento customizado para o InputBar capturar\n            const event = new CustomEvent(\"dragDropAttachments\", {\n                detail: {\n                    attachments: attachmentMetadata,\n                    chatId: chatIdToUse,\n                    username: currentUsername\n                }\n            });\n            window.dispatchEvent(event);\n            console.log(\"✅ \".concat(files.length, \" arquivo(s) adicionado(s) como anexo via drag-n-drop\"));\n        } catch (error) {\n            console.error(\"❌ Erro ao processar arquivos via drag-n-drop:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen relative\",\n        onDragEnter: handleDragEnter,\n        onDragLeave: handleDragLeave,\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: [\n            isDragOver && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-50 bg-blue-900/80 backdrop-blur-sm flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-800/90 backdrop-blur-md rounded-2xl p-8 border-2 border-dashed border-blue-400 shadow-2xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-16 h-16 text-blue-300 mx-auto animate-bounce\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                        lineNumber: 1368,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                    lineNumber: 1367,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                lineNumber: 1366,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-blue-100 mb-2\",\n                                children: \"Solte os arquivos aqui\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                lineNumber: 1371,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-300 text-sm\",\n                                children: \"Os arquivos ser\\xe3o adicionados como anexos \\xe0 conversa\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                lineNumber: 1374,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                        lineNumber: 1365,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 1364,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1363,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0\",\n                style: {\n                    height: \"calc(100vh - 140px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    chatId: actualChatId || \"temp-chat\",\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    isStreaming: isStreaming,\n                    streamingMessageId: streamingMessageId || undefined,\n                    enableSessions: appearanceSettings.sessionsEnabled,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onEditAndRegenerate: handleEditAndRegenerate,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 1386,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1385,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                onOpenAttachmentsModal: handleAttachmentsModal,\n                username: currentUsername,\n                chatId: actualChatId || undefined,\n                activeAttachmentsCount: getActiveAttachmentIds().length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1403,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1422,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1430,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isAttachmentsModalOpen,\n                onClose: ()=>setIsAttachmentsModalOpen(false),\n                attachments: getAllChatAttachments(),\n                activeAttachments: getActiveAttachmentIds(),\n                onToggleAttachment: handleToggleAttachment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1438,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatisticsModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                isOpen: isStatisticsModalOpen,\n                onClose: ()=>setIsStatisticsModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1447,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 1354,\n        columnNumber: 5\n    }, undefined);\n}, \"TG3De0pWCvgUEXIDn5rLo82n0v4=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_6__.useAppearance\n    ];\n})), \"TG3De0pWCvgUEXIDn5rLo82n0v4=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_6__.useAppearance\n    ];\n});\n_c1 = ChatArea;\nChatArea.displayName = \"ChatArea\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (ChatArea);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatArea$forwardRef\");\n$RefreshReg$(_c1, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});