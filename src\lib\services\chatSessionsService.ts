import { ChatMessage } from '@/lib/types/chat';
import { 
  ChatSession, 
  ChatSessionsMetadata, 
  SessionDivisionResult, 
  ChatContext,
  SessionNavigation,
  ChatSessionsService as IChatSessionsService
} from '@/lib/types/chatSessions';

/**
 * Utilitário para contar palavras em uma mensagem
 */
function countWordsInMessage(message: ChatMessage): number {
  if (!message.content) return 0;
  
  // Remove markdown, HTML e caracteres especiais para contagem mais precisa
  const cleanContent = message.content
    .replace(/```[\s\S]*?```/g, '') // Remove blocos de código
    .replace(/`[^`]*`/g, '') // Remove código inline
    .replace(/<[^>]*>/g, '') // Remove HTML
    .replace(/[^\w\s]/g, ' ') // Remove pontuação
    .replace(/\s+/g, ' ') // Normaliza espaços
    .trim();
  
  if (!cleanContent) return 0;
  
  return cleanContent.split(' ').filter(word => word.length > 0).length;
}

/**
 * Gera um ID único para sessão
 */
function generateSessionId(chatId: string, sessionIndex: number): string {
  return `${chatId}_session_${sessionIndex}_${Date.now()}`;
}

/**
 * Implementação do serviço de gerenciamento de sessões de chat
 */
export class ChatSessionsService implements IChatSessionsService {
  private loadedSessions: Map<string, ChatSession> = new Map();
  private sessionsMetadata: Map<string, ChatSessionsMetadata> = new Map();

  /**
   * Divide mensagens em sessões baseado na contagem de palavras
   * IMPORTANTE: Sessões são apenas para visualização, IA sempre usa todas as mensagens
   */
  divideIntoSessions(messages: ChatMessage[], wordsPerSession: number, chatId: string = 'unknown'): SessionDivisionResult {
    if (messages.length === 0) {
      throw new Error('Não é possível dividir uma lista vazia de mensagens');
    }

    const sessions: ChatSession[] = [];
    let currentSessionMessages: ChatMessage[] = [];
    let currentWordCount = 0;
    let sessionIndex = 0;
    let messageIndex = 0;

    // chatId será passado como parâmetro separado
    const now = Date.now();

    for (const message of messages) {
      const messageWordCount = countWordsInMessage(message);
      
      // Se adicionar esta mensagem exceder o limite e já temos mensagens na sessão atual
      if (currentWordCount + messageWordCount > wordsPerSession && currentSessionMessages.length > 0) {
        // Finaliza a sessão atual
        const session: ChatSession = {
          id: generateSessionId(chatId, sessionIndex),
          startMessageIndex: messageIndex - currentSessionMessages.length,
          endMessageIndex: messageIndex - 1,
          wordCount: currentWordCount,
          messages: [...currentSessionMessages],
          createdAt: now,
          lastUpdated: now,
          isLoaded: true,
          isActive: false
        };
        
        sessions.push(session);
        
        // Inicia nova sessão
        currentSessionMessages = [message];
        currentWordCount = messageWordCount;
        sessionIndex++;
      } else {
        // Adiciona mensagem à sessão atual
        currentSessionMessages.push(message);
        currentWordCount += messageWordCount;
      }
      
      messageIndex++;
    }

    // Adiciona a última sessão se houver mensagens restantes
    if (currentSessionMessages.length > 0) {
      const session: ChatSession = {
        id: generateSessionId(chatId, sessionIndex),
        startMessageIndex: messageIndex - currentSessionMessages.length,
        endMessageIndex: messageIndex - 1,
        wordCount: currentWordCount,
        messages: [...currentSessionMessages],
        createdAt: now,
        lastUpdated: now,
        isLoaded: true,
        isActive: true // A última sessão é sempre a ativa
      };
      
      sessions.push(session);
    }

    // Marca apenas a última sessão como ativa
    sessions.forEach((session, index) => {
      session.isActive = index === sessions.length - 1;
    });

    // Cria metadados
    const totalWords = sessions.reduce((sum, session) => sum + session.wordCount, 0);
    const metadata: ChatSessionsMetadata = {
      chatId,
      totalSessions: sessions.length,
      wordsPerSession,
      activeSessionId: sessions[sessions.length - 1]?.id || '',
      sessionIds: sessions.map(s => s.id),
      lastSessionUpdate: now,
      totalMessages: messages.length,
      totalWords
    };

    // Calcula estatísticas
    const stats = {
      totalSessions: sessions.length,
      averageWordsPerSession: sessions.length > 0 ? Math.round(totalWords / sessions.length) : 0,
      totalWords,
      totalMessages: messages.length
    };

    // Armazena em cache
    this.sessionsMetadata.set(chatId, metadata);
    sessions.forEach(session => {
      this.loadedSessions.set(session.id, session);
    });

    return {
      sessions,
      metadata,
      stats
    };
  }

  /**
   * Carrega uma sessão específica para visualização
   */
  async loadSessionForView(chatId: string, sessionId: string): Promise<ChatSession> {
    // Verifica se já está carregada
    const cached = this.loadedSessions.get(sessionId);
    if (cached) {
      return cached;
    }

    // Em uma implementação real, aqui carregaria do Firebase Storage
    // Por enquanto, retorna erro se não estiver em cache
    throw new Error(`Sessão ${sessionId} não encontrada`);
  }

  /**
   * Descarrega uma sessão da memória
   */
  unloadSession(sessionId: string): void {
    this.loadedSessions.delete(sessionId);
  }

  /**
   * Obtém metadados das sessões de um chat
   */
  async getSessionsMetadata(chatId: string): Promise<ChatSessionsMetadata | null> {
    return this.sessionsMetadata.get(chatId) || null;
  }

  /**
   * Reorganiza sessões após mudanças nas mensagens
   */
  async reorganizeSessions(chatId: string, messages: ChatMessage[]): Promise<SessionDivisionResult> {
    // Remove sessões antigas do cache
    const oldMetadata = this.sessionsMetadata.get(chatId);
    if (oldMetadata) {
      oldMetadata.sessionIds.forEach(sessionId => {
        this.unloadSession(sessionId);
      });
    }

    // Obtém configuração atual de palavras por sessão
    const wordsPerSession = oldMetadata?.wordsPerSession || 5000;

    // Recria as sessões
    return this.divideIntoSessions(messages, wordsPerSession);
  }

  /**
   * Navega para uma sessão específica
   */
  async navigateToSession(sessionId: string): Promise<SessionNavigation> {
    const session = this.loadedSessions.get(sessionId);
    if (!session) {
      throw new Error(`Sessão ${sessionId} não encontrada`);
    }

    // Encontrar metadados pela sessão
    let metadata: ChatSessionsMetadata | undefined;
    this.sessionsMetadata.forEach((meta) => {
      if (meta.sessionIds.includes(sessionId)) {
        metadata = meta;
      }
    });

    if (!metadata) {
      throw new Error('Metadados da sessão não encontrados');
    }

    const currentIndex = metadata.sessionIds.indexOf(sessionId);
    
    return {
      currentSession: session,
      hasPrevious: currentIndex > 0,
      hasNext: currentIndex < metadata.sessionIds.length - 1,
      currentIndex,
      totalSessions: metadata.totalSessions
    };
  }

  /**
   * Obtém sessão ativa (mais recente) para visualização
   */
  async getActiveSession(chatId: string): Promise<ChatSession | null> {
    const metadata = this.sessionsMetadata.get(chatId);
    if (!metadata || !metadata.activeSessionId) {
      return null;
    }

    return this.loadedSessions.get(metadata.activeSessionId) || null;
  }

  /**
   * Obtém contexto completo do chat (TODAS as mensagens para IA)
   * IMPORTANTE: Sempre retorna todas as mensagens, independente das sessões
   */
  async getFullChatContext(chatId: string): Promise<ChatContext> {
    const metadata = this.sessionsMetadata.get(chatId);
    if (!metadata) {
      throw new Error(`Contexto do chat ${chatId} não encontrado`);
    }

    // Coleta todas as mensagens de todas as sessões
    const allMessages: ChatMessage[] = [];
    const sessions: ChatSession[] = [];

    for (const sessionId of metadata.sessionIds) {
      const session = this.loadedSessions.get(sessionId);
      if (session) {
        sessions.push(session);
        allMessages.push(...session.messages);
      }
    }

    // Ordena mensagens por timestamp para garantir ordem correta
    allMessages.sort((a, b) => a.timestamp - b.timestamp);

    const currentViewSession = await this.getActiveSession(chatId);

    return {
      allMessages, // TODAS as mensagens para contexto da IA
      sessions,
      metadata,
      currentViewSession
    };
  }

  /**
   * Limpa cache de sessões
   */
  clearCache(): void {
    this.loadedSessions.clear();
    this.sessionsMetadata.clear();
  }
}

// Instância singleton do serviço
export const chatSessionsService = new ChatSessionsService();
