/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[3]!./node_modules/katex/dist/katex.min.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
@font-face{font-family:KaTeX_AMS;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_AMS-Regular.a79f1c31.woff2) format("woff2"),url(/_next/static/media/KaTeX_AMS-Regular.1608a09b.woff) format("woff"),url(/_next/static/media/KaTeX_AMS-Regular.4aafdb68.ttf) format("truetype")}@font-face{font-family:KaTeX_Caligraphic;font-style:normal;font-weight:700;src:url(/_next/static/media/KaTeX_Caligraphic-Bold.ec17d132.woff2) format("woff2"),url(/_next/static/media/KaTeX_Caligraphic-Bold.b6770918.woff) format("woff"),url(/_next/static/media/KaTeX_Caligraphic-Bold.cce5b8ec.ttf) format("truetype")}@font-face{font-family:KaTeX_Caligraphic;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Caligraphic-Regular.55fac258.woff2) format("woff2"),url(/_next/static/media/KaTeX_Caligraphic-Regular.dad44a7f.woff) format("woff"),url(/_next/static/media/KaTeX_Caligraphic-Regular.07ef19e7.ttf) format("truetype")}@font-face{font-family:KaTeX_Fraktur;font-style:normal;font-weight:700;src:url(/_next/static/media/KaTeX_Fraktur-Bold.d42a5579.woff2) format("woff2"),url(/_next/static/media/KaTeX_Fraktur-Bold.9f256b85.woff) format("woff"),url(/_next/static/media/KaTeX_Fraktur-Bold.b18f59e1.ttf) format("truetype")}@font-face{font-family:KaTeX_Fraktur;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Fraktur-Regular.d3c882a6.woff2) format("woff2"),url(/_next/static/media/KaTeX_Fraktur-Regular.7c187121.woff) format("woff"),url(/_next/static/media/KaTeX_Fraktur-Regular.ed38e79f.ttf) format("truetype")}@font-face{font-family:KaTeX_Main;font-style:normal;font-weight:700;src:url(/_next/static/media/KaTeX_Main-Bold.c3fb5ac2.woff2) format("woff2"),url(/_next/static/media/KaTeX_Main-Bold.d181c465.woff) format("woff"),url(/_next/static/media/KaTeX_Main-Bold.b74a1a8b.ttf) format("truetype")}@font-face{font-family:KaTeX_Main;font-style:italic;font-weight:700;src:url(/_next/static/media/KaTeX_Main-BoldItalic.6f2bb1df.woff2) format("woff2"),url(/_next/static/media/KaTeX_Main-BoldItalic.e3f82f9d.woff) format("woff"),url(/_next/static/media/KaTeX_Main-BoldItalic.70d8b0a5.ttf) format("truetype")}@font-face{font-family:KaTeX_Main;font-style:italic;font-weight:400;src:url(/_next/static/media/KaTeX_Main-Italic.8916142b.woff2) format("woff2"),url(/_next/static/media/KaTeX_Main-Italic.9024d815.woff) format("woff"),url(/_next/static/media/KaTeX_Main-Italic.47373d1e.ttf) format("truetype")}@font-face{font-family:KaTeX_Main;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Main-Regular.0462f03b.woff2) format("woff2"),url(/_next/static/media/KaTeX_Main-Regular.7f51fe03.woff) format("woff"),url(/_next/static/media/KaTeX_Main-Regular.b7f8fe9b.ttf) format("truetype")}@font-face{font-family:KaTeX_Math;font-style:italic;font-weight:700;src:url(/_next/static/media/KaTeX_Math-BoldItalic.572d331f.woff2) format("woff2"),url(/_next/static/media/KaTeX_Math-BoldItalic.f1035d8d.woff) format("woff"),url(/_next/static/media/KaTeX_Math-BoldItalic.a879cf83.ttf) format("truetype")}@font-face{font-family:KaTeX_Math;font-style:italic;font-weight:400;src:url(/_next/static/media/KaTeX_Math-Italic.f28c23ac.woff2) format("woff2"),url(/_next/static/media/KaTeX_Math-Italic.5295ba48.woff) format("woff"),url(/_next/static/media/KaTeX_Math-Italic.939bc644.ttf) format("truetype")}@font-face{font-family:"KaTeX_SansSerif";font-style:normal;font-weight:700;src:url(/_next/static/media/KaTeX_SansSerif-Bold.8c5b5494.woff2) format("woff2"),url(/_next/static/media/KaTeX_SansSerif-Bold.bf59d231.woff) format("woff"),url(/_next/static/media/KaTeX_SansSerif-Bold.94e1e8dc.ttf) format("truetype")}@font-face{font-family:"KaTeX_SansSerif";font-style:italic;font-weight:400;src:url(/_next/static/media/KaTeX_SansSerif-Italic.3b1e59b3.woff2) format("woff2"),url(/_next/static/media/KaTeX_SansSerif-Italic.7c9bc82b.woff) format("woff"),url(/_next/static/media/KaTeX_SansSerif-Italic.b4c20c84.ttf) format("truetype")}@font-face{font-family:"KaTeX_SansSerif";font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_SansSerif-Regular.ba21ed5f.woff2) format("woff2"),url(/_next/static/media/KaTeX_SansSerif-Regular.74048478.woff) format("woff"),url(/_next/static/media/KaTeX_SansSerif-Regular.d4d7ba48.ttf) format("truetype")}@font-face{font-family:KaTeX_Script;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Script-Regular.03e9641d.woff2) format("woff2"),url(/_next/static/media/KaTeX_Script-Regular.07505710.woff) format("woff"),url(/_next/static/media/KaTeX_Script-Regular.fe9cbbe1.ttf) format("truetype")}@font-face{font-family:KaTeX_Size1;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Size1-Regular.eae34984.woff2) format("woff2"),url(/_next/static/media/KaTeX_Size1-Regular.e1e279cb.woff) format("woff"),url(/_next/static/media/KaTeX_Size1-Regular.fabc004a.ttf) format("truetype")}@font-face{font-family:KaTeX_Size2;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Size2-Regular.5916a24f.woff2) format("woff2"),url(/_next/static/media/KaTeX_Size2-Regular.57727022.woff) format("woff"),url(/_next/static/media/KaTeX_Size2-Regular.d6b476ec.ttf) format("truetype")}@font-face{font-family:KaTeX_Size3;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Size3-Regular.b4230e7e.woff2) format("woff2"),url(/_next/static/media/KaTeX_Size3-Regular.9acaf01c.woff) format("woff"),url(/_next/static/media/KaTeX_Size3-Regular.a144ef58.ttf) format("truetype")}@font-face{font-family:KaTeX_Size4;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Size4-Regular.10d95fd3.woff2) format("woff2"),url(/_next/static/media/KaTeX_Size4-Regular.7a996c9d.woff) format("woff"),url(/_next/static/media/KaTeX_Size4-Regular.fbccdabe.ttf) format("truetype")}@font-face{font-family:KaTeX_Typewriter;font-style:normal;font-weight:400;src:url(/_next/static/media/KaTeX_Typewriter-Regular.a8709e36.woff2) format("woff2"),url(/_next/static/media/KaTeX_Typewriter-Regular.6258592b.woff) format("woff"),url(/_next/static/media/KaTeX_Typewriter-Regular.d97aaf4a.ttf) format("truetype")}.katex{font:normal 1.21em KaTeX_Main,Times New Roman,serif;line-height:1.2;text-indent:0;text-rendering:auto}.katex *{-ms-high-contrast-adjust:none!important;border-color:currentColor}.katex .katex-version:after{content:"0.16.22"}.katex .katex-mathml{clip:rect(1px,1px,1px,1px);border:0;height:1px;overflow:hidden;padding:0;position:absolute;width:1px}.katex .katex-html>.newline{display:block}.katex .base{position:relative;white-space:nowrap;width:-moz-min-content;width:min-content}.katex .base,.katex .strut{display:inline-block}.katex .textbf{font-weight:700}.katex .textit{font-style:italic}.katex .textrm{font-family:KaTeX_Main}.katex .textsf{font-family:KaTeX_SansSerif}.katex .texttt{font-family:KaTeX_Typewriter}.katex .mathnormal{font-family:KaTeX_Math;font-style:italic}.katex .mathit{font-family:KaTeX_Main;font-style:italic}.katex .mathrm{font-style:normal}.katex .mathbf{font-family:KaTeX_Main;font-weight:700}.katex .boldsymbol{font-family:KaTeX_Math;font-style:italic;font-weight:700}.katex .amsrm,.katex .mathbb,.katex .textbb{font-family:KaTeX_AMS}.katex .mathcal{font-family:KaTeX_Caligraphic}.katex .mathfrak,.katex .textfrak{font-family:KaTeX_Fraktur}.katex .mathboldfrak,.katex .textboldfrak{font-family:KaTeX_Fraktur;font-weight:700}.katex .mathtt{font-family:KaTeX_Typewriter}.katex .mathscr,.katex .textscr{font-family:KaTeX_Script}.katex .mathsf,.katex .textsf{font-family:KaTeX_SansSerif}.katex .mathboldsf,.katex .textboldsf{font-family:KaTeX_SansSerif;font-weight:700}.katex .mathitsf,.katex .mathsfit,.katex .textitsf{font-family:KaTeX_SansSerif;font-style:italic}.katex .mainrm{font-family:KaTeX_Main;font-style:normal}.katex .vlist-t{border-collapse:collapse;display:inline-table;table-layout:fixed}.katex .vlist-r{display:table-row}.katex .vlist{display:table-cell;position:relative;vertical-align:bottom}.katex .vlist>span{display:block;height:0;position:relative}.katex .vlist>span>span{display:inline-block}.katex .vlist>span>.pstrut{overflow:hidden;width:0}.katex .vlist-t2{margin-right:-2px}.katex .vlist-s{display:table-cell;font-size:1px;min-width:2px;vertical-align:bottom;width:2px}.katex .vbox{align-items:baseline;display:inline-flex;flex-direction:column}.katex .hbox{width:100%}.katex .hbox,.katex .thinbox{display:inline-flex;flex-direction:row}.katex .thinbox{max-width:0;width:0}.katex .msupsub{text-align:left}.katex .mfrac>span>span{text-align:center}.katex .mfrac .frac-line{border-bottom-style:solid;display:inline-block;width:100%}.katex .hdashline,.katex .hline,.katex .mfrac .frac-line,.katex .overline .overline-line,.katex .rule,.katex .underline .underline-line{min-height:1px}.katex .mspace{display:inline-block}.katex .clap,.katex .llap,.katex .rlap{position:relative;width:0}.katex .clap>.inner,.katex .llap>.inner,.katex .rlap>.inner{position:absolute}.katex .clap>.fix,.katex .llap>.fix,.katex .rlap>.fix{display:inline-block}.katex .llap>.inner{right:0}.katex .clap>.inner,.katex .rlap>.inner{left:0}.katex .clap>.inner>span{margin-left:-50%;margin-right:50%}.katex .rule{border:0 solid;display:inline-block;position:relative}.katex .hline,.katex .overline .overline-line,.katex .underline .underline-line{border-bottom-style:solid;display:inline-block;width:100%}.katex .hdashline{border-bottom-style:dashed;display:inline-block;width:100%}.katex .sqrt>.root{margin-left:.2777777778em;margin-right:-.5555555556em}.katex .fontsize-ensurer.reset-size1.size1,.katex .sizing.reset-size1.size1{font-size:1em}.katex .fontsize-ensurer.reset-size1.size2,.katex .sizing.reset-size1.size2{font-size:1.2em}.katex .fontsize-ensurer.reset-size1.size3,.katex .sizing.reset-size1.size3{font-size:1.4em}.katex .fontsize-ensurer.reset-size1.size4,.katex .sizing.reset-size1.size4{font-size:1.6em}.katex .fontsize-ensurer.reset-size1.size5,.katex .sizing.reset-size1.size5{font-size:1.8em}.katex .fontsize-ensurer.reset-size1.size6,.katex .sizing.reset-size1.size6{font-size:2em}.katex .fontsize-ensurer.reset-size1.size7,.katex .sizing.reset-size1.size7{font-size:2.4em}.katex .fontsize-ensurer.reset-size1.size8,.katex .sizing.reset-size1.size8{font-size:2.88em}.katex .fontsize-ensurer.reset-size1.size9,.katex .sizing.reset-size1.size9{font-size:3.456em}.katex .fontsize-ensurer.reset-size1.size10,.katex .sizing.reset-size1.size10{font-size:4.148em}.katex .fontsize-ensurer.reset-size1.size11,.katex .sizing.reset-size1.size11{font-size:4.976em}.katex .fontsize-ensurer.reset-size2.size1,.katex .sizing.reset-size2.size1{font-size:.8333333333em}.katex .fontsize-ensurer.reset-size2.size2,.katex .sizing.reset-size2.size2{font-size:1em}.katex .fontsize-ensurer.reset-size2.size3,.katex .sizing.reset-size2.size3{font-size:1.1666666667em}.katex .fontsize-ensurer.reset-size2.size4,.katex .sizing.reset-size2.size4{font-size:1.3333333333em}.katex .fontsize-ensurer.reset-size2.size5,.katex .sizing.reset-size2.size5{font-size:1.5em}.katex .fontsize-ensurer.reset-size2.size6,.katex .sizing.reset-size2.size6{font-size:1.6666666667em}.katex .fontsize-ensurer.reset-size2.size7,.katex .sizing.reset-size2.size7{font-size:2em}.katex .fontsize-ensurer.reset-size2.size8,.katex .sizing.reset-size2.size8{font-size:2.4em}.katex .fontsize-ensurer.reset-size2.size9,.katex .sizing.reset-size2.size9{font-size:2.88em}.katex .fontsize-ensurer.reset-size2.size10,.katex .sizing.reset-size2.size10{font-size:3.4566666667em}.katex .fontsize-ensurer.reset-size2.size11,.katex .sizing.reset-size2.size11{font-size:4.1466666667em}.katex .fontsize-ensurer.reset-size3.size1,.katex .sizing.reset-size3.size1{font-size:.7142857143em}.katex .fontsize-ensurer.reset-size3.size2,.katex .sizing.reset-size3.size2{font-size:.8571428571em}.katex .fontsize-ensurer.reset-size3.size3,.katex .sizing.reset-size3.size3{font-size:1em}.katex .fontsize-ensurer.reset-size3.size4,.katex .sizing.reset-size3.size4{font-size:1.1428571429em}.katex .fontsize-ensurer.reset-size3.size5,.katex .sizing.reset-size3.size5{font-size:1.2857142857em}.katex .fontsize-ensurer.reset-size3.size6,.katex .sizing.reset-size3.size6{font-size:1.4285714286em}.katex .fontsize-ensurer.reset-size3.size7,.katex .sizing.reset-size3.size7{font-size:1.7142857143em}.katex .fontsize-ensurer.reset-size3.size8,.katex .sizing.reset-size3.size8{font-size:2.0571428571em}.katex .fontsize-ensurer.reset-size3.size9,.katex .sizing.reset-size3.size9{font-size:2.4685714286em}.katex .fontsize-ensurer.reset-size3.size10,.katex .sizing.reset-size3.size10{font-size:2.9628571429em}.katex .fontsize-ensurer.reset-size3.size11,.katex .sizing.reset-size3.size11{font-size:3.5542857143em}.katex .fontsize-ensurer.reset-size4.size1,.katex .sizing.reset-size4.size1{font-size:.625em}.katex .fontsize-ensurer.reset-size4.size2,.katex .sizing.reset-size4.size2{font-size:.75em}.katex .fontsize-ensurer.reset-size4.size3,.katex .sizing.reset-size4.size3{font-size:.875em}.katex .fontsize-ensurer.reset-size4.size4,.katex .sizing.reset-size4.size4{font-size:1em}.katex .fontsize-ensurer.reset-size4.size5,.katex .sizing.reset-size4.size5{font-size:1.125em}.katex .fontsize-ensurer.reset-size4.size6,.katex .sizing.reset-size4.size6{font-size:1.25em}.katex .fontsize-ensurer.reset-size4.size7,.katex .sizing.reset-size4.size7{font-size:1.5em}.katex .fontsize-ensurer.reset-size4.size8,.katex .sizing.reset-size4.size8{font-size:1.8em}.katex .fontsize-ensurer.reset-size4.size9,.katex .sizing.reset-size4.size9{font-size:2.16em}.katex .fontsize-ensurer.reset-size4.size10,.katex .sizing.reset-size4.size10{font-size:2.5925em}.katex .fontsize-ensurer.reset-size4.size11,.katex .sizing.reset-size4.size11{font-size:3.11em}.katex .fontsize-ensurer.reset-size5.size1,.katex .sizing.reset-size5.size1{font-size:.5555555556em}.katex .fontsize-ensurer.reset-size5.size2,.katex .sizing.reset-size5.size2{font-size:.6666666667em}.katex .fontsize-ensurer.reset-size5.size3,.katex .sizing.reset-size5.size3{font-size:.7777777778em}.katex .fontsize-ensurer.reset-size5.size4,.katex .sizing.reset-size5.size4{font-size:.8888888889em}.katex .fontsize-ensurer.reset-size5.size5,.katex .sizing.reset-size5.size5{font-size:1em}.katex .fontsize-ensurer.reset-size5.size6,.katex .sizing.reset-size5.size6{font-size:1.1111111111em}.katex .fontsize-ensurer.reset-size5.size7,.katex .sizing.reset-size5.size7{font-size:1.3333333333em}.katex .fontsize-ensurer.reset-size5.size8,.katex .sizing.reset-size5.size8{font-size:1.6em}.katex .fontsize-ensurer.reset-size5.size9,.katex .sizing.reset-size5.size9{font-size:1.92em}.katex .fontsize-ensurer.reset-size5.size10,.katex .sizing.reset-size5.size10{font-size:2.3044444444em}.katex .fontsize-ensurer.reset-size5.size11,.katex .sizing.reset-size5.size11{font-size:2.7644444444em}.katex .fontsize-ensurer.reset-size6.size1,.katex .sizing.reset-size6.size1{font-size:.5em}.katex .fontsize-ensurer.reset-size6.size2,.katex .sizing.reset-size6.size2{font-size:.6em}.katex .fontsize-ensurer.reset-size6.size3,.katex .sizing.reset-size6.size3{font-size:.7em}.katex .fontsize-ensurer.reset-size6.size4,.katex .sizing.reset-size6.size4{font-size:.8em}.katex .fontsize-ensurer.reset-size6.size5,.katex .sizing.reset-size6.size5{font-size:.9em}.katex .fontsize-ensurer.reset-size6.size6,.katex .sizing.reset-size6.size6{font-size:1em}.katex .fontsize-ensurer.reset-size6.size7,.katex .sizing.reset-size6.size7{font-size:1.2em}.katex .fontsize-ensurer.reset-size6.size8,.katex .sizing.reset-size6.size8{font-size:1.44em}.katex .fontsize-ensurer.reset-size6.size9,.katex .sizing.reset-size6.size9{font-size:1.728em}.katex .fontsize-ensurer.reset-size6.size10,.katex .sizing.reset-size6.size10{font-size:2.074em}.katex .fontsize-ensurer.reset-size6.size11,.katex .sizing.reset-size6.size11{font-size:2.488em}.katex .fontsize-ensurer.reset-size7.size1,.katex .sizing.reset-size7.size1{font-size:.4166666667em}.katex .fontsize-ensurer.reset-size7.size2,.katex .sizing.reset-size7.size2{font-size:.5em}.katex .fontsize-ensurer.reset-size7.size3,.katex .sizing.reset-size7.size3{font-size:.5833333333em}.katex .fontsize-ensurer.reset-size7.size4,.katex .sizing.reset-size7.size4{font-size:.6666666667em}.katex .fontsize-ensurer.reset-size7.size5,.katex .sizing.reset-size7.size5{font-size:.75em}.katex .fontsize-ensurer.reset-size7.size6,.katex .sizing.reset-size7.size6{font-size:.8333333333em}.katex .fontsize-ensurer.reset-size7.size7,.katex .sizing.reset-size7.size7{font-size:1em}.katex .fontsize-ensurer.reset-size7.size8,.katex .sizing.reset-size7.size8{font-size:1.2em}.katex .fontsize-ensurer.reset-size7.size9,.katex .sizing.reset-size7.size9{font-size:1.44em}.katex .fontsize-ensurer.reset-size7.size10,.katex .sizing.reset-size7.size10{font-size:1.7283333333em}.katex .fontsize-ensurer.reset-size7.size11,.katex .sizing.reset-size7.size11{font-size:2.0733333333em}.katex .fontsize-ensurer.reset-size8.size1,.katex .sizing.reset-size8.size1{font-size:.3472222222em}.katex .fontsize-ensurer.reset-size8.size2,.katex .sizing.reset-size8.size2{font-size:.4166666667em}.katex .fontsize-ensurer.reset-size8.size3,.katex .sizing.reset-size8.size3{font-size:.4861111111em}.katex .fontsize-ensurer.reset-size8.size4,.katex .sizing.reset-size8.size4{font-size:.5555555556em}.katex .fontsize-ensurer.reset-size8.size5,.katex .sizing.reset-size8.size5{font-size:.625em}.katex .fontsize-ensurer.reset-size8.size6,.katex .sizing.reset-size8.size6{font-size:.6944444444em}.katex .fontsize-ensurer.reset-size8.size7,.katex .sizing.reset-size8.size7{font-size:.8333333333em}.katex .fontsize-ensurer.reset-size8.size8,.katex .sizing.reset-size8.size8{font-size:1em}.katex .fontsize-ensurer.reset-size8.size9,.katex .sizing.reset-size8.size9{font-size:1.2em}.katex .fontsize-ensurer.reset-size8.size10,.katex .sizing.reset-size8.size10{font-size:1.4402777778em}.katex .fontsize-ensurer.reset-size8.size11,.katex .sizing.reset-size8.size11{font-size:1.7277777778em}.katex .fontsize-ensurer.reset-size9.size1,.katex .sizing.reset-size9.size1{font-size:.2893518519em}.katex .fontsize-ensurer.reset-size9.size2,.katex .sizing.reset-size9.size2{font-size:.3472222222em}.katex .fontsize-ensurer.reset-size9.size3,.katex .sizing.reset-size9.size3{font-size:.4050925926em}.katex .fontsize-ensurer.reset-size9.size4,.katex .sizing.reset-size9.size4{font-size:.462962963em}.katex .fontsize-ensurer.reset-size9.size5,.katex .sizing.reset-size9.size5{font-size:.5208333333em}.katex .fontsize-ensurer.reset-size9.size6,.katex .sizing.reset-size9.size6{font-size:.5787037037em}.katex .fontsize-ensurer.reset-size9.size7,.katex .sizing.reset-size9.size7{font-size:.6944444444em}.katex .fontsize-ensurer.reset-size9.size8,.katex .sizing.reset-size9.size8{font-size:.8333333333em}.katex .fontsize-ensurer.reset-size9.size9,.katex .sizing.reset-size9.size9{font-size:1em}.katex .fontsize-ensurer.reset-size9.size10,.katex .sizing.reset-size9.size10{font-size:1.2002314815em}.katex .fontsize-ensurer.reset-size9.size11,.katex .sizing.reset-size9.size11{font-size:1.4398148148em}.katex .fontsize-ensurer.reset-size10.size1,.katex .sizing.reset-size10.size1{font-size:.2410800386em}.katex .fontsize-ensurer.reset-size10.size2,.katex .sizing.reset-size10.size2{font-size:.2892960463em}.katex .fontsize-ensurer.reset-size10.size3,.katex .sizing.reset-size10.size3{font-size:.337512054em}.katex .fontsize-ensurer.reset-size10.size4,.katex .sizing.reset-size10.size4{font-size:.3857280617em}.katex .fontsize-ensurer.reset-size10.size5,.katex .sizing.reset-size10.size5{font-size:.4339440694em}.katex .fontsize-ensurer.reset-size10.size6,.katex .sizing.reset-size10.size6{font-size:.4821600771em}.katex .fontsize-ensurer.reset-size10.size7,.katex .sizing.reset-size10.size7{font-size:.5785920926em}.katex .fontsize-ensurer.reset-size10.size8,.katex .sizing.reset-size10.size8{font-size:.6943105111em}.katex .fontsize-ensurer.reset-size10.size9,.katex .sizing.reset-size10.size9{font-size:.8331726133em}.katex .fontsize-ensurer.reset-size10.size10,.katex .sizing.reset-size10.size10{font-size:1em}.katex .fontsize-ensurer.reset-size10.size11,.katex .sizing.reset-size10.size11{font-size:1.1996142719em}.katex .fontsize-ensurer.reset-size11.size1,.katex .sizing.reset-size11.size1{font-size:.2009646302em}.katex .fontsize-ensurer.reset-size11.size2,.katex .sizing.reset-size11.size2{font-size:.2411575563em}.katex .fontsize-ensurer.reset-size11.size3,.katex .sizing.reset-size11.size3{font-size:.2813504823em}.katex .fontsize-ensurer.reset-size11.size4,.katex .sizing.reset-size11.size4{font-size:.3215434084em}.katex .fontsize-ensurer.reset-size11.size5,.katex .sizing.reset-size11.size5{font-size:.3617363344em}.katex .fontsize-ensurer.reset-size11.size6,.katex .sizing.reset-size11.size6{font-size:.4019292605em}.katex .fontsize-ensurer.reset-size11.size7,.katex .sizing.reset-size11.size7{font-size:.4823151125em}.katex .fontsize-ensurer.reset-size11.size8,.katex .sizing.reset-size11.size8{font-size:.578778135em}.katex .fontsize-ensurer.reset-size11.size9,.katex .sizing.reset-size11.size9{font-size:.6945337621em}.katex .fontsize-ensurer.reset-size11.size10,.katex .sizing.reset-size11.size10{font-size:.8336012862em}.katex .fontsize-ensurer.reset-size11.size11,.katex .sizing.reset-size11.size11{font-size:1em}.katex .delimsizing.size1{font-family:KaTeX_Size1}.katex .delimsizing.size2{font-family:KaTeX_Size2}.katex .delimsizing.size3{font-family:KaTeX_Size3}.katex .delimsizing.size4{font-family:KaTeX_Size4}.katex .delimsizing.mult .delim-size1>span{font-family:KaTeX_Size1}.katex .delimsizing.mult .delim-size4>span{font-family:KaTeX_Size4}.katex .nulldelimiter{display:inline-block;width:.12em}.katex .delimcenter,.katex .op-symbol{position:relative}.katex .op-symbol.small-op{font-family:KaTeX_Size1}.katex .op-symbol.large-op{font-family:KaTeX_Size2}.katex .accent>.vlist-t,.katex .op-limits>.vlist-t{text-align:center}.katex .accent .accent-body{position:relative}.katex .accent .accent-body:not(.accent-full){width:0}.katex .overlay{display:block}.katex .mtable .vertical-separator{display:inline-block;min-width:1px}.katex .mtable .arraycolsep{display:inline-block}.katex .mtable .col-align-c>.vlist-t{text-align:center}.katex .mtable .col-align-l>.vlist-t{text-align:left}.katex .mtable .col-align-r>.vlist-t{text-align:right}.katex .svg-align{text-align:left}.katex svg{fill:currentColor;stroke:currentColor;fill-rule:nonzero;fill-opacity:1;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;display:block;height:inherit;position:absolute;width:100%}.katex svg path{stroke:none}.katex img{border-style:none;max-height:none;max-width:none;min-height:0;min-width:0}.katex .stretchy{display:block;overflow:hidden;position:relative;width:100%}.katex .stretchy:after,.katex .stretchy:before{content:""}.katex .hide-tail{overflow:hidden;position:relative;width:100%}.katex .halfarrow-left{left:0;overflow:hidden;position:absolute;width:50.2%}.katex .halfarrow-right{overflow:hidden;position:absolute;right:0;width:50.2%}.katex .brace-left{left:0;overflow:hidden;position:absolute;width:25.1%}.katex .brace-center{left:25%;overflow:hidden;position:absolute;width:50%}.katex .brace-right{overflow:hidden;position:absolute;right:0;width:25.1%}.katex .x-arrow-pad{padding:0 .5em}.katex .cd-arrow-pad{padding:0 .55556em 0 .27778em}.katex .mover,.katex .munder,.katex .x-arrow{text-align:center}.katex .boxpad{padding:0 .3em}.katex .fbox,.katex .fcolorbox{border:.04em solid;box-sizing:border-box}.katex .cancel-pad{padding:0 .2em}.katex .cancel-lap{margin-left:-.2em;margin-right:-.2em}.katex .sout{border-bottom-style:solid;border-bottom-width:.08em}.katex .angl{border-right:.049em solid;border-top:.049em solid;box-sizing:border-box;margin-right:.03889em}.katex .anglpad{padding:0 .03889em}.katex .eqn-num:before{content:"(" counter(katexEqnNo) ")";counter-increment:katexEqnNo}.katex .mml-eqn-num:before{content:"(" counter(mmlEqnNo) ")";counter-increment:mmlEqnNo}.katex .mtr-glue{width:50%}.katex .cd-vert-arrow{display:inline-block;position:relative}.katex .cd-label-left{display:inline-block;position:absolute;right:calc(50% + .3em);text-align:left}.katex .cd-label-right{display:inline-block;left:calc(50% + .3em);position:absolute;text-align:right}.katex-display{display:block;margin:1em 0;text-align:center}.katex-display>.katex{display:block;text-align:center;white-space:nowrap}.katex-display>.katex>.katex-html{display:block;position:relative}.katex-display>.katex>.katex-html>.tag{position:absolute;right:0}.katex-display.leqno>.katex>.katex-html>.tag{left:0;right:auto}.katex-display.fleqn>.katex{padding-left:2em;text-align:left}body{counter-reset:katexEqnNo mmlEqnNo}

/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[3]!./src/styles/markdown-minimal.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/* ===== RAFTHOR MARKDOWN - DESIGN MINIMALISTA ===== */

.rafthor-markdown {
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.7;
  max-width: none;
}

/* ===== WEB SEARCH INDICATOR ===== */
.web-search-indicator {
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(147, 51, 234, 0.06));
  border: 1px solid rgba(59, 130, 246, 0.15);
  border-radius: 12px;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}

.search-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}

.search-icon {
  height: 1rem;
  width: 1rem;
}

.source-count {
  border-radius: 9999px;
  background-color: rgb(59 130 246 / 0.2);
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
  min-width: 20px;
  text-align: center;
}

.source-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.source-tag {
  border-radius: 0.375rem;
  background-color: rgb(51 65 85 / 0.5);
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.source-tag.more {
  border-color: rgb(59 130 246 / 0.2);
  background-color: rgb(59 130 246 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}

/* ===== HEADINGS ===== */
.markdown-heading {
  margin-bottom: 1.5rem;
  margin-top: 2rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.markdown-heading:first-child {
  margin-top: 0px;
}
.markdown-heading {
  letter-spacing: -0.025em;
  position: relative;
  transition: all 0.3s ease;
}

.markdown-heading:hover {
  transform: translateX(4px);
}

.markdown-heading .heading-content {
  position: relative;
}

.markdown-heading.h1 {
  margin-bottom: 2rem;
  font-size: 2.25rem;
  line-height: 2.5rem;
  background: linear-gradient(135deg, #3b82f6, #60a5fa, #93c5fd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  padding-bottom: 16px;
}

.markdown-heading.h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 4px;
  background: linear-gradient(135deg, #3b82f6, #60a5fa, #93c5fd);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

.markdown-heading.h2 {
  margin-bottom: 1.25rem;
  font-size: 1.5rem;
  line-height: 2rem;
  color: #60a5fa;
  padding-left: 20px;
}

.markdown-heading.h2::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 5px;
  background: linear-gradient(to bottom, #3b82f6, #60a5fa);
  border-radius: 3px;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

.markdown-heading.h3 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  color: #93c5fd;
}

.markdown-heading.h3::before {
  content: '▶';
  position: absolute;
  left: -20px;
  color: #3b82f6;
  font-size: 0.8em;
}

.markdown-heading.h4 {
  margin-bottom: 0.75rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  color: #bfdbfe;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  padding-bottom: 4px;
}

.markdown-heading.h5 {
  margin-bottom: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  color: #dbeafe;
  font-weight: 600;
}

.markdown-heading.h6 {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #e0e7ff;
  opacity: 0.9;
}

/* ===== PARAGRAPHS ===== */
.markdown-paragraph {
  margin-bottom: 1rem;
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
  line-height: 1.75;
}

.markdown-paragraph:last-child {
  margin-bottom: 0px;
}

/* ===== LINKS ===== */
.markdown-link {
  position: relative;
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
  text-decoration-line: none;
  transition: all 0.2s ease;
}

.markdown-link:hover {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}

.markdown-link::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 0;
  height: 1px;
  background: currentColor;
  transition: width 0.2s ease;
}

.markdown-link:hover::after {
  width: 100%;
}

/* ===== CODE BLOCKS ===== */
.code-block-container {
  margin-bottom: 1.5rem;
  overflow: hidden;
  border-radius: 0.75rem;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.9));
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.code-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  background: rgba(15, 23, 42, 0.8);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.copy-button {
  border-radius: 0.375rem;
  padding: 0.375rem;
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}

.copy-button:hover {
  background-color: rgb(51 65 85 / 0.5);
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
}

.copy-button {
  transition: all 0.2s ease;
}

.code-content {
  overflow-x: auto;
  padding: 1rem;
  font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.code-content code {
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
  background: none !important;
}

.inline-code {
  border-radius: 0.375rem;
  background-color: rgb(51 65 85 / 0.5);
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
  font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

/* ===== BLOCKQUOTES ===== */
.markdown-blockquote {
  position: relative;
  margin-top: 2rem;
  margin-bottom: 2rem;
  padding: 20px 24px 20px 60px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(139, 92, 246, 0.06));
  border: 1px solid rgba(59, 130, 246, 0.15);
  border-radius: 12px;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.markdown-blockquote:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.12), rgba(139, 92, 246, 0.08));
  border-color: rgba(59, 130, 246, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.markdown-blockquote::before {
  content: '"';
  position: absolute;
  left: 20px;
  top: 15px;
  font-size: 3em;
  color: #3b82f6;
  font-family: Georgia, serif;
  line-height: 1;
  opacity: 0.6;
}

.markdown-blockquote::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
  border-radius: 0 2px 2px 0;
}

.quote-content {
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
  font-size: 1.1em;
  line-height: 1.7;
  font-style: italic;
  position: relative;
  z-index: 1;
}

/* ===== LISTS ===== */
.markdown-list {
  margin-bottom: 1.5rem;
}
.markdown-list > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.markdown-list.unordered {
  list-style-type: none;
}

.markdown-list.ordered {
  list-style-type: none;
  counter-reset: list-counter;
}

.list-item {
  position: relative;
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
  line-height: 1.7;
  padding: 8px 0 8px 40px;
  transition: all 0.2s ease;
}

.list-item:hover {
  --tw-text-opacity: 1;
  color: rgb(241 245 249 / var(--tw-text-opacity, 1));
  transform: translateX(4px);
}

/* ===== LISTAS NÃO ORDENADAS - DESIGN ELEGANTE ===== */
.unordered .list-item::before {
  content: '';
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  border: 2px solid rgba(59, 130, 246, 0.2);
}

.unordered .list-item:hover::before {
  background: linear-gradient(135deg, #60a5fa, #93c5fd);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  transform: translateY(-50%) scale(1.1);
}

/* Sublistas não ordenadas - diamantes */
.unordered .list-item .markdown-list.unordered .list-item::before {
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #1e3a8a, #3b82f6);
  border-radius: 2px;
  transform: translateY(-50%) rotate(45deg);
  left: 16px;
}

/* ===== LISTAS ORDENADAS - BADGES NUMERADOS ===== */
.ordered .list-item {
  counter-increment: list-counter;
  padding-left: 50px;
}

.ordered .list-item::before {
  content: counter(list-counter);
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #1e3a8a, #3b82f6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 10px rgba(30, 58, 138, 0.4);
  border: 2px solid rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
}

.ordered .list-item:hover::before {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  box-shadow: 0 5px 15px rgba(59, 130, 246, 0.5);
  transform: translateY(-50%) scale(1.1);
}

/* Sublistas ordenadas - quadrados menores */
.ordered .list-item .markdown-list.ordered .list-item::before {
  width: 22px;
  height: 22px;
  background: linear-gradient(135deg, #0d1c4a, #1b2e76);
  border-radius: 6px;
  font-size: 11px;
  left: 12px;
}

/* ===== TABLES ===== */
.table-wrapper {
  margin-top: 2rem;
  margin-bottom: 2rem;
  overflow-x: auto;
  border-radius: 0.75rem;
  border: 1px solid rgba(59, 130, 246, 0.2);
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8), rgba(30, 41, 59, 0.6));
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
}

.markdown-table {
  width: 100%;
  border-collapse: collapse;
  background: transparent;
}

.table-header {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  text-align: left;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(30, 58, 138, 0.4));
  border-bottom: 2px solid rgba(59, 130, 246, 0.3);
  position: relative;
}

.table-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, #3b82f6, transparent);
}

.table-cell {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.2s ease;
}

.markdown-table tr:hover .table-cell {
  background: rgba(59, 130, 246, 0.05);
  color: #f1f5f9;
}

.markdown-table tr:last-child .table-cell {
  border-bottom: none;
}

/* ===== DIVIDER ===== */
.markdown-divider {
  margin-top: 3rem;
  margin-bottom: 3rem;
  border-width: 0px;
  height: 3px;
  background: linear-gradient(to right,
    transparent,
    rgba(59, 130, 246, 0.3),
    rgba(139, 92, 246, 0.3),
    rgba(59, 130, 246, 0.3),
    transparent
  );
  border-radius: 2px;
  position: relative;
}

.markdown-divider::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 50%;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 640px) {
  .rafthor-markdown {
    font-size: 15px;
  }
  
  .markdown-heading.h1 {
    font-size: 1.5rem;
    line-height: 2rem;
  }
  
  .markdown-heading.h2 {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
  
  .code-header {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
  
  .code-content {
    padding: 0.75rem;
    font-size: 13px;
  }
  
  .web-search-indicator {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .source-list {
    width: 100%;
  }
}

/* ===== STREAMING MODE ===== */
.rafthor-markdown.streaming * {
  animation: none !important;
  transition: none !important;
}

/* ===== FÓRMULAS MATEMÁTICAS (KATEX) ===== */
/* Os estilos LaTeX estão agora em markdown-latex.css */

/* Fórmulas inline - Estilo minimalista */
.rafthor-markdown p .katex {
  color: #e2e8f0 !important;
  background: rgba(148, 163, 184, 0.08);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 1.05em !important;
  border: 1px solid rgba(148, 163, 184, 0.05);
  transition: all 0.2s ease;
}

.rafthor-markdown p .katex:hover {
  background: rgba(148, 163, 184, 0.12);
  border-color: rgba(148, 163, 184, 0.15);
}

/* Cores consistentes para todos os elementos matemáticos */
.rafthor-markdown .katex .mord,
.rafthor-markdown .katex .mbin,
.rafthor-markdown .katex .mrel,
.rafthor-markdown .katex .mopen,
.rafthor-markdown .katex .mclose,
.rafthor-markdown .katex .mpunct,
.rafthor-markdown .katex .mop,
.rafthor-markdown .katex .mspace,
.rafthor-markdown .katex .base {
  color: inherit !important;
}

/* Linhas de fração e raiz com cor consistente */
.rafthor-markdown .katex .mfrac .frac-line {
  border-bottom-color: currentColor !important;
  opacity: 0.8;
}

.rafthor-markdown .katex .sqrt .sqrt-line {
  border-top-color: currentColor !important;
  opacity: 0.8;
}

/* Responsividade */
@media (max-width: 640px) {
  .rafthor-markdown .katex-display {
    padding: 20px 16px;
    margin: 20px 0;
  }

  .rafthor-markdown .katex-display .katex {
    font-size: 1.1em !important;
  }

  .rafthor-markdown p .katex {
    font-size: 1em !important;
  }
}

/* ===== SYNTAX HIGHLIGHTING OVERRIDES ===== */
.code-content .hljs-keyword { color: #c792ea; }
.code-content .hljs-string { color: #c3e88d; }
.code-content .hljs-number { color: #f78c6c; }
.code-content .hljs-comment { color: #546e7a; font-style: italic; }
.code-content .hljs-function { color: #82aaff; }
.code-content .hljs-variable { color: #eeffff; }
.code-content .hljs-title { color: #ffcb6b; }
.code-content .hljs-attr { color: #c792ea; }
.code-content .hljs-built_in { color: #ffcb6b; }
.code-content .hljs-type { color: #c792ea; }
.code-content .hljs-literal { color: #ff5370; }
.code-content .hljs-meta { color: #546e7a; }
.code-content .hljs-tag { color: #f07178; }
.code-content .hljs-name { color: #f07178; }
.code-content .hljs-selector-id,
.code-content .hljs-selector-class { color: #ffcb6b; }

/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[3]!./src/styles/markdown-latex.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/* ===== RAFTHOR LATEX - TEMA AZUL ELEGANTE ===== */

/* ===== FÓRMULAS INLINE ===== */
.markdown-content .katex {
  font-size: 1.15em !important;
  color: #f1f5f9 !important;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(30, 58, 138, 0.1)) !important;
  padding: 4px 8px !important;
  border-radius: 6px !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1) !important;
  transition: all 0.2s ease !important;
  display: inline-block !important;
  margin: 0 2px !important;
}

.markdown-content .katex:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(30, 58, 138, 0.15)) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2) !important;
  transform: translateY(-1px) !important;
}

/* ===== FÓRMULAS EM BLOCO - DESIGN ELEGANTE ===== */
.markdown-content .katex-display,
.rafthor-markdown .katex-display {
  margin: 2.5rem auto !important;
  padding: 2rem !important;
  background: linear-gradient(135deg,
    rgba(13, 28, 74, 0.8),
    rgba(27, 46, 118, 0.6),
    rgba(59, 130, 246, 0.1)
  ) !important;
  border: 2px solid rgba(59, 130, 246, 0.3) !important;
  border-radius: 16px !important;
  overflow-x: auto !important;
  position: relative !important;
  -webkit-backdrop-filter: blur(12px) !important;
          backdrop-filter: blur(12px) !important;
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  max-width: 90% !important;
  text-align: center !important;
  transition: all 0.3s ease !important;
}

.markdown-content .katex-display:hover {
  border-color: rgba(59, 130, 246, 0.5) !important;
  box-shadow:
    0 12px 40px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  transform: translateY(-4px) !important;
}

/* Efeito decorativo nas bordas */
.markdown-content .katex-display::before {
  content: '' !important;
  position: absolute !important;
  top: -2px !important;
  left: -2px !important;
  right: -2px !important;
  bottom: -2px !important;
  background: linear-gradient(45deg, #3b82f6, #60a5fa, #93c5fd, #3b82f6) !important;
  border-radius: 18px !important;
  z-index: -1 !important;
  opacity: 0.6 !important;
  animation: borderGlow 3s ease-in-out infinite !important;
}

@keyframes borderGlow {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 0.8; }
}

/* ===== CORREÇÃO PARA RENDERIZAÇÃO ===== */
.markdown-content .katex-display .katex,
.rafthor-markdown .katex-display .katex {
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  color: #f8fafc !important;
  font-size: 1.2em !important;
}

/* ===== ELEMENTOS MATEMÁTICOS - PALETA AZUL RAFTHOR ===== */

/* Base das fórmulas */
.markdown-content .katex .base {
  color: #f8fafc !important;
}

/* Operadores matemáticos (+, -, =, <, >, etc.) */
.markdown-content .katex .mbin,
.markdown-content .katex .mrel {
  color: #60a5fa !important;
  font-weight: 600 !important;
}

/* Números e variáveis */
.markdown-content .katex .mord {
  color: #e2e8f0 !important;
}

/* Funções matemáticas (sin, cos, log, etc.) */
.markdown-content .katex .mop {
  color: #93c5fd !important;
  font-weight: 600 !important;
}

/* Parênteses e delimitadores */
.markdown-content .katex .mopen,
.markdown-content .katex .mclose {
  color: #3b82f6 !important;
  font-weight: 700 !important;
}

/* Sobrescritos e subscritos */
.markdown-content .katex .msupsub {
  color: #bfdbfe !important;
}

/* ===== CENTRALIZAÇÃO AUTOMÁTICA ===== */
.markdown-content .katex-display > .katex,
.rafthor-markdown .katex-display > .katex {
  text-align: center !important;
  display: block !important;
  margin: 0 auto !important;
}

/* Container principal centralizado */
.markdown-content .katex-display,
.rafthor-markdown .katex-display {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
}

/* ===== ELEMENTOS ESPECIAIS - TEMA AZUL ===== */

/* HTML renderizado */
.markdown-content .katex .katex-html {
  color: #f1f5f9 !important;
}

/* Frações - linha divisória azul */
.markdown-content .katex .frac-line {
  border-bottom-color: #3b82f6 !important;
  border-bottom-width: 1.5px !important;
}

/* Raízes quadradas */
.markdown-content .katex .sqrt > .root {
  color: #60a5fa !important;
  font-weight: 600 !important;
}

/* Integrais e somatórios */
.markdown-content .katex .op-symbol {
  color: #3b82f6 !important;
  font-weight: 700 !important;
  font-size: 1.1em !important;
}

/* Texto em fórmulas */
.markdown-content .katex .text {
  color: #cbd5e1 !important;
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto !important;
}

/* Acentos e decorações */
.markdown-content .katex .accent > .accent-body {
  color: #60a5fa !important;
}

/* Vetores */
.markdown-content .katex .overrightarrow > .arrow {
  color: #3b82f6 !important;
}

/* ===== MATRIZES E ARRAYS ===== */
.markdown-content .katex .arraycolsep,
.rafthor-markdown .katex .arraycolsep {
  width: 0.6em !important;
}

.markdown-content .katex .array,
.markdown-content .katex .matrix,
.rafthor-markdown .katex .array,
.rafthor-markdown .katex .matrix {
  color: #f1f5f9 !important;
}

/* Correção para arrays em sistemas */
.markdown-content .katex .array .arraycolsep,
.rafthor-markdown .katex .array .arraycolsep {
  width: 0.5em !important;
}

/* Linhas de arrays */
.markdown-content .katex .arrayline,
.rafthor-markdown .katex .arrayline {
  color: #f1f5f9 !important;
}

/* Delimitadores de matrizes */
.markdown-content .katex .delimsizing,
.markdown-content .katex .delim-size1,
.markdown-content .katex .delim-size2,
.markdown-content .katex .delim-size3,
.markdown-content .katex .delim-size4 {
  color: #3b82f6 !important;
}

/* ===== SISTEMAS DE EQUAÇÕES ===== */
.markdown-content .katex .cases {
  color: #f1f5f9 !important;
}

.markdown-content .katex .cases > .arraycolsep {
  width: 0.3em !important;
}

/* Chaves dos sistemas */
.markdown-content .katex .cases .delimsizing {
  color: #60a5fa !important;
  font-weight: 600 !important;
}

/* ===== DETERMINANTES ===== */
.markdown-content .katex .vmatrix {
  color: #f1f5f9 !important;
}

/* Barras dos determinantes */
.markdown-content .katex .vmatrix .delimsizing {
  color: #3b82f6 !important;
  font-weight: 700 !important;
}

/* ===== LIMITES E OPERADORES ===== */
.markdown-content .katex .op-limits > .vlist-t,
.rafthor-markdown .katex .op-limits > .vlist-t {
  color: #93c5fd !important;
}

/* ===== EQUAÇÕES ALINHADAS ===== */
.markdown-content .katex .align,
.rafthor-markdown .katex .align {
  color: #f1f5f9 !important;
}

.markdown-content .katex .align .arraycolsep,
.rafthor-markdown .katex .align .arraycolsep {
  width: 0.5em !important;
}

/* Símbolos de alinhamento */
.markdown-content .katex .align .mrel,
.rafthor-markdown .katex .align .mrel {
  color: #60a5fa !important;
}

/* ===== RESPONSIVIDADE E OTIMIZAÇÕES ===== */

/* Dispositivos móveis */
@media (max-width: 768px) {
  .markdown-content .katex-display {
    font-size: 0.85em !important;
    padding: 1.5rem 1rem !important;
    margin: 2rem auto !important;
    max-width: 95% !important;
    border-radius: 12px !important;
  }

  .markdown-content .katex {
    font-size: 1em !important;
    padding: 3px 6px !important;
  }

  .markdown-content .katex-display::before {
    border-radius: 14px !important;
  }
}

/* Tablets */
@media (max-width: 1024px) {
  .markdown-content .katex-display {
    max-width: 85% !important;
    padding: 1.75rem !important;
  }
}

/* ===== FONTES E TIPOGRAFIA ===== */
.markdown-content .katex .mathdefault {
  font-family: KaTeX_Main, "Times New Roman", serif !important;
}

.markdown-content .katex .katex-mathml {
  color: #f8fafc !important;
}

/* ===== ESPAÇAMENTO E ALINHAMENTO ===== */
.markdown-content .katex .mspace {
  margin: 0 0.25em !important;
}

/* ===== ACESSIBILIDADE ===== */
.markdown-content .katex[title] {
  cursor: help !important;
  transition: all 0.2s ease !important;
}

.markdown-content .katex[title]:hover {
  opacity: 0.8 !important;
}

/* ===== OTIMIZAÇÕES PARA ALTA RESOLUÇÃO ===== */
@media (min-resolution: 192dpi) {
  .markdown-content .katex {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }
}

/* ===== ANIMAÇÕES SUAVES ===== */
.markdown-content .katex * {
  transition: color 0.2s ease !important;
}

/* ===== SCROLL HORIZONTAL ELEGANTE ===== */
.markdown-content .katex-display::-webkit-scrollbar {
  height: 8px !important;
}

.markdown-content .katex-display::-webkit-scrollbar-track {
  background: rgba(59, 130, 246, 0.1) !important;
  border-radius: 4px !important;
}

.markdown-content .katex-display::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #60a5fa) !important;
  border-radius: 4px !important;
}

.markdown-content .katex-display::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #60a5fa, #93c5fd) !important;
}

