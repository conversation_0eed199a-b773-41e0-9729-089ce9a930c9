'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { ChatSession, SessionNavigation as ISessionNavigation } from '@/lib/types/chatSessions';

interface SessionNavigationProps {
  navigation: ISessionNavigation | null;
  currentSession: ChatSession | null;
  totalSessions: number;
  isLoading: boolean;
  onNavigateToPrevious: () => Promise<void>;
  onNavigateToNext: () => Promise<void>;
  onNavigateToSession: (sessionId: string) => Promise<void>;
  onLoadPreviousSession: () => Promise<void>;
}

export default function SessionNavigation({
  navigation,
  currentSession,
  totalSessions,
  isLoading,
  onNavigateToPrevious,
  onNavigateToNext,
  onNavigateToSession,
  onLoadPreviousSession
}: SessionNavigationProps) {
  // Se não há sessões ou apenas uma sessão, não mostra navegação
  if (!navigation || totalSessions <= 1) {
    return null;
  }

  const { currentIndex, hasPrevious, hasNext } = navigation;

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gradient-to-r from-blue-900/30 via-purple-900/30 to-blue-900/30 backdrop-blur-sm border border-white/10 rounded-lg p-4 mb-4"
    >
      <div className="flex items-center justify-between">
        {/* Informações da sessão atual */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
            <span className="text-white/80 text-sm font-medium">
              Sessão {currentIndex + 1} de {totalSessions}
            </span>
          </div>
          
          {currentSession && (
            <div className="text-white/60 text-xs">
              {currentSession.wordCount.toLocaleString()} palavras
            </div>
          )}
        </div>

        {/* Controles de navegação */}
        <div className="flex items-center space-x-2">
          {/* Botão para carregar sessão anterior */}
          {hasPrevious && (
            <button
              onClick={onLoadPreviousSession}
              disabled={isLoading}
              className="px-3 py-1.5 text-xs text-blue-300 hover:text-white bg-blue-600/20 hover:bg-blue-600/40 
                       border border-blue-500/30 hover:border-blue-400/50 rounded-md transition-all duration-200
                       disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
              title="Carregar sessão anterior"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
              <span>Carregar</span>
            </button>
          )}

          {/* Botão anterior */}
          <button
            onClick={onNavigateToPrevious}
            disabled={!hasPrevious || isLoading}
            className="p-2 text-white/60 hover:text-white bg-white/5 hover:bg-white/10 
                     border border-white/10 hover:border-white/20 rounded-md transition-all duration-200
                     disabled:opacity-30 disabled:cursor-not-allowed"
            title="Sessão anterior"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          {/* Indicador de progresso */}
          <div className="flex items-center space-x-1 px-3">
            {Array.from({ length: Math.min(totalSessions, 5) }, (_, index) => {
              const sessionIndex = totalSessions <= 5 
                ? index 
                : Math.max(0, currentIndex - 2) + index;
              
              if (sessionIndex >= totalSessions) return null;
              
              const isActive = sessionIndex === currentIndex;
              const isAdjacent = Math.abs(sessionIndex - currentIndex) === 1;
              
              return (
                <motion.div
                  key={sessionIndex}
                  className={`w-2 h-2 rounded-full cursor-pointer transition-all duration-200 ${
                    isActive 
                      ? 'bg-blue-400 scale-125' 
                      : isAdjacent 
                        ? 'bg-white/40 hover:bg-white/60' 
                        : 'bg-white/20 hover:bg-white/40'
                  }`}
                  onClick={() => {
                    // Implementar navegação direta para sessão específica
                    // onNavigateToSession(sessionId);
                  }}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                />
              );
            })}
          </div>

          {/* Botão próximo */}
          <button
            onClick={onNavigateToNext}
            disabled={!hasNext || isLoading}
            className="p-2 text-white/60 hover:text-white bg-white/5 hover:bg-white/10 
                     border border-white/10 hover:border-white/20 rounded-md transition-all duration-200
                     disabled:opacity-30 disabled:cursor-not-allowed"
            title="Próxima sessão"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>

      {/* Barra de progresso das sessões */}
      <div className="mt-3">
        <div className="w-full bg-white/10 rounded-full h-1">
          <motion.div
            className="bg-gradient-to-r from-blue-400 to-purple-400 h-1 rounded-full"
            initial={{ width: 0 }}
            animate={{ 
              width: `${((currentIndex + 1) / totalSessions) * 100}%` 
            }}
            transition={{ duration: 0.3 }}
          />
        </div>
        
        <div className="flex justify-between mt-1 text-xs text-white/40">
          <span>Início</span>
          <span>Sessão atual</span>
          <span>Mais recente</span>
        </div>
      </div>

      {/* Indicador de carregamento */}
      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-3 flex items-center justify-center space-x-2 text-blue-300 text-xs"
          >
            <div className="w-3 h-3 border border-blue-300 border-t-transparent rounded-full animate-spin"></div>
            <span>Carregando sessão...</span>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
