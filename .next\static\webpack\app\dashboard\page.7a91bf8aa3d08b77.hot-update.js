"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useAdvancedSearch.ts":
/*!****************************************!*\
  !*** ./src/hooks/useAdvancedSearch.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAdvancedSearch: function() { return /* binding */ useAdvancedSearch; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useAdvancedSearch(models) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { debounceMs = 300, enableSuggestions = true, cacheResults = true, fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n    const [searchTerm, setSearchTermState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [hasSearched, setHasSearched] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [debounceTimer, setDebounceTimer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Cache para resultados de busca\n    const [searchCache] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Map());\n    // Função de busca principal\n    const performSearch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (term)=>{\n        if (!term.trim()) {\n            setSearchResults([]);\n            setHasSearched(false);\n            return;\n        }\n        setIsSearching(true);\n        try {\n            // Verificar cache se habilitado\n            if (cacheResults && searchCache.has(term)) {\n                setSearchResults(searchCache.get(term));\n                setHasSearched(true);\n                setIsSearching(false);\n                return;\n            }\n            // Realizar busca\n            const results = searchModels(models, term, {\n                fuzzyThreshold,\n                maxResults,\n                boostFavorites\n            });\n            // Salvar no cache se habilitado\n            if (cacheResults) {\n                searchCache.set(term, results);\n            }\n            setSearchResults(results);\n            setHasSearched(true);\n            // Rastrear termo de busca se analytics estiver habilitado\n            if (trackAnalytics && userId) {\n                await searchAnalyticsService.trackSearchTerm(userId, term);\n            }\n        } catch (error) {\n            console.error(\"Error performing search:\", error);\n            setSearchResults([]);\n        } finally{\n            setIsSearching(false);\n        }\n    }, [\n        models,\n        fuzzyThreshold,\n        maxResults,\n        boostFavorites,\n        cacheResults,\n        searchCache,\n        trackAnalytics,\n        userId\n    ]);\n    // Função para definir termo de busca com debounce\n    const setSearchTerm = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((term)=>{\n        setSearchTermState(term);\n        // Limpar timer anterior\n        if (debounceTimer) {\n            clearTimeout(debounceTimer);\n        }\n        // Configurar novo timer\n        const timer = setTimeout(()=>{\n            performSearch(term);\n        }, debounceMs);\n        setDebounceTimer(timer);\n    }, [\n        debounceTimer,\n        debounceMs,\n        performSearch\n    ]);\n    // Carregar sugestões quando o termo muda\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (enableSuggestions && searchTerm.length > 0 && userId) {\n            searchAnalyticsService.getSearchSuggestions(userId, searchTerm, 5).then(setSuggestions).catch((error)=>{\n                console.error(\"Error loading suggestions:\", error);\n                setSuggestions([]);\n            });\n        } else {\n            setSuggestions([]);\n        }\n    }, [\n        searchTerm,\n        enableSuggestions,\n        userId\n    ]);\n    // Limpar busca\n    const clearSearch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setSearchTermState(\"\");\n        setSearchResults([]);\n        setHasSearched(false);\n        setSuggestions([]);\n        if (debounceTimer) {\n            clearTimeout(debounceTimer);\n            setDebounceTimer(null);\n        }\n    }, [\n        debounceTimer\n    ]);\n    // Rastrear seleção de modelo\n    const trackModelSelection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (modelId)=>{\n        if (trackAnalytics && userId) {\n            const model = models.find((m)=>m.id === modelId);\n            if (model) {\n                await searchAnalyticsService.trackModelSelection(userId, modelId, model.name, hasSearched ? searchTerm : undefined);\n            }\n        }\n    }, [\n        trackAnalytics,\n        userId,\n        models,\n        hasSearched,\n        searchTerm\n    ]);\n    // Limpar timer ao desmontar\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            if (debounceTimer) {\n                clearTimeout(debounceTimer);\n            }\n        };\n    }, [\n        debounceTimer\n    ]);\n    return {\n        searchTerm,\n        setSearchTerm,\n        searchResults,\n        suggestions,\n        isSearching,\n        hasSearched,\n        clearSearch,\n        trackModelSelection\n    };\n}\n// Função de busca com fuzzy matching\nfunction searchModels(models, searchTerm) {\n    let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n    const { fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n    if (!searchTerm.trim()) {\n        return [];\n    }\n    const term = searchTerm.toLowerCase();\n    const results = [];\n    for (const model of models){\n        let score = 0;\n        const matchedFields = [];\n        let highlightedName = model.name;\n        let highlightedDescription = model.description || \"\";\n        // Busca no nome (peso maior)\n        if (model.name.toLowerCase().includes(term)) {\n            score += 10;\n            matchedFields.push(\"name\");\n            highlightedName = highlightText(model.name, term);\n        }\n        // Busca no ID (peso médio)\n        if (model.id.toLowerCase().includes(term)) {\n            score += 7;\n            matchedFields.push(\"id\");\n        }\n        // Busca na descrição (peso menor)\n        if (model.description && model.description.toLowerCase().includes(term)) {\n            score += 3;\n            matchedFields.push(\"description\");\n            highlightedDescription = highlightText(model.description, term);\n        }\n        // Boost para favoritos\n        if (boostFavorites && model.isFavorite) {\n            score *= 1.5;\n        }\n        // Boost para modelos gratuitos se buscar por \"free\" ou \"grátis\"\n        if ((term.includes(\"free\") || term.includes(\"gr\\xe1tis\")) && isModelFree(model)) {\n            score += 5;\n        }\n        // Boost para modelos caros se buscar por \"expensive\" ou \"caro\"\n        if ((term.includes(\"expensive\") || term.includes(\"caro\")) && getTotalPrice(model) > 0.00002) {\n            score += 5;\n        }\n        if (score > 0) {\n            results.push({\n                model,\n                score,\n                matchedFields,\n                highlightedName,\n                highlightedDescription\n            });\n        }\n    }\n    // Ordenar por score e limitar resultados\n    return results.sort((a, b)=>b.score - a.score).slice(0, maxResults);\n}\n// Função auxiliar para destacar texto\nfunction highlightText(text, term) {\n    const regex = new RegExp(\"(\".concat(term, \")\"), \"gi\");\n    return text.replace(regex, '<mark class=\"bg-yellow-300 text-black px-1 rounded\">$1</mark>');\n}\n// Função auxiliar para verificar se modelo é gratuito\nfunction isModelFree(model) {\n    const promptPrice = parseFloat(model.pricing.prompt);\n    const completionPrice = parseFloat(model.pricing.completion);\n    return promptPrice === 0 && completionPrice === 0;\n}\n// Função auxiliar para obter preço total\nfunction getTotalPrice(model) {\n    const promptPrice = parseFloat(model.pricing.prompt);\n    const completionPrice = parseFloat(model.pricing.completion);\n    return promptPrice + completionPrice;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\n"));

/***/ })

});