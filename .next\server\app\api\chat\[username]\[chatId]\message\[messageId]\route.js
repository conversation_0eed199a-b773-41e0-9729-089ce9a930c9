"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/[username]/[chatId]/message/[messageId]/route";
exports.ids = ["app/api/chat/[username]/[chatId]/message/[messageId]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Fmessage%2F%5BmessageId%5D%2Froute&page=%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Fmessage%2F%5BmessageId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Fmessage%2F%5BmessageId%5D%2Froute.ts&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Fmessage%2F%5BmessageId%5D%2Froute&page=%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Fmessage%2F%5BmessageId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Fmessage%2F%5BmessageId%5D%2Froute.ts&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/node-polyfill-headers */ \"(rsc)/./node_modules/next/dist/server/node-polyfill-headers.js\");\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var C_Users_Henri_Desktop_Rafthor_RafthorIA_src_app_api_chat_username_chatId_message_messageId_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/[username]/[chatId]/message/[messageId]/route.ts */ \"(rsc)/./src/app/api/chat/[username]/[chatId]/message/[messageId]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/[username]/[chatId]/message/[messageId]/route\",\n        pathname: \"/api/chat/[username]/[chatId]/message/[messageId]\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/[username]/[chatId]/message/[messageId]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\api\\\\chat\\\\[username]\\\\[chatId]\\\\message\\\\[messageId]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Henri_Desktop_Rafthor_RafthorIA_src_app_api_chat_username_chatId_message_messageId_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/chat/[username]/[chatId]/message/[messageId]/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Fmessage%2F%5BmessageId%5D%2Froute&page=%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Fmessage%2F%5BmessageId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Fmessage%2F%5BmessageId%5D%2Froute.ts&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/[username]/[chatId]/message/[messageId]/route.ts":
/*!***************************************************************************!*\
  !*** ./src/app/api/chat/[username]/[chatId]/message/[messageId]/route.ts ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/storage */ \"(rsc)/./node_modules/firebase/storage/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/firebase */ \"(rsc)/./src/lib/firebase.ts\");\n\n\n\n// DELETE - Deletar mensagem\nasync function DELETE(request, { params }) {\n    const { username, chatId, messageId } = params;\n    if (!username || !chatId || !messageId) {\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Username, chatId e messageId s\\xe3o obrigat\\xf3rios\"\n        }, {\n            status: 400\n        });\n    }\n    try {\n        // Referência para o arquivo chat.json no Firebase Storage\n        const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_1__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.storage, `usuarios/${username}/conversas/${chatId}/chat.json`);\n        // Obter URL de download\n        const downloadUrl = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_1__.getDownloadURL)(chatJsonRef);\n        // Fazer fetch do arquivo\n        const response = await fetch(downloadUrl);\n        if (!response.ok) {\n            throw new Error(`Erro ao buscar arquivo: ${response.statusText}`);\n        }\n        const chatData = await response.json();\n        // Filtrar mensagens removendo a mensagem com o ID especificado\n        const updatedMessages = chatData.messages.filter((msg)=>msg.id !== messageId);\n        // Atualizar dados do chat\n        const updatedChatData = {\n            ...chatData,\n            messages: updatedMessages,\n            lastUpdated: new Date().toISOString()\n        };\n        // Salvar arquivo atualizado\n        const chatJsonBlob = new Blob([\n            JSON.stringify(updatedChatData, null, 2)\n        ], {\n            type: \"application/json\"\n        });\n        await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_1__.uploadBytes)(chatJsonRef, chatJsonBlob);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Mensagem deletada com sucesso\"\n        }, {\n            headers: {\n                \"Access-Control-Allow-Origin\": \"*\",\n                \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n                \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n            }\n        });\n    } catch (error) {\n        console.error(\"Erro ao deletar mensagem:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Erro interno do servidor\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - Atualizar mensagem\nasync function PUT(request, { params }) {\n    const { username, chatId, messageId } = params;\n    if (!username || !chatId || !messageId) {\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Username, chatId e messageId s\\xe3o obrigat\\xf3rios\"\n        }, {\n            status: 400\n        });\n    }\n    try {\n        const body = await request.json();\n        const { content } = body;\n        if (!content || typeof content !== \"string\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Conte\\xfado da mensagem \\xe9 obrigat\\xf3rio\"\n            }, {\n                status: 400\n            });\n        }\n        // Referência para o arquivo chat.json no Firebase Storage\n        const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_1__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.storage, `usuarios/${username}/conversas/${chatId}/chat.json`);\n        // Obter URL de download\n        const downloadUrl = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_1__.getDownloadURL)(chatJsonRef);\n        // Fazer fetch do arquivo\n        const response = await fetch(downloadUrl);\n        if (!response.ok) {\n            throw new Error(`Erro ao buscar arquivo: ${response.statusText}`);\n        }\n        const chatData = await response.json();\n        // Atualizar a mensagem específica\n        const updatedMessages = chatData.messages.map((msg)=>{\n            if (msg.id === messageId) {\n                return {\n                    ...msg,\n                    content: content.trim(),\n                    timestamp: new Date().toISOString()\n                };\n            }\n            return msg;\n        });\n        // Atualizar dados do chat\n        const updatedChatData = {\n            ...chatData,\n            messages: updatedMessages,\n            lastUpdated: new Date().toISOString()\n        };\n        // Salvar arquivo atualizado\n        const chatJsonBlob = new Blob([\n            JSON.stringify(updatedChatData, null, 2)\n        ], {\n            type: \"application/json\"\n        });\n        await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_1__.uploadBytes)(chatJsonRef, chatJsonBlob);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Mensagem atualizada com sucesso\"\n        }, {\n            headers: {\n                \"Access-Control-Allow-Origin\": \"*\",\n                \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n                \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n            }\n        });\n    } catch (error) {\n        console.error(\"Erro ao atualizar mensagem:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Erro interno do servidor\"\n        }, {\n            status: 500\n        });\n    }\n}\n// OPTIONS - Para CORS\nasync function OPTIONS() {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({}, {\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/[username]/[chatId]/message/[messageId]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   functions: () => (/* binding */ functions),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(rsc)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(rsc)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(rsc)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(rsc)/./node_modules/firebase/storage/dist/index.mjs\");\n/* harmony import */ var firebase_functions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/functions */ \"(rsc)/./node_modules/firebase/functions/dist/index.mjs\");\n\n\n\n\n\n// Configuração do Firebase - novo app criado - ATUALIZADO\nconst firebaseConfig = {\n    apiKey: \"AIzaSyA4ojPmlKBkDDl2hcfNPDXG23tEgolgCv8\",\n    authDomain: \"rafthor-0001.firebaseapp.com\",\n    projectId: \"rafthor-0001\",\n    storageBucket: \"rafthor-0001.firebasestorage.app\",\n    messagingSenderId: \"863587500028\",\n    appId: \"1:863587500028:web:ea161ddd3a1a024a7f3c79\"\n};\n// Verificar se a configuração está correta\nif (!firebaseConfig.apiKey || firebaseConfig.apiKey.length < 30) {\n    throw new Error(\"Firebase API Key inv\\xe1lida ou n\\xe3o configurada\");\n}\n// Inicializar Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n// Inicializar serviços\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\nconst functions = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_4__.getFunctions)(app);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/firebase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@firebase","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/idb"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Fmessage%2F%5BmessageId%5D%2Froute&page=%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Fmessage%2F%5BmessageId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Fmessage%2F%5BmessageId%5D%2Froute.ts&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();