"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AppearanceContext */ \"(app-pages-browser)/./src/contexts/AppearanceContext.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./AttachmentsModal */ \"(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\");\n/* harmony import */ var _StatisticsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./StatisticsModal */ \"(app-pages-browser)/./src/components/dashboard/StatisticsModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ChatArea = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { currentChat, onChatCreated, onUpdateOpenRouterBalance, onChatSelect, sidebarRef } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { settings: appearanceSettings } = (0,_contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_6__.useAppearance)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStatisticsModalOpen, setIsStatisticsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Estados para drag-n-drop\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragCounter, setDragCounter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dragTimeout, setDragTimeout] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Detectar quando o drag sai da janela para resetar o estado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleWindowDragLeave = (e)=>{\n            // Se o drag sai da janela (relatedTarget é null), resetar o estado\n            if (!e.relatedTarget) {\n                setIsDragOver(false);\n                setDragCounter(0);\n            }\n        };\n        const handleWindowDragEnd = ()=>{\n            // Resetar estado quando o drag termina\n            setIsDragOver(false);\n            setDragCounter(0);\n        };\n        const handleWindowDrop = ()=>{\n            // Resetar estado quando há drop em qualquer lugar\n            setIsDragOver(false);\n            setDragCounter(0);\n        };\n        // Adicionar listeners globais\n        window.addEventListener(\"dragleave\", handleWindowDragLeave);\n        window.addEventListener(\"dragend\", handleWindowDragEnd);\n        window.addEventListener(\"drop\", handleWindowDrop);\n        return ()=>{\n            window.removeEventListener(\"dragleave\", handleWindowDragLeave);\n            window.removeEventListener(\"dragend\", handleWindowDragEnd);\n            window.removeEventListener(\"drop\", handleWindowDrop);\n        };\n    }, []);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado para um chat específico\n    const saveLastUsedModelForChat = async (modelId, chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            // Atualizar o lastUsedModel no documento do chat\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(chatRef, {\n                lastUsedModel: modelId,\n                lastModelUpdateAt: Date.now()\n            });\n        } catch (error) {\n            console.error(\"Error saving last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado de um chat específico\n    const loadLastUsedModelForChat = async (chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(chatRef);\n            if (chatDoc.exists()) {\n                const data = chatDoc.data();\n                if (data.lastUsedModel) {\n                    // Verificar se o modelo salvo ainda é válido\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (isValid) {\n                        setSelectedModel(data.lastUsedModel);\n                    } else {\n                        // Limpar o modelo inválido do chat\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(chatRef, {\n                            lastUsedModel: \"\"\n                        });\n                        // Carregar o modelo padrão do endpoint ativo\n                        loadDefaultModelFromActiveEndpoint();\n                    }\n                } else {\n                    // Se o chat não tem modelo salvo, carregar o modelo padrão do endpoint ativo\n                    loadDefaultModelFromActiveEndpoint();\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o modelo padrão do endpoint ativo\n    const loadDefaultModelFromActiveEndpoint = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                // Primeiro, tentar carregar o último modelo usado globalmente\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    return;\n                }\n                // Se não há último modelo usado, buscar o modelo padrão do endpoint ativo\n                if (data.endpoints) {\n                    const activeEndpoint = Object.values(data.endpoints).find((endpoint)=>endpoint.ativo);\n                    if (activeEndpoint && activeEndpoint.modeloPadrao) {\n                        setSelectedModel(activeEndpoint.modeloPadrao);\n                        return;\n                    }\n                }\n            }\n            // Fallback para o modelo padrão hardcoded\n            setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n        } catch (error) {\n            console.error(\"Error loading default model from active endpoint:\", error);\n            // Fallback para o modelo padrão hardcoded em caso de erro\n            setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n        }\n    };\n    // Função para validar se um modelo ainda existe/é válido\n    const isValidModel = async (modelId)=>{\n        // Lista de modelos conhecidos como inválidos ou removidos\n        const invalidModels = [\n            \"qwen/qwen3-235b-a22b-thinking-2507\"\n        ];\n        return !invalidModels.includes(modelId);\n    };\n    // Função para limpar modelos inválidos de todos os chats do usuário\n    const cleanupInvalidModelsFromAllChats = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\");\n            const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(chatsRef);\n            const updatePromises = [];\n            for (const chatDoc of chatsSnapshot.docs){\n                const data = chatDoc.data();\n                if (data.lastUsedModel) {\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (!isValid) {\n                        updatePromises.push((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatDoc.id), {\n                            lastUsedModel: \"\"\n                        }));\n                    }\n                }\n            }\n            if (updatePromises.length > 0) {\n                await Promise.all(updatePromises);\n            }\n        } catch (error) {\n            console.error(\"Error cleaning invalid models from chats:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado globalmente (fallback)\n    const loadGlobalLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    // Verificar se o modelo salvo ainda é válido\n                    const isValid = await isValidModel(data.lastUsedModel);\n                    if (isValid) {\n                        setSelectedModel(data.lastUsedModel);\n                    } else {\n                        // Limpar o modelo inválido das configurações\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(userRef, {\n                            lastUsedModel: null\n                        });\n                        // Carregar o modelo padrão do endpoint ativo\n                        loadDefaultModelFromActiveEndpoint();\n                    }\n                } else {\n                    // Se não há último modelo usado, carregar o modelo padrão do endpoint ativo\n                    loadDefaultModelFromActiveEndpoint();\n                }\n            } else {\n                // Se não há configurações, carregar o modelo padrão do endpoint ativo\n                loadDefaultModelFromActiveEndpoint();\n            }\n        } catch (error) {\n            console.error(\"Error loading global last used model:\", error);\n            // Fallback para carregar o modelo padrão do endpoint ativo\n            loadDefaultModelFromActiveEndpoint();\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        // Salvar no chat específico se houver um chat ativo\n        if (actualChatId) {\n            saveLastUsedModelForChat(modelId, actualChatId);\n        }\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const firebaseStorageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(firebaseStorageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            // Definir o nome do chat imediatamente após criação\n            setChatName(finalChatName);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments, webSearchEnabled)=>{\n        // Obter anexos históricos ativos\n        const historicalAttachments = getAllChatAttachments().filter((att)=>att.isActive !== false);\n        // Combinar anexos novos com anexos históricos ativos\n        const allAttachmentsToSend = [\n            ...attachments || [],\n            ...historicalAttachments // Anexos históricos ativos\n        ];\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachmentsToSend.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // O nome já foi definido na função createAutoChat, mas vamos garantir carregando também\n                loadChatName(chatIdToUse);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Mover chat para o topo com animação\n        if (chatIdToUse && (sidebarRef === null || sidebarRef === void 0 ? void 0 : sidebarRef.current)) {\n            sidebarRef.current.moveToTop(chatIdToUse);\n        }\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA (incluindo anexos históricos ativos + anexos novos)\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: uniqueAttachments,\n            webSearchEnabled: webSearchEnabled,\n            userMessageId: userMessage.id\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString(),\n                        hasWebSearch: webSearchEnabled\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            // Salvar o modelo usado no chat específico\n            if (chatIdToUse) {\n                saveLastUsedModelForChat(selectedModel, chatIdToUse);\n            }\n            // Atualizar saldo do OpenRouter após a resposta com delay de 5 segundos\n            if (onUpdateOpenRouterBalance) {\n                setTimeout(()=>{\n                    onUpdateOpenRouterBalance();\n                }, 5000);\n            }\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                const chatName = chatData.name || \"Conversa sem nome\";\n                setChatName(chatName);\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, chatId);\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(chatMessages);\n            setMessages(convertedMessages);\n        } catch (error) {\n            console.error(\"❌ Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado globalmente quando o componente montar (apenas se não há chat)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && !currentChat) {\n            // Limpar modelos inválidos uma vez quando o usuário faz login\n            cleanupInvalidModelsFromAllChats();\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        user,\n        currentChat\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n            // Carregar o modelo específico do chat\n            loadLastUsedModelForChat(currentChat);\n        } else if (!currentChat && actualChatId) {\n            // Só resetar se realmente não há chat e havia um chat antes\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n            // Carregar modelo global quando não há chat específico\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // ✅ CORREÇÃO: Recarregar mensagens do Firebase Storage para garantir estado atualizado\n        console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o para garantir estado atualizado...\");\n        try {\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                setIsLoading(false);\n                setIsStreaming(false);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // Deletar apenas as mensagens POSTERIORES do Firebase Storage (não a mensagem atual)\n            console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n            for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                const msgToDelete = convertedFreshMessages[i];\n                console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true,\n                userMessageId: messageToRegenerate.id\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração com delay de 5 segundos\n                if (onUpdateOpenRouterBalance) {\n                    setTimeout(()=>{\n                        onUpdateOpenRouterBalance();\n                    }, 5000);\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return false;\n        console.log(\"✏️ Iniciando edi\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            console.log(\"\\uD83D\\uDCE4 Enviando atualiza\\xe7\\xe3o para o servidor...\");\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                console.error(\"❌ Falha ao atualizar mensagem no servidor\");\n                loadChatMessages(actualChatId);\n                return false;\n            } else {\n                console.log(\"✅ Mensagem editada e salva com sucesso no Firebase Storage:\", {\n                    messageId,\n                    timestamp: new Date().toISOString()\n                });\n                return true;\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            console.error(\"❌ Erro ao atualizar mensagem:\", error);\n            loadChatMessages(actualChatId);\n            return false;\n        }\n    };\n    const handleEditAndRegenerate = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        console.log(\"✏️\\uD83D\\uDD04 Iniciando edi\\xe7\\xe3o e regenera\\xe7\\xe3o de mensagem:\", {\n            messageId,\n            chatId: actualChatId,\n            newContentLength: newContent.length,\n            newContentPreview: newContent.substring(0, 100) + \"...\"\n        });\n        try {\n            // 1. Primeiro, salvar a edição\n            const editSuccess = await handleEditMessage(messageId, newContent);\n            if (!editSuccess) {\n                console.error(\"❌ Falha ao editar mensagem, cancelando regenera\\xe7\\xe3o\");\n                return;\n            }\n            console.log(\"✅ Mensagem editada com sucesso, iniciando regenera\\xe7\\xe3o...\");\n            // 2. Aguardar um pouco para garantir que a edição foi salva\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n            // 3. Recarregar mensagens do Firebase Storage para garantir estado atualizado\n            console.log(\"\\uD83D\\uDD04 Recarregando mensagens antes da regenera\\xe7\\xe3o...\");\n            const username = await getUsernameFromFirestore();\n            // Carregar mensagens diretamente do Firebase Storage\n            const freshMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].loadChatMessages(username, actualChatId);\n            const convertedFreshMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].convertFromAIFormat(freshMessages);\n            console.log(\"\\uD83D\\uDCE5 Mensagens recarregadas do Storage:\", convertedFreshMessages.length);\n            // 4. Buscar o índice da mensagem nas mensagens frescas\n            const messageIndex = convertedFreshMessages.findIndex((msg)=>msg.id === messageId);\n            if (messageIndex === -1) {\n                console.error(\"❌ Mensagem n\\xe3o encontrada ap\\xf3s recarregar:\", messageId);\n                return;\n            }\n            const messageToRegenerate = convertedFreshMessages[messageIndex];\n            console.log(\"\\uD83D\\uDCDD Mensagem que ser\\xe1 regenerada:\", {\n                id: messageToRegenerate.id,\n                content: messageToRegenerate.content.substring(0, 100) + \"...\",\n                index: messageIndex\n            });\n            // 5. Verificar se há mensagens após esta mensagem\n            const hasMessagesAfter = messageIndex < convertedFreshMessages.length - 1;\n            console.log(\"\\uD83D\\uDCCA Mensagens ap\\xf3s esta: \".concat(convertedFreshMessages.length - messageIndex - 1));\n            // 6. Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n            const messagesBeforeRegeneration = convertedFreshMessages.slice(0, messageIndex + 1);\n            setMessages(messagesBeforeRegeneration);\n            // 7. Preparar o conteúdo da mensagem para regenerar\n            setMessage(messageToRegenerate.content);\n            setIsLoading(true);\n            setIsStreaming(true);\n            // 8. Preparar ID para a nova mensagem da IA que será criada durante o streaming\n            const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].generateMessageId();\n            setStreamingMessageId(aiMessageId);\n            // 9. Deletar apenas as mensagens POSTERIORES do Firebase Storage (se houver)\n            if (hasMessagesAfter) {\n                console.log(\"\\uD83D\\uDDD1️ Deletando \".concat(convertedFreshMessages.length - messageIndex - 1, \" mensagens posteriores...\"));\n                for(let i = messageIndex + 1; i < convertedFreshMessages.length; i++){\n                    const msgToDelete = convertedFreshMessages[i];\n                    console.log(\"\\uD83D\\uDDD1️ Deletando mensagem:\", msgToDelete.id);\n                    await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n                }\n            }\n            // 10. Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_13__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true,\n                userMessageId: messageToRegenerate.id\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração com delay de 5 segundos\n                if (onUpdateOpenRouterBalance) {\n                    setTimeout(()=>{\n                        onUpdateOpenRouterBalance();\n                    }, 5000);\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao editar e regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: 0,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: scrollContainer.scrollHeight,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = (chatId)=>{\n        // Se um chatId específico foi fornecido e é diferente do atual, muda para esse chat primeiro\n        if (chatId && chatId !== currentChat && onChatSelect) {\n            onChatSelect(chatId);\n            // Abre o modal após um pequeno delay para garantir que o chat foi carregado\n            setTimeout(()=>{\n                setIsDownloadModalOpen(true);\n            }, 100);\n        } else {\n            setIsDownloadModalOpen(true);\n        }\n    };\n    // Função para abrir o modal de anexos\n    const handleAttachmentsModal = (chatId)=>{\n        // Se um chatId específico foi fornecido e é diferente do atual, muda para esse chat primeiro\n        if (chatId && chatId !== currentChat && onChatSelect) {\n            onChatSelect(chatId);\n            setTimeout(()=>{\n                setIsAttachmentsModalOpen(true);\n            }, 100);\n        } else {\n            setIsAttachmentsModalOpen(true);\n        }\n    };\n    // Função para abrir o modal de estatísticas\n    const handleStatisticsModal = (chatId)=>{\n        // Se um chatId específico foi fornecido e é diferente do atual, muda para esse chat primeiro\n        if (chatId && chatId !== currentChat && onChatSelect) {\n            onChatSelect(chatId);\n            setTimeout(()=>{\n                setIsStatisticsModalOpen(true);\n            }, 100);\n        } else {\n            setIsStatisticsModalOpen(true);\n        }\n    };\n    // Expor as funções para o componente pai\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            handleDownloadModal,\n            handleAttachmentsModal,\n            handleStatisticsModal\n        }));\n    // Função para obter todos os anexos do chat\n    const getAllChatAttachments = ()=>{\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                allAttachments.push(...message.attachments);\n            }\n        });\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachments.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        return uniqueAttachments;\n    };\n    // Função para salvar o estado dos anexos no Firebase Storage\n    const saveAttachmentStates = async (updatedMessages)=>{\n        if (!currentUsername || !actualChatId) return;\n        try {\n            // Preparar dados do chat para salvar\n            const chatData = {\n                id: actualChatId,\n                name: chatName || \"Chat\",\n                messages: updatedMessages.map((msg)=>({\n                        id: msg.id,\n                        content: msg.content,\n                        role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                        timestamp: msg.timestamp,\n                        isFavorite: msg.isFavorite,\n                        attachments: msg.attachments\n                    })),\n                createdAt: new Date().toISOString(),\n                lastUpdated: new Date().toISOString()\n            };\n            // Salvar no Firebase Storage\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(currentUsername, \"/conversas/\").concat(actualChatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(chatJsonRef, chatJsonBlob);\n            console.log(\"✅ Estado dos anexos salvo no Firebase Storage\");\n            console.log(\"\\uD83D\\uDCC1 Dados salvos:\", {\n                chatId: actualChatId,\n                totalMessages: chatData.messages.length,\n                messagesWithAttachments: chatData.messages.filter((msg)=>msg.attachments && msg.attachments.length > 0).length\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao salvar estado dos anexos:\", error);\n        }\n    };\n    // Função para alternar o estado ativo de um anexo\n    const handleToggleAttachment = (attachmentId)=>{\n        setMessages((prevMessages)=>{\n            const updatedMessages = prevMessages.map((message)=>{\n                if (message.attachments && message.attachments.length > 0) {\n                    const updatedAttachments = message.attachments.map((attachment)=>{\n                        if (attachment.id === attachmentId) {\n                            // Se isActive não está definido, considerar como true (ativo por padrão)\n                            const currentState = attachment.isActive !== false;\n                            return {\n                                ...attachment,\n                                isActive: !currentState\n                            };\n                        }\n                        return attachment;\n                    });\n                    return {\n                        ...message,\n                        attachments: updatedAttachments\n                    };\n                }\n                return message;\n            });\n            // Salvar o estado atualizado no Firebase Storage\n            saveAttachmentStates(updatedMessages);\n            return updatedMessages;\n        });\n    };\n    // Função para filtrar anexos ativos para envio à IA\n    const getActiveAttachments = (attachments)=>{\n        if (!attachments) return [];\n        // Para anexos novos (sem isActive definido), incluir por padrão\n        // Para anexos existentes, verificar se isActive não é false\n        return attachments.filter((attachment)=>{\n            // Se isActive não está definido (anexo novo), incluir\n            if (attachment.isActive === undefined) return true;\n            // Se isActive está definido, incluir apenas se não for false\n            return attachment.isActive !== false;\n        });\n    };\n    // Função para obter IDs dos anexos ativos\n    const getActiveAttachmentIds = ()=>{\n        const allAttachments = getAllChatAttachments();\n        return allAttachments.filter((att)=>att.isActive !== false).map((att)=>att.id);\n    };\n    // Função para resetar estado de drag\n    const resetDragState = ()=>{\n        setIsDragOver(false);\n        setDragCounter(0);\n        if (dragTimeout) {\n            clearTimeout(dragTimeout);\n            setDragTimeout(null);\n        }\n    };\n    // Funções para drag-n-drop\n    const handleDragEnter = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragCounter((prev)=>prev + 1);\n        // Verificar se há arquivos sendo arrastados\n        if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n            // Verificar se pelo menos um item é um arquivo\n            const hasFiles = Array.from(e.dataTransfer.items).some((item)=>item.kind === \"file\");\n            if (hasFiles) {\n                setIsDragOver(true);\n                // Limpar timeout anterior se existir\n                if (dragTimeout) {\n                    clearTimeout(dragTimeout);\n                }\n                // Definir timeout de segurança para resetar o estado após 3 segundos\n                const timeout = setTimeout(()=>{\n                    resetDragState();\n                }, 3000);\n                setDragTimeout(timeout);\n            }\n        }\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragCounter((prev)=>{\n            const newCount = prev - 1;\n            // Só remove o overlay quando o contador chega a 0\n            if (newCount <= 0) {\n                resetDragState();\n                return 0;\n            }\n            return newCount;\n        });\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        // Verificar se há arquivos sendo arrastados\n        if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {\n            const hasFiles = Array.from(e.dataTransfer.items).some((item)=>item.kind === \"file\");\n            if (hasFiles) {\n                e.dataTransfer.dropEffect = \"copy\";\n            } else {\n                e.dataTransfer.dropEffect = \"none\";\n            }\n        }\n    };\n    const handleDrop = async (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        // Sempre resetar o estado de drag, independentemente do que acontecer\n        resetDragState();\n        const files = Array.from(e.dataTransfer.files);\n        // Se não há arquivos, apenas sair\n        if (files.length === 0) {\n            return;\n        }\n        // Verificar se temos username necessário\n        if (!currentUsername) {\n            console.error(\"Username n\\xe3o dispon\\xedvel para upload de anexos\");\n            return;\n        }\n        // Se não há chat atual, criar um automaticamente para poder fazer upload dos anexos\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = files.length === 1 ? \"Arquivo anexado: \".concat(files[0].name) : \"\".concat(files.length, \" arquivos anexados\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                loadChatName(chatIdToUse);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID para anexos\");\n            return;\n        }\n        try {\n            // Importar o attachmentService dinamicamente\n            const { default: attachmentService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/services/attachmentService */ \"(app-pages-browser)/./src/lib/services/attachmentService.ts\"));\n            // Fazer upload dos arquivos\n            const uploadedAttachments = await attachmentService.uploadMultipleAttachments(files, currentUsername, chatIdToUse);\n            // Em vez de enviar a mensagem, vamos notificar o InputBar sobre os novos anexos\n            // Isso será feito através de um evento customizado\n            const attachmentMetadata = uploadedAttachments.map((att)=>att.metadata);\n            // Disparar evento customizado para o InputBar capturar\n            const event = new CustomEvent(\"dragDropAttachments\", {\n                detail: {\n                    attachments: attachmentMetadata,\n                    chatId: chatIdToUse,\n                    username: currentUsername\n                }\n            });\n            window.dispatchEvent(event);\n            console.log(\"✅ \".concat(files.length, \" arquivo(s) adicionado(s) como anexo via drag-n-drop\"));\n        } catch (error) {\n            console.error(\"❌ Erro ao processar arquivos via drag-n-drop:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen relative\",\n        onDragEnter: handleDragEnter,\n        onDragLeave: handleDragLeave,\n        onDragOver: handleDragOver,\n        onDrop: handleDrop,\n        children: [\n            isDragOver && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-50 bg-blue-900/80 backdrop-blur-sm flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-800/90 backdrop-blur-md rounded-2xl p-8 border-2 border-dashed border-blue-400 shadow-2xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-16 h-16 text-blue-300 mx-auto animate-bounce\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                        lineNumber: 1355,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                    lineNumber: 1354,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                lineNumber: 1353,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-blue-100 mb-2\",\n                                children: \"Solte os arquivos aqui\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                lineNumber: 1358,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-300 text-sm\",\n                                children: \"Os arquivos ser\\xe3o adicionados como anexos \\xe0 conversa\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                                lineNumber: 1361,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                        lineNumber: 1352,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 1351,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1350,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0\",\n                style: {\n                    height: \"calc(100vh - 140px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    chatId: actualChatId || \"temp-chat\",\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    isStreaming: isStreaming,\n                    streamingMessageId: streamingMessageId || undefined,\n                    enableSessions: appearanceSettings.sessionsEnabled,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onEditAndRegenerate: handleEditAndRegenerate,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 1373,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1372,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                onOpenAttachmentsModal: handleAttachmentsModal,\n                username: currentUsername,\n                chatId: actualChatId || undefined,\n                activeAttachmentsCount: getActiveAttachmentIds().length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1390,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1409,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1417,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isAttachmentsModalOpen,\n                onClose: ()=>setIsAttachmentsModalOpen(false),\n                attachments: getAllChatAttachments(),\n                activeAttachments: getActiveAttachmentIds(),\n                onToggleAttachment: handleToggleAttachment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1425,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatisticsModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                isOpen: isStatisticsModalOpen,\n                onClose: ()=>setIsStatisticsModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 1434,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 1341,\n        columnNumber: 5\n    }, undefined);\n}, \"TG3De0pWCvgUEXIDn5rLo82n0v4=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_6__.useAppearance\n    ];\n})), \"TG3De0pWCvgUEXIDn5rLo82n0v4=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_6__.useAppearance\n    ];\n});\n_c1 = ChatArea;\nChatArea.displayName = \"ChatArea\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (ChatArea);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatArea$forwardRef\");\n$RefreshReg$(_c1, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9DaGF0QXJlYS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXFGO0FBQ2tCO0FBQ3JDO0FBQ3JCO0FBQ0k7QUFDWTtBQUVqQjtBQUNWO0FBRVU7QUFDWTtBQUNOO0FBQ0Y7QUFFQztBQThCakQsTUFBTTJCLHlCQUFXeEIsR0FBQUEsaURBQVVBLFNBSVQsUUFBc0ZVO1FBQXJGLEVBQUVlLFdBQVcsRUFBRUMsYUFBYSxFQUFFQyx5QkFBeUIsRUFBRUMsWUFBWSxFQUFFQyxVQUFVLEVBQUU7O0lBQ3BHLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUdmLDhEQUFPQTtJQUN4QixNQUFNLEVBQUVnQixVQUFVQyxrQkFBa0IsRUFBRSxHQUFHaEIsMEVBQWFBO0lBQ3RELE1BQU0sQ0FBQ2lCLFNBQVNDLFdBQVcsR0FBR3JDLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ3NDLFdBQVdDLGFBQWEsR0FBR3ZDLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3dDLFVBQVVDLFlBQVksR0FBR3pDLCtDQUFRQSxDQUFZLEVBQUU7SUFDdEQsTUFBTSxDQUFDMEMsZUFBZUMsaUJBQWlCLEdBQUczQywrQ0FBUUEsQ0FBQztJQUVuRCxNQUFNLENBQUM0QyxjQUFjQyxnQkFBZ0IsR0FBRzdDLCtDQUFRQSxDQUFnQjRCO0lBQ2hFLE1BQU0sQ0FBQ2tCLHFCQUFxQkMsdUJBQXVCLEdBQUcvQywrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUNnRCxrQkFBa0JDLG9CQUFvQixHQUFHakQsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDa0Qsd0JBQXdCQywwQkFBMEIsR0FBR25ELCtDQUFRQSxDQUFDO0lBQ3JFLE1BQU0sQ0FBQ29ELHVCQUF1QkMseUJBQXlCLEdBQUdyRCwrQ0FBUUEsQ0FBQztJQUNuRSxNQUFNLENBQUNzRCxhQUFhQyxlQUFlLEdBQUd2RCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUN3RCxvQkFBb0JDLHNCQUFzQixHQUFHekQsK0NBQVFBLENBQWdCO0lBQzVFLE1BQU0sQ0FBQzBELFVBQVVDLFlBQVksR0FBRzNELCtDQUFRQSxDQUFTO0lBR2pELE1BQU0sQ0FBQzRELGVBQWVDLGlCQUFpQixHQUFHN0QsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDOEQsaUJBQWlCQyxtQkFBbUIsR0FBRy9ELCtDQUFRQSxDQUFxQmdFO0lBQzNFLE1BQU1DLG1CQUFtQi9ELDZDQUFNQSxDQUFpQjtJQUVoRCwyQkFBMkI7SUFDM0IsTUFBTSxDQUFDZ0UsWUFBWUMsY0FBYyxHQUFHbkUsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDb0UsYUFBYUMsZUFBZSxHQUFHckUsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDc0UsYUFBYUMsZUFBZSxHQUFHdkUsK0NBQVFBLENBQXdCO0lBRXRFLHdEQUF3RDtJQUN4REMsZ0RBQVNBLENBQUM7UUFDUixNQUFNdUUsZUFBZTtZQUNuQixJQUFJdkMsaUJBQUFBLDJCQUFBQSxLQUFNd0MsS0FBSyxFQUFFO2dCQUNmLE1BQU1DLFdBQVcsTUFBTUM7Z0JBQ3ZCWixtQkFBbUJXO1lBQ3JCO1FBQ0Y7UUFDQUY7SUFDRixHQUFHO1FBQUN2QyxpQkFBQUEsMkJBQUFBLEtBQU13QyxLQUFLO0tBQUM7SUFFaEIsNkRBQTZEO0lBQzdEeEUsZ0RBQVNBLENBQUM7UUFDUixNQUFNMkUsd0JBQXdCLENBQUNDO1lBQzdCLG1FQUFtRTtZQUNuRSxJQUFJLENBQUNBLEVBQUVDLGFBQWEsRUFBRTtnQkFDcEJYLGNBQWM7Z0JBQ2RFLGVBQWU7WUFDakI7UUFDRjtRQUVBLE1BQU1VLHNCQUFzQjtZQUMxQix1Q0FBdUM7WUFDdkNaLGNBQWM7WUFDZEUsZUFBZTtRQUNqQjtRQUVBLE1BQU1XLG1CQUFtQjtZQUN2QixrREFBa0Q7WUFDbERiLGNBQWM7WUFDZEUsZUFBZTtRQUNqQjtRQUVBLDhCQUE4QjtRQUM5QlksT0FBT0MsZ0JBQWdCLENBQUMsYUFBYU47UUFDckNLLE9BQU9DLGdCQUFnQixDQUFDLFdBQVdIO1FBQ25DRSxPQUFPQyxnQkFBZ0IsQ0FBQyxRQUFRRjtRQUVoQyxPQUFPO1lBQ0xDLE9BQU9FLG1CQUFtQixDQUFDLGFBQWFQO1lBQ3hDSyxPQUFPRSxtQkFBbUIsQ0FBQyxXQUFXSjtZQUN0Q0UsT0FBT0UsbUJBQW1CLENBQUMsUUFBUUg7UUFDckM7SUFDRixHQUFHLEVBQUU7SUFFTCxpREFBaUQ7SUFDakQsTUFBTUwsMkJBQTJCO1FBQy9CLElBQUksRUFBQzFDLGlCQUFBQSwyQkFBQUEsS0FBTXdDLEtBQUssR0FBRSxPQUFPO1FBRXpCLElBQUk7WUFDRixNQUFNVyxjQUFjN0UsOERBQVVBLENBQUNTLDZDQUFFQSxFQUFFO1lBQ25DLE1BQU1xRSxJQUFJM0UseURBQUtBLENBQUMwRSxhQUFhekUseURBQUtBLENBQUMsU0FBUyxNQUFNc0IsS0FBS3dDLEtBQUs7WUFDNUQsTUFBTWEsZ0JBQWdCLE1BQU0xRSwyREFBT0EsQ0FBQ3lFO1lBRXBDLElBQUksQ0FBQ0MsY0FBY0MsS0FBSyxFQUFFO2dCQUN4QixNQUFNQyxVQUFVRixjQUFjRyxJQUFJLENBQUMsRUFBRTtnQkFDckMsTUFBTUMsV0FBV0YsUUFBUUcsSUFBSTtnQkFDN0IsT0FBT0QsU0FBU2hCLFFBQVEsSUFBSXpDLEtBQUt3QyxLQUFLLENBQUNtQixLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDdEQ7WUFFQSxPQUFPM0QsS0FBS3dDLEtBQUssQ0FBQ21CLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxFQUFFLFdBQVc7UUFDOUMsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDLE9BQU81RCxLQUFLd0MsS0FBSyxDQUFDbUIsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUUsV0FBVztRQUM5QztJQUNGO0lBRUEsbUVBQW1FO0lBQ25FLE1BQU1HLDJCQUEyQixPQUFPQyxTQUFpQkM7UUFDdkQsSUFBSSxDQUFDaEUsUUFBUSxDQUFDZ0UsUUFBUTtRQUV0QixJQUFJO1lBQ0YsTUFBTXZCLFdBQVcsTUFBTUM7WUFDdkIsTUFBTXVCLFVBQVU3Rix1REFBR0EsQ0FBQ1csNkNBQUVBLEVBQUUsWUFBWTBELFVBQVUsYUFBYXVCO1lBRTNELGlEQUFpRDtZQUNqRCxNQUFNeEYsNkRBQVNBLENBQUN5RixTQUFTO2dCQUN2QkMsZUFBZUg7Z0JBQ2ZJLG1CQUFtQkMsS0FBS0MsR0FBRztZQUM3QjtRQUdGLEVBQUUsT0FBT1QsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMENBQTBDQTtRQUMxRDtJQUNGO0lBRUEsbUVBQW1FO0lBQ25FLE1BQU1VLDJCQUEyQixPQUFPTjtRQUN0QyxJQUFJLENBQUNoRSxRQUFRLENBQUNnRSxRQUFRO1FBRXRCLElBQUk7WUFDRixNQUFNdkIsV0FBVyxNQUFNQztZQUN2QixNQUFNdUIsVUFBVTdGLHVEQUFHQSxDQUFDVyw2Q0FBRUEsRUFBRSxZQUFZMEQsVUFBVSxhQUFhdUI7WUFDM0QsTUFBTU8sVUFBVSxNQUFNaEcsMERBQU1BLENBQUMwRjtZQUU3QixJQUFJTSxRQUFRQyxNQUFNLElBQUk7Z0JBQ3BCLE1BQU1kLE9BQU9hLFFBQVFiLElBQUk7Z0JBQ3pCLElBQUlBLEtBQUtRLGFBQWEsRUFBRTtvQkFDdEIsNkNBQTZDO29CQUM3QyxNQUFNTyxVQUFVLE1BQU1DLGFBQWFoQixLQUFLUSxhQUFhO29CQUNyRCxJQUFJTyxTQUFTO3dCQUNYL0QsaUJBQWlCZ0QsS0FBS1EsYUFBYTtvQkFFckMsT0FBTzt3QkFFTCxtQ0FBbUM7d0JBQ25DLE1BQU0xRiw2REFBU0EsQ0FBQ3lGLFNBQVM7NEJBQ3ZCQyxlQUFlO3dCQUNqQjt3QkFDQSw2Q0FBNkM7d0JBQzdDUztvQkFDRjtnQkFDRixPQUFPO29CQUNMLDZFQUE2RTtvQkFDN0VBO2dCQUVGO1lBQ0Y7UUFDRixFQUFFLE9BQU9mLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDJDQUEyQ0E7UUFDM0Q7SUFDRjtJQUVBLHlEQUF5RDtJQUN6RCxNQUFNZSxxQ0FBcUM7UUFDekMsSUFBSSxDQUFDM0UsTUFBTTtRQUVYLElBQUk7WUFDRixNQUFNeUMsV0FBVyxNQUFNQztZQUN2QixNQUFNa0MsVUFBVXhHLHVEQUFHQSxDQUFDVyw2Q0FBRUEsRUFBRSxZQUFZMEQsVUFBVSxpQkFBaUI7WUFDL0QsTUFBTWMsVUFBVSxNQUFNaEYsMERBQU1BLENBQUNxRztZQUU3QixJQUFJckIsUUFBUWlCLE1BQU0sSUFBSTtnQkFDcEIsTUFBTWQsT0FBT0gsUUFBUUcsSUFBSTtnQkFFekIsOERBQThEO2dCQUM5RCxJQUFJQSxLQUFLUSxhQUFhLEVBQUU7b0JBQ3RCeEQsaUJBQWlCZ0QsS0FBS1EsYUFBYTtvQkFFbkM7Z0JBQ0Y7Z0JBRUEsMEVBQTBFO2dCQUMxRSxJQUFJUixLQUFLbUIsU0FBUyxFQUFFO29CQUNsQixNQUFNQyxpQkFBaUJDLE9BQU9DLE1BQU0sQ0FBQ3RCLEtBQUttQixTQUFTLEVBQUVJLElBQUksQ0FBQyxDQUFDQyxXQUFrQkEsU0FBU0MsS0FBSztvQkFDM0YsSUFBSUwsa0JBQWtCLGVBQXdCTSxZQUFZLEVBQUU7d0JBQzFEMUUsaUJBQWlCLGVBQXdCMEUsWUFBWTt3QkFFckQ7b0JBQ0Y7Z0JBQ0Y7WUFDRjtZQUVBLDBDQUEwQztZQUMxQzFFLGlCQUFpQjtRQUVuQixFQUFFLE9BQU9rRCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxxREFBcURBO1lBQ25FLDBEQUEwRDtZQUMxRGxELGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEseURBQXlEO0lBQ3pELE1BQU1nRSxlQUFlLE9BQU9YO1FBQzFCLDBEQUEwRDtRQUMxRCxNQUFNc0IsZ0JBQWdCO1lBQ3BCO1NBRUQ7UUFFRCxPQUFPLENBQUNBLGNBQWNDLFFBQVEsQ0FBQ3ZCO0lBQ2pDO0lBRUEsb0VBQW9FO0lBQ3BFLE1BQU13QixtQ0FBbUM7UUFDdkMsSUFBSSxDQUFDdkYsTUFBTTtRQUVYLElBQUk7WUFDRixNQUFNeUMsV0FBVyxNQUFNQztZQUN2QixNQUFNOEMsV0FBV2xILDhEQUFVQSxDQUFDUyw2Q0FBRUEsRUFBRSxZQUFZMEQsVUFBVTtZQUN0RCxNQUFNZ0QsZ0JBQWdCLE1BQU05RywyREFBT0EsQ0FBQzZHO1lBRXBDLE1BQU1FLGlCQUFrQyxFQUFFO1lBRTFDLEtBQUssTUFBTW5CLFdBQVdrQixjQUFjakMsSUFBSSxDQUFFO2dCQUN4QyxNQUFNRSxPQUFPYSxRQUFRYixJQUFJO2dCQUN6QixJQUFJQSxLQUFLUSxhQUFhLEVBQUU7b0JBQ3RCLE1BQU1PLFVBQVUsTUFBTUMsYUFBYWhCLEtBQUtRLGFBQWE7b0JBQ3JELElBQUksQ0FBQ08sU0FBUzt3QkFFWmlCLGVBQWVDLElBQUksQ0FDakJuSCw2REFBU0EsQ0FBQ0osdURBQUdBLENBQUNXLDZDQUFFQSxFQUFFLFlBQVkwRCxVQUFVLGFBQWE4QixRQUFRcUIsRUFBRSxHQUFHOzRCQUNoRTFCLGVBQWU7d0JBQ2pCO29CQUVKO2dCQUNGO1lBQ0Y7WUFFQSxJQUFJd0IsZUFBZUcsTUFBTSxHQUFHLEdBQUc7Z0JBQzdCLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQ0w7WUFFcEI7UUFDRixFQUFFLE9BQU85QixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw2Q0FBNkNBO1FBQzdEO0lBQ0Y7SUFFQSxvRUFBb0U7SUFDcEUsTUFBTW9DLDBCQUEwQjtRQUM5QixJQUFJLENBQUNoRyxNQUFNO1FBRVgsSUFBSTtZQUNGLE1BQU15QyxXQUFXLE1BQU1DO1lBQ3ZCLE1BQU1rQyxVQUFVeEcsdURBQUdBLENBQUNXLDZDQUFFQSxFQUFFLFlBQVkwRCxVQUFVLGlCQUFpQjtZQUMvRCxNQUFNYyxVQUFVLE1BQU1oRiwwREFBTUEsQ0FBQ3FHO1lBRTdCLElBQUlyQixRQUFRaUIsTUFBTSxJQUFJO2dCQUNwQixNQUFNZCxPQUFPSCxRQUFRRyxJQUFJO2dCQUN6QixJQUFJQSxLQUFLUSxhQUFhLEVBQUU7b0JBQ3RCLDZDQUE2QztvQkFDN0MsTUFBTU8sVUFBVSxNQUFNQyxhQUFhaEIsS0FBS1EsYUFBYTtvQkFDckQsSUFBSU8sU0FBUzt3QkFDWC9ELGlCQUFpQmdELEtBQUtRLGFBQWE7b0JBRXJDLE9BQU87d0JBRUwsNkNBQTZDO3dCQUM3QyxNQUFNMUYsNkRBQVNBLENBQUNvRyxTQUFTOzRCQUN2QlYsZUFBZTt3QkFDakI7d0JBQ0EsNkNBQTZDO3dCQUM3Q1M7b0JBQ0Y7Z0JBQ0YsT0FBTztvQkFDTCw0RUFBNEU7b0JBQzVFQTtnQkFDRjtZQUNGLE9BQU87Z0JBQ0wsc0VBQXNFO2dCQUN0RUE7WUFDRjtRQUNGLEVBQUUsT0FBT2YsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMseUNBQXlDQTtZQUN2RCwyREFBMkQ7WUFDM0RlO1FBQ0Y7SUFDRjtJQUVBLHFFQUFxRTtJQUNyRSxNQUFNc0Isb0JBQW9CLENBQUNsQztRQUN6QnJELGlCQUFpQnFEO1FBRWpCLG9EQUFvRDtRQUNwRCxJQUFJcEQsY0FBYztZQUNoQm1ELHlCQUF5QkMsU0FBU3BEO1FBQ3BDO0lBQ0Y7SUFFQSw0Q0FBNEM7SUFDNUMsTUFBTXVGLGlCQUFpQixPQUFPQztRQUM1QixJQUFJLEVBQUNuRyxpQkFBQUEsMkJBQUFBLEtBQU13QyxLQUFLLEdBQUUsT0FBTztRQUV6QixJQUFJO1lBQ0YsNkJBQTZCO1lBQzdCLE1BQU1XLGNBQWM3RSw4REFBVUEsQ0FBQ1MsNkNBQUVBLEVBQUU7WUFDbkMsTUFBTXFFLElBQUkzRSx5REFBS0EsQ0FBQzBFLGFBQWF6RSx5REFBS0EsQ0FBQyxTQUFTLE1BQU1zQixLQUFLd0MsS0FBSztZQUM1RCxNQUFNYSxnQkFBZ0IsTUFBTTFFLDJEQUFPQSxDQUFDeUU7WUFFcEMsSUFBSUMsY0FBY0MsS0FBSyxFQUFFLE9BQU87WUFFaEMsTUFBTUMsVUFBVUYsY0FBY0csSUFBSSxDQUFDLEVBQUU7WUFDckMsTUFBTUMsV0FBV0YsUUFBUUcsSUFBSTtZQUM3QixNQUFNakIsV0FBV2dCLFNBQVNoQixRQUFRO1lBRWxDLDZCQUE2QjtZQUM3QixNQUFNMkQsWUFBWWhDLEtBQUtDLEdBQUc7WUFDMUIsTUFBTWdDLFNBQVNDLEtBQUtELE1BQU0sR0FBR0UsUUFBUSxDQUFDLElBQUlDLFNBQVMsQ0FBQyxHQUFHO1lBQ3ZELE1BQU14QyxTQUFTLFFBQXFCcUMsT0FBYkQsV0FBVSxLQUFVLE9BQVBDO1lBQ3BDLE1BQU1oQyxNQUFNLElBQUlELE9BQU9xQyxXQUFXO1lBRWxDLDJFQUEyRTtZQUMzRSxJQUFJQyxnQkFBZ0I7WUFDcEIsSUFBSVAsYUFBYVEsSUFBSSxHQUFHZCxNQUFNLEdBQUcsR0FBRztnQkFDbEMsTUFBTWUsUUFBUVQsYUFBYVEsSUFBSSxHQUFHaEQsS0FBSyxDQUFDO2dCQUN4QyxNQUFNbEMsV0FBV21GLE1BQU1DLEtBQUssQ0FBQyxHQUFHUCxLQUFLUSxHQUFHLENBQUMsR0FBR0YsTUFBTWYsTUFBTSxHQUFHa0IsSUFBSSxDQUFDO2dCQUNoRUwsZ0JBQWdCakYsU0FBU29FLE1BQU0sR0FBRyxLQUFLcEUsU0FBUytFLFNBQVMsQ0FBQyxHQUFHLE1BQU0sUUFBUS9FO1lBQzdFO1lBRUEseUJBQXlCO1lBQ3pCLE1BQU11RixnQkFBZ0I7Z0JBQ3BCQyxTQUFTO2dCQUNUQyxXQUFXN0M7Z0JBQ1g4QyxVQUFVO2dCQUNWQyxrQkFBa0I7Z0JBQ2xCQyxTQUFTO2dCQUNUQyxlQUFlakQ7Z0JBQ2ZILGVBQWV6RDtnQkFDZjhHLG1CQUFtQjtnQkFDbkJDLFdBQVc7Z0JBQ1hDLE1BQU1mO2dCQUNOZ0IsVUFBVTtnQkFDVkMsbUJBQW1CO2dCQUNuQkMsYUFBYTtvQkFDWEMsa0JBQWtCeEQ7b0JBQ2xCeUQsYUFBYXpEO29CQUNiMEQsV0FBVztnQkFDYjtnQkFDQUMsY0FBYztnQkFDZEMsYUFBYTtnQkFDYkMsZ0JBQWdCL0IsZ0JBQWdCO2dCQUNoQ2dDLGtCQUFrQjlEO2dCQUNsQitELFdBQVcvRDtZQUNiO1lBRUEsK0JBQStCO1lBQy9CLE1BQU1oRywwREFBTUEsQ0FBQ0QsdURBQUdBLENBQUNXLDZDQUFFQSxFQUFFLFlBQVkwRCxVQUFVLGFBQWF1QixTQUFTZ0Q7WUFFakUscUNBQXFDO1lBQ3JDLE1BQU1xQixlQUFlO2dCQUNuQnpDLElBQUk1QjtnQkFDSnlELE1BQU1mO2dCQUNObkcsVUFBVSxFQUFFO2dCQUNaMkcsV0FBVzdDO2dCQUNYeUQsYUFBYXpEO1lBQ2Y7WUFFQSxNQUFNaUUsZUFBZSxJQUFJQyxLQUFLO2dCQUFDQyxLQUFLQyxTQUFTLENBQUNKLGNBQWMsTUFBTTthQUFHLEVBQUU7Z0JBQ3JFSyxNQUFNO1lBQ1I7WUFFQSxNQUFNQyxxQkFBcUI5SixxREFBVUEsQ0FBQ0csa0RBQU9BLEVBQUUsWUFBa0NnRixPQUF0QnZCLFVBQVMsZUFBb0IsT0FBUHVCLFFBQU87WUFDeEYsTUFBTWxGLDZEQUFXQSxDQUFDNkosb0JBQW9CTDtZQUV0Q3pFLFFBQVErRSxHQUFHLENBQUMsZ0NBQWdDNUU7WUFDNUMsb0RBQW9EO1lBQ3BEdEMsWUFBWWdGO1lBQ1osT0FBTzFDO1FBRVQsRUFBRSxPQUFPSixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyx1Q0FBdUNBO1lBQ3JELE9BQU87UUFDVDtJQUNGO0lBRUEsTUFBTWlGLG9CQUFvQixPQUFPQyxhQUErREM7UUFFOUYsaUNBQWlDO1FBQ2pDLE1BQU1DLHdCQUF3QkMsd0JBQXdCQyxNQUFNLENBQUNDLENBQUFBLE1BQU9BLElBQUlDLFFBQVEsS0FBSztRQUVyRixxREFBcUQ7UUFDckQsTUFBTUMsdUJBQXVCO2VBQ3ZCUCxlQUFlLEVBQUU7ZUFDbEJFLHNCQUFzQiwyQkFBMkI7U0FDckQ7UUFFRCxtQ0FBbUM7UUFDbkMsTUFBTU0sb0JBQW9CRCxxQkFBcUJILE1BQU0sQ0FBQyxDQUFDSyxZQUFZQyxPQUFPQyxPQUN4RUQsVUFBVUMsS0FBS0MsU0FBUyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFL0QsRUFBRSxLQUFLMkQsV0FBVzNELEVBQUU7UUFHdEQsSUFBSSxDQUFFekYsUUFBUXdHLElBQUksTUFBTyxFQUFDbUMsZUFBZUEsWUFBWWpELE1BQU0sS0FBSyxNQUFPeEYsYUFBYWdCLGFBQWE7WUFDL0Y7UUFDRjtRQUNBLElBQUksRUFBQ3JCLGlCQUFBQSwyQkFBQUEsS0FBTXdDLEtBQUssR0FBRTtRQUVsQixNQUFNb0gsY0FBdUI7WUFDM0JoRSxJQUFJbkcsZ0VBQVNBLENBQUNvSyxpQkFBaUI7WUFDL0JDLFNBQVMzSixRQUFRd0csSUFBSTtZQUNyQm9ELFFBQVE7WUFDUjNELFdBQVcsSUFBSWhDLE9BQU9xQyxXQUFXO1lBQ2pDcUMsYUFBYUEsZUFBZSxFQUFFO1FBQ2hDO1FBRUEsaURBQWlEO1FBQ2pELElBQUlrQixjQUFjcko7UUFDbEIsSUFBSSxDQUFDcUosYUFBYTtZQUNoQixNQUFNQyxpQkFBaUI5SixRQUFRd0csSUFBSSxNQUFPbUMsQ0FBQUEsZUFBZUEsWUFBWWpELE1BQU0sR0FBRyxJQUFJLGtCQUFrQixlQUFjO1lBQ2xIbUUsY0FBYyxNQUFNOUQsZUFBZStEO1lBQ25DLElBQUlELGFBQWE7Z0JBQ2ZwSixnQkFBZ0JvSjtnQkFDaEIsd0ZBQXdGO2dCQUN4RkUsYUFBYUY7Z0JBQ2JwSywwQkFBQUEsb0NBQUFBLGNBQWdCb0s7WUFDbEI7UUFDRjtRQUVBLElBQUksQ0FBQ0EsYUFBYTtZQUNoQm5HLFFBQVFELEtBQUssQ0FBQztZQUNkO1FBQ0Y7UUFFQSxnQ0FBZ0M7UUFDaENwRCxZQUFZMkosQ0FBQUEsT0FBUTttQkFBSUE7Z0JBQU1QO2FBQVk7UUFDMUMsTUFBTVEsaUJBQWlCakssUUFBUXdHLElBQUksTUFBTSxJQUFJLDJDQUEyQztRQUN4RnZHLFdBQVc7UUFDWEUsYUFBYTtRQUNiZ0IsZUFBZTtRQUVmLHNDQUFzQztRQUN0QyxJQUFJMEksZ0JBQWVqSyx1QkFBQUEsaUNBQUFBLFdBQVlzSyxPQUFPLEdBQUU7WUFDdEN0SyxXQUFXc0ssT0FBTyxDQUFDQyxTQUFTLENBQUNOO1FBQy9CO1FBRUEsd0VBQXdFO1FBQ3hFLE1BQU1PLGNBQWM5SyxnRUFBU0EsQ0FBQ29LLGlCQUFpQjtRQUMvQ3JJLHNCQUFzQitJO1FBRXRCLHFDQUFxQztRQUNyQyxNQUFNOUgsV0FBVyxNQUFNQztRQUV2Qix1RUFBdUU7UUFDdkUsTUFBTWpELGdFQUFTQSxDQUFDK0ssZUFBZSxDQUM3QjtZQUNFL0gsVUFBVUE7WUFDVnVCLFFBQVFnRztZQUNSN0osU0FBU2lLO1lBQ1RLLE9BQU9oSztZQUNQcUksYUFBYVE7WUFDYlAsa0JBQWtCQTtZQUNsQjJCLGVBQWVkLFlBQVloRSxFQUFFO1FBQy9CLEdBQ0Esd0VBQXdFO1FBQ3hFLENBQUMrRTtZQUNDbkssWUFBWTJKLENBQUFBO2dCQUNWLDBDQUEwQztnQkFDMUMsTUFBTVMsdUJBQXVCVCxLQUFLVCxTQUFTLENBQUNtQixDQUFBQSxNQUFPQSxJQUFJakYsRUFBRSxLQUFLMkU7Z0JBRTlELElBQUlLLHlCQUF5QixDQUFDLEdBQUc7b0JBQy9CLCtCQUErQjtvQkFDL0IsT0FBT1QsS0FBS1csR0FBRyxDQUFDRCxDQUFBQSxNQUNkQSxJQUFJakYsRUFBRSxLQUFLMkUsY0FDUDs0QkFBRSxHQUFHTSxHQUFHOzRCQUFFZixTQUFTZSxJQUFJZixPQUFPLEdBQUdhO3dCQUFNLElBQ3ZDRTtnQkFFUixPQUFPO29CQUNMLDhDQUE4QztvQkFDOUMsbUVBQW1FO29CQUNuRXZLLGFBQWE7b0JBRWIsTUFBTXlLLFlBQXFCO3dCQUN6Qm5GLElBQUkyRTt3QkFDSlQsU0FBU2E7d0JBQ1RaLFFBQVE7d0JBQ1IzRCxXQUFXLElBQUloQyxPQUFPcUMsV0FBVzt3QkFDakN1RSxjQUFjakM7b0JBQ2hCO29CQUNBLE9BQU87MkJBQUlvQjt3QkFBTVk7cUJBQVU7Z0JBQzdCO1lBQ0Y7UUFDRixHQUNBLG1DQUFtQztRQUNuQyxDQUFDRTtZQUNDekssWUFBWTJKLENBQUFBLE9BQVFBLEtBQUtXLEdBQUcsQ0FBQ0QsQ0FBQUEsTUFDM0JBLElBQUlqRixFQUFFLEtBQUsyRSxjQUNQO3dCQUFFLEdBQUdNLEdBQUc7d0JBQUVmLFNBQVNtQjtvQkFBYSxJQUNoQ0o7WUFFTnZLLGFBQWE7WUFDYmdCLGVBQWU7WUFDZkUsc0JBQXNCO1lBRXRCLDJDQUEyQztZQUMzQyxJQUFJd0ksYUFBYTtnQkFDZmxHLHlCQUF5QnJELGVBQWV1SjtZQUMxQztZQUVBLHdFQUF3RTtZQUN4RSxJQUFJbkssMkJBQTJCO2dCQUM3QnFMLFdBQVc7b0JBQ1RyTDtnQkFDRixHQUFHO1lBQ0w7UUFDRixHQUNBLHlCQUF5QjtRQUN6QixDQUFDK0Q7WUFDQ0MsUUFBUUQsS0FBSyxDQUFDLGVBQWVBO1lBQzdCcEQsWUFBWTJKLENBQUFBLE9BQVFBLEtBQUtXLEdBQUcsQ0FBQ0QsQ0FBQUEsTUFDM0JBLElBQUlqRixFQUFFLEtBQUsyRSxjQUNQO3dCQUFFLEdBQUdNLEdBQUc7d0JBQUVmLFNBQVMsV0FBaUIsT0FBTmxHO29CQUFRLElBQ3RDaUg7WUFFTnZLLGFBQWE7WUFDYmdCLGVBQWU7WUFDZkUsc0JBQXNCO1FBQ3hCO0lBRUo7SUFFQSxpQ0FBaUM7SUFDakMsTUFBTTJKLHdCQUF3QjtRQUM1QjFMLGdFQUFTQSxDQUFDMkwsYUFBYTtRQUN2QjlLLGFBQWE7UUFDYmdCLGVBQWU7UUFDZkUsc0JBQXNCO0lBQ3hCO0lBRUEsbURBQW1EO0lBQ25ELE1BQU0wSSxlQUFlLE9BQU9sRztRQUMxQixJQUFJLEVBQUNoRSxpQkFBQUEsMkJBQUFBLEtBQU13QyxLQUFLLEdBQUU7UUFFbEIsSUFBSTtZQUNGLE1BQU1DLFdBQVcsTUFBTUM7WUFDdkIsTUFBTTZCLFVBQVUsTUFBTWhHLDBEQUFNQSxDQUFDSCx1REFBR0EsQ0FBQ1csNkNBQUVBLEVBQUUsWUFBWTBELFVBQVUsYUFBYXVCO1lBRXhFLElBQUlPLFFBQVFDLE1BQU0sSUFBSTtnQkFDcEIsTUFBTTZHLFdBQVc5RyxRQUFRYixJQUFJO2dCQUM3QixNQUFNakMsV0FBVzRKLFNBQVM1RCxJQUFJLElBQUk7Z0JBQ2xDL0YsWUFBWUQ7WUFDZCxPQUFPO2dCQUNMQyxZQUFZO1lBQ2Q7UUFDRixFQUFFLE9BQU9rQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxrQ0FBa0NBO1lBQ2hEbEMsWUFBWTtRQUNkO0lBQ0Y7SUFFQSxvREFBb0Q7SUFDcEQsTUFBTTRKLG1CQUFtQixPQUFPdEg7UUFDOUIsSUFBSSxFQUFDaEUsaUJBQUFBLDJCQUFBQSxLQUFNd0MsS0FBSyxHQUFFO1FBRWxCWixpQkFBaUI7UUFDakIsSUFBSTtZQUNGLE1BQU1hLFdBQVcsTUFBTUM7WUFDdkIsTUFBTTZJLGVBQWUsTUFBTTlMLGdFQUFTQSxDQUFDNkwsZ0JBQWdCLENBQUM3SSxVQUFVdUI7WUFDaEUsTUFBTXdILG9CQUFvQi9MLGdFQUFTQSxDQUFDZ00sbUJBQW1CLENBQUNGO1lBQ3hEL0ssWUFBWWdMO1FBQ2QsRUFBRSxPQUFPNUgsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMseUNBQXlDQTtZQUN2RHBELFlBQVksRUFBRTtRQUNoQixTQUFVO1lBQ1JvQixpQkFBaUI7UUFDbkI7SUFDRjtJQUVBLDhGQUE4RjtJQUM5RjVELGdEQUFTQSxDQUFDO1FBQ1IsSUFBSWdDLFFBQVEsQ0FBQ0wsYUFBYTtZQUN4Qiw4REFBOEQ7WUFDOUQ0RjtZQUNBUztRQUNGO0lBQ0YsR0FBRztRQUFDaEc7UUFBTUw7S0FBWTtJQUV0QiwrQ0FBK0M7SUFDL0MzQixnREFBU0EsQ0FBQztRQUNSLElBQUkyQixlQUFlQSxnQkFBZ0JnQixjQUFjO1lBQy9DQyxnQkFBZ0JqQjtZQUNoQmlDLGlCQUFpQjtZQUNqQix1RUFBdUU7WUFDdkVwQixZQUFZLEVBQUU7WUFDZDhLLGlCQUFpQjNMO1lBQ2pCdUssYUFBYXZLO1lBQ2IsdUNBQXVDO1lBQ3ZDMkUseUJBQXlCM0U7UUFDM0IsT0FBTyxJQUFJLENBQUNBLGVBQWVnQixjQUFjO1lBQ3ZDLDREQUE0RDtZQUM1REMsZ0JBQWdCO1lBQ2hCSixZQUFZLEVBQUU7WUFDZGtCLFlBQVk7WUFDWkUsaUJBQWlCO1lBQ2pCLHVEQUF1RDtZQUN2RG9FO1FBQ0Y7SUFDRixHQUFHO1FBQUNyRztRQUFhSyxpQkFBQUEsMkJBQUFBLEtBQU13QyxLQUFLO0tBQUM7SUFFN0IsbUNBQW1DO0lBQ25DLE1BQU1rSixzQkFBc0IsT0FBT0M7UUFDakMsSUFBSSxDQUFDaEwsZ0JBQWdCLEVBQUNYLGlCQUFBQSwyQkFBQUEsS0FBTXdDLEtBQUssR0FBRTtRQUVuQyw4Q0FBOEM7UUFDOUNoQyxZQUFZMkosQ0FBQUEsT0FBUUEsS0FBS2pCLE1BQU0sQ0FBQzJCLENBQUFBLE1BQU9BLElBQUlqRixFQUFFLEtBQUsrRjtRQUVsRCxJQUFJO1lBQ0YsTUFBTWxKLFdBQVcsTUFBTUM7WUFDdkIsTUFBTWtKLFVBQVUsTUFBTW5NLGdFQUFTQSxDQUFDb00sYUFBYSxDQUFDcEosVUFBVTlCLGNBQWNnTDtZQUV0RSxJQUFJLENBQUNDLFNBQVM7Z0JBQ1osa0NBQWtDO2dCQUNsQ04saUJBQWlCM0s7Z0JBQ2pCa0QsUUFBUUQsS0FBSyxDQUFDO1lBQ2hCO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO1lBQ2Qsa0NBQWtDO1lBQ2xDMEgsaUJBQWlCM0s7WUFDakJrRCxRQUFRRCxLQUFLLENBQUMsNkJBQTZCQTtRQUM3QztJQUNGO0lBRUEsTUFBTWtJLDBCQUEwQixPQUFPSDtRQUNyQyxJQUFJLENBQUNoTCxnQkFBZ0IsRUFBQ1gsaUJBQUFBLDJCQUFBQSxLQUFNd0MsS0FBSyxHQUFFO1FBRW5DLHVGQUF1RjtRQUN2RnFCLFFBQVErRSxHQUFHLENBQUM7UUFFWixJQUFJO1lBQ0YsTUFBTW5HLFdBQVcsTUFBTUM7WUFFdkIscURBQXFEO1lBQ3JELE1BQU1xSixnQkFBZ0IsTUFBTXRNLGdFQUFTQSxDQUFDNkwsZ0JBQWdCLENBQUM3SSxVQUFVOUI7WUFDakUsTUFBTXFMLHlCQUF5QnZNLGdFQUFTQSxDQUFDZ00sbUJBQW1CLENBQUNNO1lBRTdEbEksUUFBUStFLEdBQUcsQ0FBQyxtREFBeUNvRCx1QkFBdUJuRyxNQUFNO1lBRWxGLG9EQUFvRDtZQUNwRCxNQUFNb0csZUFBZUQsdUJBQXVCdEMsU0FBUyxDQUFDbUIsQ0FBQUEsTUFBT0EsSUFBSWpGLEVBQUUsS0FBSytGO1lBQ3hFLElBQUlNLGlCQUFpQixDQUFDLEdBQUc7Z0JBQ3ZCcEksUUFBUUQsS0FBSyxDQUFDLG9EQUE4QytIO2dCQUM1RHJMLGFBQWE7Z0JBQ2JnQixlQUFlO2dCQUNmO1lBQ0Y7WUFFQSxNQUFNNEssc0JBQXNCRixzQkFBc0IsQ0FBQ0MsYUFBYTtZQUNoRXBJLFFBQVErRSxHQUFHLENBQUMsaURBQW9DO2dCQUM5Q2hELElBQUlzRyxvQkFBb0J0RyxFQUFFO2dCQUMxQmtFLFNBQVNvQyxvQkFBb0JwQyxPQUFPLENBQUN0RCxTQUFTLENBQUMsR0FBRyxPQUFPO2dCQUN6RGdELE9BQU95QztZQUNUO1lBRUEsOEVBQThFO1lBQzlFLE1BQU1FLDZCQUE2QkgsdUJBQXVCbkYsS0FBSyxDQUFDLEdBQUdvRixlQUFlO1lBQ2xGekwsWUFBWTJMO1lBRVosaURBQWlEO1lBQ2pEL0wsV0FBVzhMLG9CQUFvQnBDLE9BQU87WUFDdEN4SixhQUFhO1lBQ2JnQixlQUFlO1lBRWYsNkVBQTZFO1lBQzdFLE1BQU1pSixjQUFjOUssZ0VBQVNBLENBQUNvSyxpQkFBaUI7WUFDL0NySSxzQkFBc0IrSTtZQUV0QixxRkFBcUY7WUFDckYxRyxRQUFRK0UsR0FBRyxDQUFDLDJCQUFrRSxPQUFqRG9ELHVCQUF1Qm5HLE1BQU0sR0FBR29HLGVBQWUsR0FBRTtZQUM5RSxJQUFLLElBQUlHLElBQUlILGVBQWUsR0FBR0csSUFBSUosdUJBQXVCbkcsTUFBTSxFQUFFdUcsSUFBSztnQkFDckUsTUFBTUMsY0FBY0wsc0JBQXNCLENBQUNJLEVBQUU7Z0JBQzdDdkksUUFBUStFLEdBQUcsQ0FBQyxxQ0FBMkJ5RCxZQUFZekcsRUFBRTtnQkFDckQsTUFBTW5HLGdFQUFTQSxDQUFDb00sYUFBYSxDQUFDcEosVUFBVTlCLGNBQWMwTCxZQUFZekcsRUFBRTtZQUN0RTtZQUVBLGtDQUFrQztZQUNsQyxNQUFNbkcsZ0VBQVNBLENBQUMrSyxlQUFlLENBQzdCO2dCQUNFL0gsVUFBVUE7Z0JBQ1Z1QixRQUFRckQ7Z0JBQ1JSLFNBQVMrTCxvQkFBb0JwQyxPQUFPO2dCQUNwQ1csT0FBT2hLO2dCQUNQNkwsZ0JBQWdCO2dCQUNoQjVCLGVBQWV3QixvQkFBb0J0RyxFQUFFO1lBQ3ZDLEdBQ0Esd0VBQXdFO1lBQ3hFLENBQUMrRTtnQkFDQ25LLFlBQVkySixDQUFBQTtvQkFDViwwQ0FBMEM7b0JBQzFDLE1BQU1TLHVCQUF1QlQsS0FBS1QsU0FBUyxDQUFDbUIsQ0FBQUEsTUFBT0EsSUFBSWpGLEVBQUUsS0FBSzJFO29CQUU5RCxJQUFJSyx5QkFBeUIsQ0FBQyxHQUFHO3dCQUMvQiwrQkFBK0I7d0JBQy9CLE9BQU9ULEtBQUtXLEdBQUcsQ0FBQ0QsQ0FBQUEsTUFDZEEsSUFBSWpGLEVBQUUsS0FBSzJFLGNBQ1A7Z0NBQUUsR0FBR00sR0FBRztnQ0FBRWYsU0FBU2UsSUFBSWYsT0FBTyxHQUFHYTs0QkFBTSxJQUN2Q0U7b0JBRVIsT0FBTzt3QkFDTCw4Q0FBOEM7d0JBQzlDLG1FQUFtRTt3QkFDbkV2SyxhQUFhO3dCQUViLE1BQU15SyxZQUFxQjs0QkFDekJuRixJQUFJMkU7NEJBQ0pULFNBQVNhOzRCQUNUWixRQUFROzRCQUNSM0QsV0FBVyxJQUFJaEMsT0FBT3FDLFdBQVc7NEJBQ2pDdUUsY0FBYzt3QkFDaEI7d0JBQ0EsT0FBTzsrQkFBSWI7NEJBQU1ZO3lCQUFVO29CQUM3QjtnQkFDRjtZQUNGLEdBQ0EsbUNBQW1DO1lBQ25DLENBQUNFO2dCQUNDekssWUFBWTJKLENBQUFBLE9BQVFBLEtBQUtXLEdBQUcsQ0FBQ0QsQ0FBQUEsTUFDM0JBLElBQUlqRixFQUFFLEtBQUsyRSxjQUNQOzRCQUFFLEdBQUdNLEdBQUc7NEJBQUVmLFNBQVNtQjt3QkFBYSxJQUNoQ0o7Z0JBRU52SyxhQUFhO2dCQUNiZ0IsZUFBZTtnQkFDZkUsc0JBQXNCO2dCQUN0QnBCLFdBQVcsS0FBSywwQkFBMEI7Z0JBRTFDLDJFQUEyRTtnQkFDM0UsSUFBSVAsMkJBQTJCO29CQUM3QnFMLFdBQVc7d0JBQ1RyTDtvQkFDRixHQUFHO2dCQUNMO1lBQ0YsR0FDQSx5QkFBeUI7WUFDekIsQ0FBQytEO2dCQUNDQyxRQUFRRCxLQUFLLENBQUMsOEJBQXdCQTtnQkFDdENwRCxZQUFZMkosQ0FBQUEsT0FBUUEsS0FBS1csR0FBRyxDQUFDRCxDQUFBQSxNQUMzQkEsSUFBSWpGLEVBQUUsS0FBSzJFLGNBQ1A7NEJBQUUsR0FBR00sR0FBRzs0QkFBRWYsU0FBUyxnQ0FBZ0MsT0FBTmxHO3dCQUFRLElBQ3JEaUg7Z0JBRU52SyxhQUFhO2dCQUNiZ0IsZUFBZTtnQkFDZkUsc0JBQXNCO2dCQUN0QnBCLFdBQVcsS0FBSywwQkFBMEI7WUFDNUM7UUFHSixFQUFFLE9BQU93RCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQ0FBaUNBO1lBQy9DdEQsYUFBYTtZQUNiZ0IsZUFBZTtZQUNmRSxzQkFBc0I7WUFDdEJwQixXQUFXLEtBQUssMEJBQTBCO1lBRTFDLHVDQUF1QztZQUN2Q2tMLGlCQUFpQjNLO1FBQ25CO0lBQ0Y7SUFJQSxNQUFNNEwsb0JBQW9CLE9BQU9aLFdBQW1CYTtRQUNsRCxJQUFJLENBQUM3TCxnQkFBZ0IsRUFBQ1gsaUJBQUFBLDJCQUFBQSxLQUFNd0MsS0FBSyxHQUFFLE9BQU87UUFFMUNxQixRQUFRK0UsR0FBRyxDQUFDLDBDQUFvQztZQUM5QytDO1lBQ0EzSCxRQUFRckQ7WUFDUjhMLGtCQUFrQkQsV0FBVzNHLE1BQU07WUFDbkM2RyxtQkFBbUJGLFdBQVdoRyxTQUFTLENBQUMsR0FBRyxPQUFPO1FBQ3BEO1FBRUEsZ0RBQWdEO1FBQ2hEaEcsWUFBWTJKLENBQUFBLE9BQVFBLEtBQUtXLEdBQUcsQ0FBQ0QsQ0FBQUEsTUFDM0JBLElBQUlqRixFQUFFLEtBQUsrRixZQUFZO29CQUFFLEdBQUdkLEdBQUc7b0JBQUVmLFNBQVMwQztnQkFBVyxJQUFJM0I7UUFHM0QsSUFBSTtZQUNGLE1BQU1wSSxXQUFXLE1BQU1DO1lBQ3ZCbUIsUUFBUStFLEdBQUcsQ0FBQztZQUVaLE1BQU1nRCxVQUFVLE1BQU1uTSxnRUFBU0EsQ0FBQ2tOLGFBQWEsQ0FBQ2xLLFVBQVU5QixjQUFjZ0wsV0FBV2E7WUFFakYsSUFBSSxDQUFDWixTQUFTO2dCQUNaLDJDQUEyQztnQkFDM0MvSCxRQUFRRCxLQUFLLENBQUM7Z0JBQ2QwSCxpQkFBaUIzSztnQkFDakIsT0FBTztZQUNULE9BQU87Z0JBQ0xrRCxRQUFRK0UsR0FBRyxDQUFDLCtEQUErRDtvQkFDekUrQztvQkFDQXZGLFdBQVcsSUFBSWhDLE9BQU9xQyxXQUFXO2dCQUNuQztnQkFDQSxPQUFPO1lBQ1Q7UUFDRixFQUFFLE9BQU83QyxPQUFPO1lBQ2QsMkNBQTJDO1lBQzNDQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQzBILGlCQUFpQjNLO1lBQ2pCLE9BQU87UUFDVDtJQUNGO0lBRUEsTUFBTWlNLDBCQUEwQixPQUFPakIsV0FBbUJhO1FBQ3hELElBQUksQ0FBQzdMLGdCQUFnQixFQUFDWCxpQkFBQUEsMkJBQUFBLEtBQU13QyxLQUFLLEdBQUU7UUFFbkNxQixRQUFRK0UsR0FBRyxDQUFDLDBFQUFvRDtZQUM5RCtDO1lBQ0EzSCxRQUFRckQ7WUFDUjhMLGtCQUFrQkQsV0FBVzNHLE1BQU07WUFDbkM2RyxtQkFBbUJGLFdBQVdoRyxTQUFTLENBQUMsR0FBRyxPQUFPO1FBQ3BEO1FBRUEsSUFBSTtZQUNGLCtCQUErQjtZQUMvQixNQUFNcUcsY0FBYyxNQUFNTixrQkFBa0JaLFdBQVdhO1lBQ3ZELElBQUksQ0FBQ0ssYUFBYTtnQkFDaEJoSixRQUFRRCxLQUFLLENBQUM7Z0JBQ2Q7WUFDRjtZQUVBQyxRQUFRK0UsR0FBRyxDQUFDO1lBRVosNERBQTREO1lBQzVELE1BQU0sSUFBSTlDLFFBQVFnSCxDQUFBQSxVQUFXNUIsV0FBVzRCLFNBQVM7WUFFakQsOEVBQThFO1lBQzlFakosUUFBUStFLEdBQUcsQ0FBQztZQUVaLE1BQU1uRyxXQUFXLE1BQU1DO1lBRXZCLHFEQUFxRDtZQUNyRCxNQUFNcUosZ0JBQWdCLE1BQU10TSxnRUFBU0EsQ0FBQzZMLGdCQUFnQixDQUFDN0ksVUFBVTlCO1lBQ2pFLE1BQU1xTCx5QkFBeUJ2TSxnRUFBU0EsQ0FBQ2dNLG1CQUFtQixDQUFDTTtZQUU3RGxJLFFBQVErRSxHQUFHLENBQUMsbURBQXlDb0QsdUJBQXVCbkcsTUFBTTtZQUVsRix1REFBdUQ7WUFDdkQsTUFBTW9HLGVBQWVELHVCQUF1QnRDLFNBQVMsQ0FBQ21CLENBQUFBLE1BQU9BLElBQUlqRixFQUFFLEtBQUsrRjtZQUN4RSxJQUFJTSxpQkFBaUIsQ0FBQyxHQUFHO2dCQUN2QnBJLFFBQVFELEtBQUssQ0FBQyxvREFBOEMrSDtnQkFDNUQ7WUFDRjtZQUVBLE1BQU1PLHNCQUFzQkYsc0JBQXNCLENBQUNDLGFBQWE7WUFDaEVwSSxRQUFRK0UsR0FBRyxDQUFDLGlEQUFvQztnQkFDOUNoRCxJQUFJc0csb0JBQW9CdEcsRUFBRTtnQkFDMUJrRSxTQUFTb0Msb0JBQW9CcEMsT0FBTyxDQUFDdEQsU0FBUyxDQUFDLEdBQUcsT0FBTztnQkFDekRnRCxPQUFPeUM7WUFDVDtZQUVBLGtEQUFrRDtZQUNsRCxNQUFNYyxtQkFBbUJkLGVBQWVELHVCQUF1Qm5HLE1BQU0sR0FBRztZQUN4RWhDLFFBQVErRSxHQUFHLENBQUMsd0NBQTRFLE9BQWpEb0QsdUJBQXVCbkcsTUFBTSxHQUFHb0csZUFBZTtZQUV0RixpRkFBaUY7WUFDakYsTUFBTUUsNkJBQTZCSCx1QkFBdUJuRixLQUFLLENBQUMsR0FBR29GLGVBQWU7WUFDbEZ6TCxZQUFZMkw7WUFFWixvREFBb0Q7WUFDcEQvTCxXQUFXOEwsb0JBQW9CcEMsT0FBTztZQUN0Q3hKLGFBQWE7WUFDYmdCLGVBQWU7WUFFZixnRkFBZ0Y7WUFDaEYsTUFBTWlKLGNBQWM5SyxnRUFBU0EsQ0FBQ29LLGlCQUFpQjtZQUMvQ3JJLHNCQUFzQitJO1lBRXRCLDZFQUE2RTtZQUM3RSxJQUFJd0Msa0JBQWtCO2dCQUNwQmxKLFFBQVErRSxHQUFHLENBQUMsMkJBQWtFLE9BQWpEb0QsdUJBQXVCbkcsTUFBTSxHQUFHb0csZUFBZSxHQUFFO2dCQUM5RSxJQUFLLElBQUlHLElBQUlILGVBQWUsR0FBR0csSUFBSUosdUJBQXVCbkcsTUFBTSxFQUFFdUcsSUFBSztvQkFDckUsTUFBTUMsY0FBY0wsc0JBQXNCLENBQUNJLEVBQUU7b0JBQzdDdkksUUFBUStFLEdBQUcsQ0FBQyxxQ0FBMkJ5RCxZQUFZekcsRUFBRTtvQkFDckQsTUFBTW5HLGdFQUFTQSxDQUFDb00sYUFBYSxDQUFDcEosVUFBVTlCLGNBQWMwTCxZQUFZekcsRUFBRTtnQkFDdEU7WUFDRjtZQUVBLHNDQUFzQztZQUN0QyxNQUFNbkcsZ0VBQVNBLENBQUMrSyxlQUFlLENBQzdCO2dCQUNFL0gsVUFBVUE7Z0JBQ1Z1QixRQUFRckQ7Z0JBQ1JSLFNBQVMrTCxvQkFBb0JwQyxPQUFPO2dCQUNwQ1csT0FBT2hLO2dCQUNQNkwsZ0JBQWdCO2dCQUNoQjVCLGVBQWV3QixvQkFBb0J0RyxFQUFFO1lBQ3ZDLEdBQ0Esd0VBQXdFO1lBQ3hFLENBQUMrRTtnQkFDQ25LLFlBQVkySixDQUFBQTtvQkFDViwwQ0FBMEM7b0JBQzFDLE1BQU1TLHVCQUF1QlQsS0FBS1QsU0FBUyxDQUFDbUIsQ0FBQUEsTUFBT0EsSUFBSWpGLEVBQUUsS0FBSzJFO29CQUU5RCxJQUFJSyx5QkFBeUIsQ0FBQyxHQUFHO3dCQUMvQiwrQkFBK0I7d0JBQy9CLE9BQU9ULEtBQUtXLEdBQUcsQ0FBQ0QsQ0FBQUEsTUFDZEEsSUFBSWpGLEVBQUUsS0FBSzJFLGNBQ1A7Z0NBQUUsR0FBR00sR0FBRztnQ0FBRWYsU0FBU2UsSUFBSWYsT0FBTyxHQUFHYTs0QkFBTSxJQUN2Q0U7b0JBRVIsT0FBTzt3QkFDTCw4Q0FBOEM7d0JBQzlDLG1FQUFtRTt3QkFDbkV2SyxhQUFhO3dCQUViLE1BQU15SyxZQUFxQjs0QkFDekJuRixJQUFJMkU7NEJBQ0pULFNBQVNhOzRCQUNUWixRQUFROzRCQUNSM0QsV0FBVyxJQUFJaEMsT0FBT3FDLFdBQVc7NEJBQ2pDdUUsY0FBYzt3QkFDaEI7d0JBQ0EsT0FBTzsrQkFBSWI7NEJBQU1ZO3lCQUFVO29CQUM3QjtnQkFDRjtZQUNGLEdBQ0EsbUNBQW1DO1lBQ25DLENBQUNFO2dCQUNDekssWUFBWTJKLENBQUFBLE9BQVFBLEtBQUtXLEdBQUcsQ0FBQ0QsQ0FBQUEsTUFDM0JBLElBQUlqRixFQUFFLEtBQUsyRSxjQUNQOzRCQUFFLEdBQUdNLEdBQUc7NEJBQUVmLFNBQVNtQjt3QkFBYSxJQUNoQ0o7Z0JBRU52SyxhQUFhO2dCQUNiZ0IsZUFBZTtnQkFDZkUsc0JBQXNCO2dCQUN0QnBCLFdBQVcsS0FBSywwQkFBMEI7Z0JBRTFDLDJFQUEyRTtnQkFDM0UsSUFBSVAsMkJBQTJCO29CQUM3QnFMLFdBQVc7d0JBQ1RyTDtvQkFDRixHQUFHO2dCQUNMO1lBQ0YsR0FDQSx5QkFBeUI7WUFDekIsQ0FBQytEO2dCQUNDQyxRQUFRRCxLQUFLLENBQUMsOEJBQXdCQTtnQkFDdENwRCxZQUFZMkosQ0FBQUEsT0FBUUEsS0FBS1csR0FBRyxDQUFDRCxDQUFBQSxNQUMzQkEsSUFBSWpGLEVBQUUsS0FBSzJFLGNBQ1A7NEJBQUUsR0FBR00sR0FBRzs0QkFBRWYsU0FBUyxnQ0FBZ0MsT0FBTmxHO3dCQUFRLElBQ3JEaUg7Z0JBRU52SyxhQUFhO2dCQUNiZ0IsZUFBZTtnQkFDZkUsc0JBQXNCO2dCQUN0QnBCLFdBQVcsS0FBSywwQkFBMEI7WUFDNUM7UUFHSixFQUFFLE9BQU93RCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywwQ0FBMENBO1lBQ3hEdEQsYUFBYTtZQUNiZ0IsZUFBZTtZQUNmRSxzQkFBc0I7WUFDdEJwQixXQUFXLEtBQUssMEJBQTBCO1lBRTFDLHVDQUF1QztZQUN2Q2tMLGlCQUFpQjNLO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNcU0sb0JBQW9CLENBQUNsRDtRQUN6Qm1ELFVBQVVDLFNBQVMsQ0FBQ0MsU0FBUyxDQUFDckQsU0FBU3NELElBQUksQ0FBQztZQUMxQ3ZKLFFBQVErRSxHQUFHLENBQUM7UUFDZDtJQUNGO0lBRUEsdUJBQXVCO0lBQ3ZCLE1BQU15RSxvQkFBb0I7WUFDQXJMO1FBQXhCLE1BQU1zTCxtQkFBa0J0TCw0QkFBQUEsaUJBQWlCcUksT0FBTyxjQUF4QnJJLGdEQUFBQSwwQkFBMEJ1TCxhQUFhLENBQUM7UUFDaEUsSUFBSUQsaUJBQWlCO1lBQ25CQSxnQkFBZ0JFLFFBQVEsQ0FBQztnQkFBRUMsS0FBSztnQkFBR0MsVUFBVTtZQUFTO1FBQ3hEO0lBQ0Y7SUFFQSxNQUFNQyx1QkFBdUI7WUFDSDNMO1FBQXhCLE1BQU1zTCxtQkFBa0J0TCw0QkFBQUEsaUJBQWlCcUksT0FBTyxjQUF4QnJJLGdEQUFBQSwwQkFBMEJ1TCxhQUFhLENBQUM7UUFDaEUsSUFBSUQsaUJBQWlCO1lBQ25CQSxnQkFBZ0JFLFFBQVEsQ0FBQztnQkFDdkJDLEtBQUtILGdCQUFnQk0sWUFBWTtnQkFDakNGLFVBQVU7WUFDWjtRQUNGO0lBQ0Y7SUFJQSw2REFBNkQ7SUFDN0QsTUFBTUcsd0JBQXdCLENBQUN0TjtRQUM3QixPQUFPQSxTQUFTdUssR0FBRyxDQUFDRCxDQUFBQSxNQUFRO2dCQUMxQmpGLElBQUlpRixJQUFJakYsRUFBRTtnQkFDVmtFLFNBQVNlLElBQUlmLE9BQU87Z0JBQ3BCZ0UsTUFBTWpELElBQUlkLE1BQU0sS0FBSyxTQUFTLFNBQVM7Z0JBQ3ZDM0QsV0FBVyxJQUFJaEMsS0FBS3lHLElBQUl6RSxTQUFTLEVBQUUySCxPQUFPO2dCQUMxQ0MsWUFBWW5ELElBQUltRCxVQUFVLElBQUk7Z0JBQzlCbEYsYUFBYStCLElBQUkvQixXQUFXLElBQUksRUFBRTtZQUNwQztJQUNGO0lBRUEsd0NBQXdDO0lBQ3hDLE1BQU1tRixzQkFBc0IsQ0FBQ2pLO1FBQzNCLDZGQUE2RjtRQUM3RixJQUFJQSxVQUFVQSxXQUFXckUsZUFBZUcsY0FBYztZQUNwREEsYUFBYWtFO1lBQ2IsNEVBQTRFO1lBQzVFa0gsV0FBVztnQkFDVHBLLHVCQUF1QjtZQUN6QixHQUFHO1FBQ0wsT0FBTztZQUNMQSx1QkFBdUI7UUFDekI7SUFDRjtJQUVBLHNDQUFzQztJQUN0QyxNQUFNb04seUJBQXlCLENBQUNsSztRQUM5Qiw2RkFBNkY7UUFDN0YsSUFBSUEsVUFBVUEsV0FBV3JFLGVBQWVHLGNBQWM7WUFDcERBLGFBQWFrRTtZQUNia0gsV0FBVztnQkFDVGhLLDBCQUEwQjtZQUM1QixHQUFHO1FBQ0wsT0FBTztZQUNMQSwwQkFBMEI7UUFDNUI7SUFDRjtJQUVBLDRDQUE0QztJQUM1QyxNQUFNaU4sd0JBQXdCLENBQUNuSztRQUM3Qiw2RkFBNkY7UUFDN0YsSUFBSUEsVUFBVUEsV0FBV3JFLGVBQWVHLGNBQWM7WUFDcERBLGFBQWFrRTtZQUNia0gsV0FBVztnQkFDVDlKLHlCQUF5QjtZQUMzQixHQUFHO1FBQ0wsT0FBTztZQUNMQSx5QkFBeUI7UUFDM0I7SUFDRjtJQUVBLHlDQUF5QztJQUN6Q2pELDBEQUFtQkEsQ0FBQ1MsS0FBSyxJQUFPO1lBQzlCcVA7WUFDQUM7WUFDQUM7UUFDRjtJQUVBLDRDQUE0QztJQUM1QyxNQUFNbEYsd0JBQXdCO1FBQzVCLE1BQU1tRixpQkFBa0UsRUFBRTtRQUUxRTdOLFNBQVM4TixPQUFPLENBQUNsTyxDQUFBQTtZQUNmLElBQUlBLFFBQVEySSxXQUFXLElBQUkzSSxRQUFRMkksV0FBVyxDQUFDakQsTUFBTSxHQUFHLEdBQUc7Z0JBQ3pEdUksZUFBZXpJLElBQUksSUFBSXhGLFFBQVEySSxXQUFXO1lBQzVDO1FBQ0Y7UUFFQSxtQ0FBbUM7UUFDbkMsTUFBTVEsb0JBQW9COEUsZUFBZWxGLE1BQU0sQ0FBQyxDQUFDSyxZQUFZQyxPQUFPQyxPQUNsRUQsVUFBVUMsS0FBS0MsU0FBUyxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFL0QsRUFBRSxLQUFLMkQsV0FBVzNELEVBQUU7UUFHdEQsT0FBTzBEO0lBQ1Q7SUFFQSw2REFBNkQ7SUFDN0QsTUFBTWdGLHVCQUF1QixPQUFPQztRQUNsQyxJQUFJLENBQUMxTSxtQkFBbUIsQ0FBQ2xCLGNBQWM7UUFFdkMsSUFBSTtZQUNGLHFDQUFxQztZQUNyQyxNQUFNMEssV0FBVztnQkFDZnpGLElBQUlqRjtnQkFDSjhHLE1BQU1oRyxZQUFZO2dCQUNsQmxCLFVBQVVnTyxnQkFBZ0J6RCxHQUFHLENBQUNELENBQUFBLE1BQVE7d0JBQ3BDakYsSUFBSWlGLElBQUlqRixFQUFFO3dCQUNWa0UsU0FBU2UsSUFBSWYsT0FBTzt3QkFDcEJnRSxNQUFNakQsSUFBSWQsTUFBTSxLQUFLLFNBQVMsU0FBUzt3QkFDdkMzRCxXQUFXeUUsSUFBSXpFLFNBQVM7d0JBQ3hCNEgsWUFBWW5ELElBQUltRCxVQUFVO3dCQUMxQmxGLGFBQWErQixJQUFJL0IsV0FBVztvQkFDOUI7Z0JBQ0E1QixXQUFXLElBQUk5QyxPQUFPcUMsV0FBVztnQkFDakNxQixhQUFhLElBQUkxRCxPQUFPcUMsV0FBVztZQUNyQztZQUVBLDZCQUE2QjtZQUM3QixNQUFNNkIsZUFBZSxJQUFJQyxLQUFLO2dCQUFDQyxLQUFLQyxTQUFTLENBQUM0QyxVQUFVLE1BQU07YUFBRyxFQUFFO2dCQUNqRTNDLE1BQU07WUFDUjtZQUVBLE1BQU04RixjQUFjM1AscURBQVVBLENBQUNHLGtEQUFPQSxFQUFFLFlBQXlDMkIsT0FBN0JrQixpQkFBZ0IsZUFBMEIsT0FBYmxCLGNBQWE7WUFDOUYsTUFBTTdCLDZEQUFXQSxDQUFDMFAsYUFBYWxHO1lBRS9CekUsUUFBUStFLEdBQUcsQ0FBQztZQUNaL0UsUUFBUStFLEdBQUcsQ0FBQyw4QkFBb0I7Z0JBQzlCNUUsUUFBUXJEO2dCQUNSOE4sZUFBZXBELFNBQVM5SyxRQUFRLENBQUNzRixNQUFNO2dCQUN2QzZJLHlCQUF5QnJELFNBQVM5SyxRQUFRLENBQUMySSxNQUFNLENBQUMyQixDQUFBQSxNQUFPQSxJQUFJL0IsV0FBVyxJQUFJK0IsSUFBSS9CLFdBQVcsQ0FBQ2pELE1BQU0sR0FBRyxHQUFHQSxNQUFNO1lBQ2hIO1FBQ0YsRUFBRSxPQUFPakMsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsdUNBQXVDQTtRQUN2RDtJQUNGO0lBRUEsa0RBQWtEO0lBQ2xELE1BQU0rSyx5QkFBeUIsQ0FBQ0M7UUFDOUJwTyxZQUFZcU8sQ0FBQUE7WUFDVixNQUFNTixrQkFBa0JNLGFBQWEvRCxHQUFHLENBQUMzSyxDQUFBQTtnQkFDdkMsSUFBSUEsUUFBUTJJLFdBQVcsSUFBSTNJLFFBQVEySSxXQUFXLENBQUNqRCxNQUFNLEdBQUcsR0FBRztvQkFDekQsTUFBTWlKLHFCQUFxQjNPLFFBQVEySSxXQUFXLENBQUNnQyxHQUFHLENBQUN2QixDQUFBQTt3QkFDakQsSUFBSUEsV0FBVzNELEVBQUUsS0FBS2dKLGNBQWM7NEJBQ2xDLHlFQUF5RTs0QkFDekUsTUFBTUcsZUFBZXhGLFdBQVdILFFBQVEsS0FBSzs0QkFDN0MsT0FBTztnQ0FBRSxHQUFHRyxVQUFVO2dDQUFFSCxVQUFVLENBQUMyRjs0QkFBYTt3QkFDbEQ7d0JBQ0EsT0FBT3hGO29CQUNUO29CQUNBLE9BQU87d0JBQUUsR0FBR3BKLE9BQU87d0JBQUUySSxhQUFhZ0c7b0JBQW1CO2dCQUN2RDtnQkFDQSxPQUFPM087WUFDVDtZQUVBLGlEQUFpRDtZQUNqRG1PLHFCQUFxQkM7WUFFckIsT0FBT0E7UUFDVDtJQUNGO0lBRUEsb0RBQW9EO0lBQ3BELE1BQU1TLHVCQUF1QixDQUFDbEc7UUFDNUIsSUFBSSxDQUFDQSxhQUFhLE9BQU8sRUFBRTtRQUUzQixnRUFBZ0U7UUFDaEUsNERBQTREO1FBQzVELE9BQU9BLFlBQVlJLE1BQU0sQ0FBQ0ssQ0FBQUE7WUFDeEIsc0RBQXNEO1lBQ3RELElBQUlBLFdBQVdILFFBQVEsS0FBS3JILFdBQVcsT0FBTztZQUM5Qyw2REFBNkQ7WUFDN0QsT0FBT3dILFdBQVdILFFBQVEsS0FBSztRQUNqQztJQUNGO0lBSUEsMENBQTBDO0lBQzFDLE1BQU02Rix5QkFBeUI7UUFDN0IsTUFBTWIsaUJBQWlCbkY7UUFDdkIsT0FBT21GLGVBQWVsRixNQUFNLENBQUNDLENBQUFBLE1BQU9BLElBQUlDLFFBQVEsS0FBSyxPQUFPMEIsR0FBRyxDQUFDM0IsQ0FBQUEsTUFBT0EsSUFBSXZELEVBQUU7SUFDL0U7SUFFQSxxQ0FBcUM7SUFDckMsTUFBTXNKLGlCQUFpQjtRQUNyQmhOLGNBQWM7UUFDZEUsZUFBZTtRQUNmLElBQUlDLGFBQWE7WUFDZjhNLGFBQWE5TTtZQUNiQyxlQUFlO1FBQ2pCO0lBQ0Y7SUFFQSwyQkFBMkI7SUFDM0IsTUFBTThNLGtCQUFrQixDQUFDeE07UUFDdkJBLEVBQUV5TSxjQUFjO1FBQ2hCek0sRUFBRTBNLGVBQWU7UUFFakJsTixlQUFlK0gsQ0FBQUEsT0FBUUEsT0FBTztRQUU5Qiw0Q0FBNEM7UUFDNUMsSUFBSXZILEVBQUUyTSxZQUFZLENBQUNDLEtBQUssSUFBSTVNLEVBQUUyTSxZQUFZLENBQUNDLEtBQUssQ0FBQzNKLE1BQU0sR0FBRyxHQUFHO1lBQzNELCtDQUErQztZQUMvQyxNQUFNNEosV0FBV0MsTUFBTUMsSUFBSSxDQUFDL00sRUFBRTJNLFlBQVksQ0FBQ0MsS0FBSyxFQUFFSSxJQUFJLENBQUNDLENBQUFBLE9BQVFBLEtBQUtDLElBQUksS0FBSztZQUM3RSxJQUFJTCxVQUFVO2dCQUNadk4sY0FBYztnQkFFZCxxQ0FBcUM7Z0JBQ3JDLElBQUlHLGFBQWE7b0JBQ2Y4TSxhQUFhOU07Z0JBQ2Y7Z0JBRUEscUVBQXFFO2dCQUNyRSxNQUFNME4sVUFBVTdFLFdBQVc7b0JBQ3pCZ0U7Z0JBQ0YsR0FBRztnQkFDSDVNLGVBQWV5TjtZQUNqQjtRQUNGO0lBQ0Y7SUFFQSxNQUFNQyxrQkFBa0IsQ0FBQ3BOO1FBQ3ZCQSxFQUFFeU0sY0FBYztRQUNoQnpNLEVBQUUwTSxlQUFlO1FBRWpCbE4sZUFBZStILENBQUFBO1lBQ2IsTUFBTThGLFdBQVc5RixPQUFPO1lBQ3hCLGtEQUFrRDtZQUNsRCxJQUFJOEYsWUFBWSxHQUFHO2dCQUNqQmY7Z0JBQ0EsT0FBTztZQUNUO1lBQ0EsT0FBT2U7UUFDVDtJQUNGO0lBRUEsTUFBTUMsaUJBQWlCLENBQUN0TjtRQUN0QkEsRUFBRXlNLGNBQWM7UUFDaEJ6TSxFQUFFME0sZUFBZTtRQUVqQiw0Q0FBNEM7UUFDNUMsSUFBSTFNLEVBQUUyTSxZQUFZLENBQUNDLEtBQUssSUFBSTVNLEVBQUUyTSxZQUFZLENBQUNDLEtBQUssQ0FBQzNKLE1BQU0sR0FBRyxHQUFHO1lBQzNELE1BQU00SixXQUFXQyxNQUFNQyxJQUFJLENBQUMvTSxFQUFFMk0sWUFBWSxDQUFDQyxLQUFLLEVBQUVJLElBQUksQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS0MsSUFBSSxLQUFLO1lBQzdFLElBQUlMLFVBQVU7Z0JBQ1o3TSxFQUFFMk0sWUFBWSxDQUFDWSxVQUFVLEdBQUc7WUFDOUIsT0FBTztnQkFDTHZOLEVBQUUyTSxZQUFZLENBQUNZLFVBQVUsR0FBRztZQUM5QjtRQUNGO0lBQ0Y7SUFFQSxNQUFNQyxhQUFhLE9BQU94TjtRQUN4QkEsRUFBRXlNLGNBQWM7UUFDaEJ6TSxFQUFFME0sZUFBZTtRQUVqQixzRUFBc0U7UUFDdEVKO1FBRUEsTUFBTW1CLFFBQVFYLE1BQU1DLElBQUksQ0FBQy9NLEVBQUUyTSxZQUFZLENBQUNjLEtBQUs7UUFFN0Msa0NBQWtDO1FBQ2xDLElBQUlBLE1BQU14SyxNQUFNLEtBQUssR0FBRztZQUN0QjtRQUNGO1FBRUEseUNBQXlDO1FBQ3pDLElBQUksQ0FBQ2hFLGlCQUFpQjtZQUNwQmdDLFFBQVFELEtBQUssQ0FBQztZQUNkO1FBQ0Y7UUFFQSxvRkFBb0Y7UUFDcEYsSUFBSW9HLGNBQWNySjtRQUNsQixJQUFJLENBQUNxSixhQUFhO1lBQ2hCLE1BQU1DLGlCQUFpQm9HLE1BQU14SyxNQUFNLEtBQUssSUFDcEMsb0JBQWtDLE9BQWR3SyxLQUFLLENBQUMsRUFBRSxDQUFDNUksSUFBSSxJQUNqQyxHQUFnQixPQUFiNEksTUFBTXhLLE1BQU0sRUFBQztZQUNwQm1FLGNBQWMsTUFBTTlELGVBQWUrRDtZQUNuQyxJQUFJRCxhQUFhO2dCQUNmcEosZ0JBQWdCb0o7Z0JBQ2hCRSxhQUFhRjtnQkFDYnBLLDBCQUFBQSxvQ0FBQUEsY0FBZ0JvSztZQUNsQjtRQUNGO1FBRUEsSUFBSSxDQUFDQSxhQUFhO1lBQ2hCbkcsUUFBUUQsS0FBSyxDQUFDO1lBQ2Q7UUFDRjtRQUVBLElBQUk7WUFDRiw2Q0FBNkM7WUFDN0MsTUFBTSxFQUFFME0sU0FBU0MsaUJBQWlCLEVBQUUsR0FBRyxNQUFNLDJMQUFPO1lBRXBELDRCQUE0QjtZQUM1QixNQUFNQyxzQkFBc0IsTUFBTUQsa0JBQWtCRSx5QkFBeUIsQ0FDM0VKLE9BQ0F4TyxpQkFDQW1JO1lBR0YsZ0ZBQWdGO1lBQ2hGLG1EQUFtRDtZQUNuRCxNQUFNMEcscUJBQXFCRixvQkFBb0IxRixHQUFHLENBQUMzQixDQUFBQSxNQUFPQSxJQUFJd0gsUUFBUTtZQUV0RSx1REFBdUQ7WUFDdkQsTUFBTUMsUUFBUSxJQUFJQyxZQUFZLHVCQUF1QjtnQkFDbkRDLFFBQVE7b0JBQ05oSSxhQUFhNEg7b0JBQ2IxTSxRQUFRZ0c7b0JBQ1J2SCxVQUFVWjtnQkFDWjtZQUNGO1lBQ0FtQixPQUFPK04sYUFBYSxDQUFDSDtZQUVyQi9NLFFBQVErRSxHQUFHLENBQUMsS0FBa0IsT0FBYnlILE1BQU14SyxNQUFNLEVBQUM7UUFFaEMsRUFBRSxPQUFPakMsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsaURBQWlEQTtRQUNqRTtJQUNGO0lBRUEscUJBQ0UsOERBQUNvTjtRQUNDQyxXQUFVO1FBQ1ZDLGFBQWE5QjtRQUNiK0IsYUFBYW5CO1FBQ2JvQixZQUFZbEI7UUFDWm1CLFFBQVFqQjs7WUFHUG5PLDRCQUNDLDhEQUFDK087Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDSztvQ0FBSUwsV0FBVTtvQ0FBaURNLE1BQUs7b0NBQU9DLFFBQU87b0NBQWVDLFNBQVE7OENBQ3hHLDRFQUFDQzt3Q0FBS0MsZUFBYzt3Q0FBUUMsZ0JBQWU7d0NBQVFDLGFBQWE7d0NBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7MENBR3pFLDhEQUFDQztnQ0FBR2QsV0FBVTswQ0FBMkM7Ozs7OzswQ0FHekQsOERBQUNlO2dDQUFFZixXQUFVOzBDQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFXN0MsOERBQUNEO2dCQUFJcFMsS0FBS29EO2dCQUFrQmlQLFdBQVU7Z0JBQWlCZ0IsT0FBTztvQkFBRUMsUUFBUTtnQkFBc0I7MEJBQzVGLDRFQUFDL1Msc0RBQWFBO29CQUNaNkUsUUFBUXJELGdCQUFnQjtvQkFDeEJKLFVBQVVBO29CQUNWRixXQUFXQTtvQkFDWHNCLGVBQWVBO29CQUNmTixhQUFhQTtvQkFDYkUsb0JBQW9CQSxzQkFBc0JRO29CQUMxQ29RLGdCQUFnQmpTLG1CQUFtQmtTLGVBQWU7b0JBQ2xEQyxpQkFBaUIzRztvQkFDakI0RyxxQkFBcUJ4RztvQkFDckJ5RyxlQUFlaEc7b0JBQ2ZpRyxxQkFBcUI1RjtvQkFDckI2RixlQUFlekY7Ozs7Ozs7Ozs7OzBCQUtuQiw4REFBQzVOLGlEQUFRQTtnQkFDUGUsU0FBU0E7Z0JBQ1RDLFlBQVlBO2dCQUNac1MsZUFBZTdKO2dCQUNmeEksV0FBV0E7Z0JBQ1hJLGVBQWVBO2dCQUNma1MsZUFBZTFNO2dCQUNmMk0sZUFBZXZGO2dCQUNmd0Ysa0JBQWtCbEY7Z0JBQ2xCdE0sYUFBYUE7Z0JBQ2J5UixtQkFBbUIzSDtnQkFDbkI0SCxrQkFBa0IsSUFBTS9SLG9CQUFvQjtnQkFDNUNnUyx3QkFBd0I5RTtnQkFDeEJ6TCxVQUFVWjtnQkFDVm1DLFFBQVFyRCxnQkFBZ0JvQjtnQkFDeEJrUix3QkFBd0JoRSx5QkFBeUJwSixNQUFNOzs7Ozs7MEJBSXpELDhEQUFDeEcsc0RBQWFBO2dCQUNaNlQsUUFBUXJTO2dCQUNSc1MsU0FBUyxJQUFNclMsdUJBQXVCO2dCQUN0Q1AsVUFBVXNOLHNCQUFzQnROO2dCQUNoQ2tCLFVBQVVBOzs7Ozs7MEJBSVosOERBQUNuQyw2REFBbUJBO2dCQUNsQjRULFFBQVFuUztnQkFDUm9TLFNBQVMsSUFBTW5TLG9CQUFvQjtnQkFDbkNvUyxjQUFjM1M7Z0JBQ2Q0UyxlQUFlcE47Ozs7OzswQkFJakIsOERBQUMxRywwREFBZ0JBO2dCQUNmMlQsUUFBUWpTO2dCQUNSa1MsU0FBUyxJQUFNalMsMEJBQTBCO2dCQUN6QzRILGFBQWFHO2dCQUNicUssbUJBQW1CckU7Z0JBQ25Cc0Usb0JBQW9CNUU7Ozs7OzswQkFJdEIsOERBQUNuUCx5REFBZUE7Z0JBQ2QwVCxRQUFRL1I7Z0JBQ1JnUyxTQUFTLElBQU0vUix5QkFBeUI7Z0JBQ3hDYixVQUFVc04sc0JBQXNCdE47Z0JBQ2hDa0IsVUFBVUE7Ozs7Ozs7Ozs7OztBQUlsQjs7UUE3MkNtQnhDLDBEQUFPQTtRQUNpQkMsc0VBQWFBOzs7O1FBRHJDRCwwREFBT0E7UUFDaUJDLHNFQUFhQTs7OztBQTgyQ3hEUSxTQUFTOFQsV0FBVyxHQUFHO0FBRXZCLCtEQUFlOVQsUUFBUUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvQ2hhdEFyZWEudHN4P2M2NmQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VSZWYsIGZvcndhcmRSZWYsIHVzZUltcGVyYXRpdmVIYW5kbGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBkb2MsIHNldERvYywgY29sbGVjdGlvbiwgZ2V0RG9jLCB1cGRhdGVEb2MsIHF1ZXJ5LCB3aGVyZSwgZ2V0RG9jcyB9IGZyb20gJ2ZpcmViYXNlL2ZpcmVzdG9yZSc7XG5pbXBvcnQgeyByZWYgYXMgc3RvcmFnZVJlZiwgdXBsb2FkQnl0ZXMgfSBmcm9tICdmaXJlYmFzZS9zdG9yYWdlJztcbmltcG9ydCB7IGRiLCBzdG9yYWdlIH0gZnJvbSAnQC9saWIvZmlyZWJhc2UnO1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnO1xuaW1wb3J0IHsgdXNlQXBwZWFyYW5jZSB9IGZyb20gJ0AvY29udGV4dHMvQXBwZWFyYW5jZUNvbnRleHQnO1xuXG5pbXBvcnQgQ2hhdEludGVyZmFjZSBmcm9tICcuL0NoYXRJbnRlcmZhY2UnO1xuaW1wb3J0IElucHV0QmFyIGZyb20gJy4vSW5wdXRCYXInO1xuaW1wb3J0IEF0dGFjaG1lbnREaXNwbGF5IGZyb20gJy4vQXR0YWNobWVudERpc3BsYXknO1xuaW1wb3J0IERvd25sb2FkTW9kYWwgZnJvbSAnLi9Eb3dubG9hZE1vZGFsJztcbmltcG9ydCBNb2RlbFNlbGVjdGlvbk1vZGFsIGZyb20gJy4vTW9kZWxTZWxlY3Rpb25Nb2RhbCc7XG5pbXBvcnQgQXR0YWNobWVudHNNb2RhbCBmcm9tICcuL0F0dGFjaG1lbnRzTW9kYWwnO1xuaW1wb3J0IFN0YXRpc3RpY3NNb2RhbCBmcm9tICcuL1N0YXRpc3RpY3NNb2RhbCc7XG5pbXBvcnQgeyBDaGF0TWVzc2FnZSB9IGZyb20gJ0AvbGliL3R5cGVzL2NoYXQnO1xuaW1wb3J0IGFpU2VydmljZSBmcm9tICdAL2xpYi9zZXJ2aWNlcy9haVNlcnZpY2UnO1xuXG5pbnRlcmZhY2UgV2ViU2VhcmNoQW5ub3RhdGlvbiB7XG4gIHR5cGU6IFwidXJsX2NpdGF0aW9uXCI7XG4gIHVybDogc3RyaW5nO1xuICB0aXRsZTogc3RyaW5nO1xuICBjb250ZW50Pzogc3RyaW5nO1xuICBzdGFydF9pbmRleDogbnVtYmVyO1xuICBlbmRfaW5kZXg6IG51bWJlcjtcbn1cblxuaW50ZXJmYWNlIE1lc3NhZ2Uge1xuICBpZDogc3RyaW5nO1xuICBjb250ZW50OiBzdHJpbmc7XG4gIHNlbmRlcjogJ3VzZXInIHwgJ2FpJztcbiAgdGltZXN0YW1wOiBzdHJpbmc7XG4gIGlzRmF2b3JpdGU/OiBib29sZWFuO1xuICBhdHRhY2htZW50cz86IGltcG9ydCgnQC9saWIvdHlwZXMvY2hhdCcpLkF0dGFjaG1lbnRNZXRhZGF0YVtdO1xuICBoYXNXZWJTZWFyY2g/OiBib29sZWFuO1xuICB3ZWJTZWFyY2hBbm5vdGF0aW9ucz86IFdlYlNlYXJjaEFubm90YXRpb25bXTtcbn1cblxuaW50ZXJmYWNlIENoYXRBcmVhUHJvcHMge1xuICBjdXJyZW50Q2hhdDogc3RyaW5nIHwgbnVsbDtcbiAgb25DaGF0Q3JlYXRlZD86IChjaGF0SWQ6IHN0cmluZykgPT4gdm9pZDtcbiAgb25VcGRhdGVPcGVuUm91dGVyQmFsYW5jZT86ICgpID0+IHZvaWQ7XG4gIG9uQ2hhdFNlbGVjdD86IChjaGF0SWQ6IHN0cmluZykgPT4gdm9pZDtcbiAgc2lkZWJhclJlZj86IFJlYWN0LlJlZk9iamVjdDx7IHJlbG9hZENoYXRzOiAoKSA9PiB2b2lkOyB1cGRhdGVPcGVuUm91dGVyQmFsYW5jZTogKCkgPT4gdm9pZDsgbW92ZVRvVG9wOiAoY2hhdElkOiBzdHJpbmcpID0+IHZvaWQgfT47XG59XG5cbmNvbnN0IENoYXRBcmVhID0gZm9yd2FyZFJlZjx7XG4gIGhhbmRsZURvd25sb2FkTW9kYWw6IChjaGF0SWQ/OiBzdHJpbmcpID0+IHZvaWQ7XG4gIGhhbmRsZUF0dGFjaG1lbnRzTW9kYWw6IChjaGF0SWQ/OiBzdHJpbmcpID0+IHZvaWQ7XG4gIGhhbmRsZVN0YXRpc3RpY3NNb2RhbDogKGNoYXRJZD86IHN0cmluZykgPT4gdm9pZDtcbn0sIENoYXRBcmVhUHJvcHM+KCh7IGN1cnJlbnRDaGF0LCBvbkNoYXRDcmVhdGVkLCBvblVwZGF0ZU9wZW5Sb3V0ZXJCYWxhbmNlLCBvbkNoYXRTZWxlY3QsIHNpZGViYXJSZWYgfSwgcmVmKSA9PiB7XG4gIGNvbnN0IHsgdXNlciB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCB7IHNldHRpbmdzOiBhcHBlYXJhbmNlU2V0dGluZ3MgfSA9IHVzZUFwcGVhcmFuY2UoKTtcbiAgY29uc3QgW21lc3NhZ2UsIHNldE1lc3NhZ2VdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbbWVzc2FnZXMsIHNldE1lc3NhZ2VzXSA9IHVzZVN0YXRlPE1lc3NhZ2VbXT4oW10pO1xuICBjb25zdCBbc2VsZWN0ZWRNb2RlbCwgc2V0U2VsZWN0ZWRNb2RlbF0gPSB1c2VTdGF0ZSgnbWV0YS1sbGFtYS9sbGFtYS0zLjEtOGItaW5zdHJ1Y3Q6ZnJlZScpO1xuXG4gIGNvbnN0IFthY3R1YWxDaGF0SWQsIHNldEFjdHVhbENoYXRJZF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihjdXJyZW50Q2hhdCk7XG4gIGNvbnN0IFtpc0Rvd25sb2FkTW9kYWxPcGVuLCBzZXRJc0Rvd25sb2FkTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzTW9kZWxNb2RhbE9wZW4sIHNldElzTW9kZWxNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNBdHRhY2htZW50c01vZGFsT3Blbiwgc2V0SXNBdHRhY2htZW50c01vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc1N0YXRpc3RpY3NNb2RhbE9wZW4sIHNldElzU3RhdGlzdGljc01vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc1N0cmVhbWluZywgc2V0SXNTdHJlYW1pbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc3RyZWFtaW5nTWVzc2FnZUlkLCBzZXRTdHJlYW1pbmdNZXNzYWdlSWRdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtjaGF0TmFtZSwgc2V0Q2hhdE5hbWVdID0gdXNlU3RhdGU8c3RyaW5nPignTm92YSBDb252ZXJzYScpO1xuXG5cbiAgY29uc3QgW2lzTG9hZGluZ0NoYXQsIHNldElzTG9hZGluZ0NoYXRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbY3VycmVudFVzZXJuYW1lLCBzZXRDdXJyZW50VXNlcm5hbWVdID0gdXNlU3RhdGU8c3RyaW5nIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuICBjb25zdCBjaGF0SW50ZXJmYWNlUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcblxuICAvLyBFc3RhZG9zIHBhcmEgZHJhZy1uLWRyb3BcbiAgY29uc3QgW2lzRHJhZ092ZXIsIHNldElzRHJhZ092ZXJdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZHJhZ0NvdW50ZXIsIHNldERyYWdDb3VudGVyXSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbZHJhZ1RpbWVvdXQsIHNldERyYWdUaW1lb3V0XSA9IHVzZVN0YXRlPE5vZGVKUy5UaW1lb3V0IHwgbnVsbD4obnVsbCk7XG5cbiAgLy8gQ2FycmVnYXIgdXNlcm5hbWUgcXVhbmRvIG8gdXN1w6FyaW8gZXN0aXZlciBkaXNwb27DrXZlbFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGxvYWRVc2VybmFtZSA9IGFzeW5jICgpID0+IHtcbiAgICAgIGlmICh1c2VyPy5lbWFpbCkge1xuICAgICAgICBjb25zdCB1c2VybmFtZSA9IGF3YWl0IGdldFVzZXJuYW1lRnJvbUZpcmVzdG9yZSgpO1xuICAgICAgICBzZXRDdXJyZW50VXNlcm5hbWUodXNlcm5hbWUpO1xuICAgICAgfVxuICAgIH07XG4gICAgbG9hZFVzZXJuYW1lKCk7XG4gIH0sIFt1c2VyPy5lbWFpbF0pO1xuXG4gIC8vIERldGVjdGFyIHF1YW5kbyBvIGRyYWcgc2FpIGRhIGphbmVsYSBwYXJhIHJlc2V0YXIgbyBlc3RhZG9cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVXaW5kb3dEcmFnTGVhdmUgPSAoZTogRHJhZ0V2ZW50KSA9PiB7XG4gICAgICAvLyBTZSBvIGRyYWcgc2FpIGRhIGphbmVsYSAocmVsYXRlZFRhcmdldCDDqSBudWxsKSwgcmVzZXRhciBvIGVzdGFkb1xuICAgICAgaWYgKCFlLnJlbGF0ZWRUYXJnZXQpIHtcbiAgICAgICAgc2V0SXNEcmFnT3ZlcihmYWxzZSk7XG4gICAgICAgIHNldERyYWdDb3VudGVyKDApO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBjb25zdCBoYW5kbGVXaW5kb3dEcmFnRW5kID0gKCkgPT4ge1xuICAgICAgLy8gUmVzZXRhciBlc3RhZG8gcXVhbmRvIG8gZHJhZyB0ZXJtaW5hXG4gICAgICBzZXRJc0RyYWdPdmVyKGZhbHNlKTtcbiAgICAgIHNldERyYWdDb3VudGVyKDApO1xuICAgIH07XG5cbiAgICBjb25zdCBoYW5kbGVXaW5kb3dEcm9wID0gKCkgPT4ge1xuICAgICAgLy8gUmVzZXRhciBlc3RhZG8gcXVhbmRvIGjDoSBkcm9wIGVtIHF1YWxxdWVyIGx1Z2FyXG4gICAgICBzZXRJc0RyYWdPdmVyKGZhbHNlKTtcbiAgICAgIHNldERyYWdDb3VudGVyKDApO1xuICAgIH07XG5cbiAgICAvLyBBZGljaW9uYXIgbGlzdGVuZXJzIGdsb2JhaXNcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignZHJhZ2xlYXZlJywgaGFuZGxlV2luZG93RHJhZ0xlYXZlKTtcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignZHJhZ2VuZCcsIGhhbmRsZVdpbmRvd0RyYWdFbmQpO1xuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdkcm9wJywgaGFuZGxlV2luZG93RHJvcCk7XG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2RyYWdsZWF2ZScsIGhhbmRsZVdpbmRvd0RyYWdMZWF2ZSk7XG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignZHJhZ2VuZCcsIGhhbmRsZVdpbmRvd0RyYWdFbmQpO1xuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2Ryb3AnLCBoYW5kbGVXaW5kb3dEcm9wKTtcbiAgICB9O1xuICB9LCBbXSk7XG5cbiAgLy8gRnVuw6fDo28gdXRpbGl0w6FyaWEgcGFyYSBidXNjYXIgdXNlcm5hbWUgY29ycmV0b1xuICBjb25zdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUgPSBhc3luYyAoKTogUHJvbWlzZTxzdHJpbmc+ID0+IHtcbiAgICBpZiAoIXVzZXI/LmVtYWlsKSByZXR1cm4gJ3Vua25vd24nO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzdWFyaW9zUmVmID0gY29sbGVjdGlvbihkYiwgJ3VzdWFyaW9zJyk7XG4gICAgICBjb25zdCBxID0gcXVlcnkodXN1YXJpb3NSZWYsIHdoZXJlKCdlbWFpbCcsICc9PScsIHVzZXIuZW1haWwpKTtcbiAgICAgIGNvbnN0IHF1ZXJ5U25hcHNob3QgPSBhd2FpdCBnZXREb2NzKHEpO1xuXG4gICAgICBpZiAoIXF1ZXJ5U25hcHNob3QuZW1wdHkpIHtcbiAgICAgICAgY29uc3QgdXNlckRvYyA9IHF1ZXJ5U25hcHNob3QuZG9jc1swXTtcbiAgICAgICAgY29uc3QgdXNlckRhdGEgPSB1c2VyRG9jLmRhdGEoKTtcbiAgICAgICAgcmV0dXJuIHVzZXJEYXRhLnVzZXJuYW1lIHx8IHVzZXIuZW1haWwuc3BsaXQoJ0AnKVswXTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHVzZXIuZW1haWwuc3BsaXQoJ0AnKVswXTsgLy8gZmFsbGJhY2tcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBidXNjYXIgdXNlcm5hbWU6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIHVzZXIuZW1haWwuc3BsaXQoJ0AnKVswXTsgLy8gZmFsbGJhY2tcbiAgICB9XG4gIH07XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBzYWx2YXIgbyDDumx0aW1vIG1vZGVsbyB1c2FkbyBwYXJhIHVtIGNoYXQgZXNwZWPDrWZpY29cbiAgY29uc3Qgc2F2ZUxhc3RVc2VkTW9kZWxGb3JDaGF0ID0gYXN5bmMgKG1vZGVsSWQ6IHN0cmluZywgY2hhdElkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXVzZXIgfHwgIWNoYXRJZCkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG4gICAgICBjb25zdCBjaGF0UmVmID0gZG9jKGRiLCAndXN1YXJpb3MnLCB1c2VybmFtZSwgJ2NvbnZlcnNhcycsIGNoYXRJZCk7XG5cbiAgICAgIC8vIEF0dWFsaXphciBvIGxhc3RVc2VkTW9kZWwgbm8gZG9jdW1lbnRvIGRvIGNoYXRcbiAgICAgIGF3YWl0IHVwZGF0ZURvYyhjaGF0UmVmLCB7XG4gICAgICAgIGxhc3RVc2VkTW9kZWw6IG1vZGVsSWQsXG4gICAgICAgIGxhc3RNb2RlbFVwZGF0ZUF0OiBEYXRlLm5vdygpXG4gICAgICB9KTtcblxuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNhdmluZyBsYXN0IHVzZWQgbW9kZWwgZm9yIGNoYXQ6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIGNhcnJlZ2FyIG8gw7psdGltbyBtb2RlbG8gdXNhZG8gZGUgdW0gY2hhdCBlc3BlY8OtZmljb1xuICBjb25zdCBsb2FkTGFzdFVzZWRNb2RlbEZvckNoYXQgPSBhc3luYyAoY2hhdElkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXVzZXIgfHwgIWNoYXRJZCkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG4gICAgICBjb25zdCBjaGF0UmVmID0gZG9jKGRiLCAndXN1YXJpb3MnLCB1c2VybmFtZSwgJ2NvbnZlcnNhcycsIGNoYXRJZCk7XG4gICAgICBjb25zdCBjaGF0RG9jID0gYXdhaXQgZ2V0RG9jKGNoYXRSZWYpO1xuXG4gICAgICBpZiAoY2hhdERvYy5leGlzdHMoKSkge1xuICAgICAgICBjb25zdCBkYXRhID0gY2hhdERvYy5kYXRhKCk7XG4gICAgICAgIGlmIChkYXRhLmxhc3RVc2VkTW9kZWwpIHtcbiAgICAgICAgICAvLyBWZXJpZmljYXIgc2UgbyBtb2RlbG8gc2Fsdm8gYWluZGEgw6kgdsOhbGlkb1xuICAgICAgICAgIGNvbnN0IGlzVmFsaWQgPSBhd2FpdCBpc1ZhbGlkTW9kZWwoZGF0YS5sYXN0VXNlZE1vZGVsKTtcbiAgICAgICAgICBpZiAoaXNWYWxpZCkge1xuICAgICAgICAgICAgc2V0U2VsZWN0ZWRNb2RlbChkYXRhLmxhc3RVc2VkTW9kZWwpO1xuXG4gICAgICAgICAgfSBlbHNlIHtcblxuICAgICAgICAgICAgLy8gTGltcGFyIG8gbW9kZWxvIGludsOhbGlkbyBkbyBjaGF0XG4gICAgICAgICAgICBhd2FpdCB1cGRhdGVEb2MoY2hhdFJlZiwge1xuICAgICAgICAgICAgICBsYXN0VXNlZE1vZGVsOiAnJ1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAvLyBDYXJyZWdhciBvIG1vZGVsbyBwYWRyw6NvIGRvIGVuZHBvaW50IGF0aXZvXG4gICAgICAgICAgICBsb2FkRGVmYXVsdE1vZGVsRnJvbUFjdGl2ZUVuZHBvaW50KCk7XG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIFNlIG8gY2hhdCBuw6NvIHRlbSBtb2RlbG8gc2Fsdm8sIGNhcnJlZ2FyIG8gbW9kZWxvIHBhZHLDo28gZG8gZW5kcG9pbnQgYXRpdm9cbiAgICAgICAgICBsb2FkRGVmYXVsdE1vZGVsRnJvbUFjdGl2ZUVuZHBvaW50KCk7XG5cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGxhc3QgdXNlZCBtb2RlbCBmb3IgY2hhdDonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgY2FycmVnYXIgbyBtb2RlbG8gcGFkcsOjbyBkbyBlbmRwb2ludCBhdGl2b1xuICBjb25zdCBsb2FkRGVmYXVsdE1vZGVsRnJvbUFjdGl2ZUVuZHBvaW50ID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghdXNlcikgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG4gICAgICBjb25zdCB1c2VyUmVmID0gZG9jKGRiLCAndXN1YXJpb3MnLCB1c2VybmFtZSwgJ2NvbmZpZ3VyYWNvZXMnLCAnc2V0dGluZ3MnKTtcbiAgICAgIGNvbnN0IHVzZXJEb2MgPSBhd2FpdCBnZXREb2ModXNlclJlZik7XG5cbiAgICAgIGlmICh1c2VyRG9jLmV4aXN0cygpKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSB1c2VyRG9jLmRhdGEoKTtcblxuICAgICAgICAvLyBQcmltZWlybywgdGVudGFyIGNhcnJlZ2FyIG8gw7psdGltbyBtb2RlbG8gdXNhZG8gZ2xvYmFsbWVudGVcbiAgICAgICAgaWYgKGRhdGEubGFzdFVzZWRNb2RlbCkge1xuICAgICAgICAgIHNldFNlbGVjdGVkTW9kZWwoZGF0YS5sYXN0VXNlZE1vZGVsKTtcblxuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFNlIG7Do28gaMOhIMO6bHRpbW8gbW9kZWxvIHVzYWRvLCBidXNjYXIgbyBtb2RlbG8gcGFkcsOjbyBkbyBlbmRwb2ludCBhdGl2b1xuICAgICAgICBpZiAoZGF0YS5lbmRwb2ludHMpIHtcbiAgICAgICAgICBjb25zdCBhY3RpdmVFbmRwb2ludCA9IE9iamVjdC52YWx1ZXMoZGF0YS5lbmRwb2ludHMpLmZpbmQoKGVuZHBvaW50OiBhbnkpID0+IGVuZHBvaW50LmF0aXZvKTtcbiAgICAgICAgICBpZiAoYWN0aXZlRW5kcG9pbnQgJiYgKGFjdGl2ZUVuZHBvaW50IGFzIGFueSkubW9kZWxvUGFkcmFvKSB7XG4gICAgICAgICAgICBzZXRTZWxlY3RlZE1vZGVsKChhY3RpdmVFbmRwb2ludCBhcyBhbnkpLm1vZGVsb1BhZHJhbyk7XG5cbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gRmFsbGJhY2sgcGFyYSBvIG1vZGVsbyBwYWRyw6NvIGhhcmRjb2RlZFxuICAgICAgc2V0U2VsZWN0ZWRNb2RlbCgnbWV0YS1sbGFtYS9sbGFtYS0zLjEtOGItaW5zdHJ1Y3Q6ZnJlZScpO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgZGVmYXVsdCBtb2RlbCBmcm9tIGFjdGl2ZSBlbmRwb2ludDonLCBlcnJvcik7XG4gICAgICAvLyBGYWxsYmFjayBwYXJhIG8gbW9kZWxvIHBhZHLDo28gaGFyZGNvZGVkIGVtIGNhc28gZGUgZXJyb1xuICAgICAgc2V0U2VsZWN0ZWRNb2RlbCgnbWV0YS1sbGFtYS9sbGFtYS0zLjEtOGItaW5zdHJ1Y3Q6ZnJlZScpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIHZhbGlkYXIgc2UgdW0gbW9kZWxvIGFpbmRhIGV4aXN0ZS/DqSB2w6FsaWRvXG4gIGNvbnN0IGlzVmFsaWRNb2RlbCA9IGFzeW5jIChtb2RlbElkOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+ID0+IHtcbiAgICAvLyBMaXN0YSBkZSBtb2RlbG9zIGNvbmhlY2lkb3MgY29tbyBpbnbDoWxpZG9zIG91IHJlbW92aWRvc1xuICAgIGNvbnN0IGludmFsaWRNb2RlbHMgPSBbXG4gICAgICAncXdlbi9xd2VuMy0yMzViLWEyMmItdGhpbmtpbmctMjUwNycsXG4gICAgICAvLyBBZGljaW9uZSBvdXRyb3MgbW9kZWxvcyBpbnbDoWxpZG9zIGFxdWkgY29uZm9ybWUgbmVjZXNzw6FyaW9cbiAgICBdO1xuXG4gICAgcmV0dXJuICFpbnZhbGlkTW9kZWxzLmluY2x1ZGVzKG1vZGVsSWQpO1xuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgbGltcGFyIG1vZGVsb3MgaW52w6FsaWRvcyBkZSB0b2RvcyBvcyBjaGF0cyBkbyB1c3XDoXJpb1xuICBjb25zdCBjbGVhbnVwSW52YWxpZE1vZGVsc0Zyb21BbGxDaGF0cyA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXVzZXIpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB1c2VybmFtZSA9IGF3YWl0IGdldFVzZXJuYW1lRnJvbUZpcmVzdG9yZSgpO1xuICAgICAgY29uc3QgY2hhdHNSZWYgPSBjb2xsZWN0aW9uKGRiLCAndXN1YXJpb3MnLCB1c2VybmFtZSwgJ2NvbnZlcnNhcycpO1xuICAgICAgY29uc3QgY2hhdHNTbmFwc2hvdCA9IGF3YWl0IGdldERvY3MoY2hhdHNSZWYpO1xuXG4gICAgICBjb25zdCB1cGRhdGVQcm9taXNlczogUHJvbWlzZTx2b2lkPltdID0gW107XG5cbiAgICAgIGZvciAoY29uc3QgY2hhdERvYyBvZiBjaGF0c1NuYXBzaG90LmRvY3MpIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGNoYXREb2MuZGF0YSgpO1xuICAgICAgICBpZiAoZGF0YS5sYXN0VXNlZE1vZGVsKSB7XG4gICAgICAgICAgY29uc3QgaXNWYWxpZCA9IGF3YWl0IGlzVmFsaWRNb2RlbChkYXRhLmxhc3RVc2VkTW9kZWwpO1xuICAgICAgICAgIGlmICghaXNWYWxpZCkge1xuXG4gICAgICAgICAgICB1cGRhdGVQcm9taXNlcy5wdXNoKFxuICAgICAgICAgICAgICB1cGRhdGVEb2MoZG9jKGRiLCAndXN1YXJpb3MnLCB1c2VybmFtZSwgJ2NvbnZlcnNhcycsIGNoYXREb2MuaWQpLCB7XG4gICAgICAgICAgICAgICAgbGFzdFVzZWRNb2RlbDogJydcbiAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGlmICh1cGRhdGVQcm9taXNlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIGF3YWl0IFByb21pc2UuYWxsKHVwZGF0ZVByb21pc2VzKTtcblxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjbGVhbmluZyBpbnZhbGlkIG1vZGVscyBmcm9tIGNoYXRzOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBjYXJyZWdhciBvIMO6bHRpbW8gbW9kZWxvIHVzYWRvIGdsb2JhbG1lbnRlIChmYWxsYmFjaylcbiAgY29uc3QgbG9hZEdsb2JhbExhc3RVc2VkTW9kZWwgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF1c2VyKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcbiAgICAgIGNvbnN0IHVzZXJSZWYgPSBkb2MoZGIsICd1c3VhcmlvcycsIHVzZXJuYW1lLCAnY29uZmlndXJhY29lcycsICdzZXR0aW5ncycpO1xuICAgICAgY29uc3QgdXNlckRvYyA9IGF3YWl0IGdldERvYyh1c2VyUmVmKTtcblxuICAgICAgaWYgKHVzZXJEb2MuZXhpc3RzKCkpIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IHVzZXJEb2MuZGF0YSgpO1xuICAgICAgICBpZiAoZGF0YS5sYXN0VXNlZE1vZGVsKSB7XG4gICAgICAgICAgLy8gVmVyaWZpY2FyIHNlIG8gbW9kZWxvIHNhbHZvIGFpbmRhIMOpIHbDoWxpZG9cbiAgICAgICAgICBjb25zdCBpc1ZhbGlkID0gYXdhaXQgaXNWYWxpZE1vZGVsKGRhdGEubGFzdFVzZWRNb2RlbCk7XG4gICAgICAgICAgaWYgKGlzVmFsaWQpIHtcbiAgICAgICAgICAgIHNldFNlbGVjdGVkTW9kZWwoZGF0YS5sYXN0VXNlZE1vZGVsKTtcblxuICAgICAgICAgIH0gZWxzZSB7XG5cbiAgICAgICAgICAgIC8vIExpbXBhciBvIG1vZGVsbyBpbnbDoWxpZG8gZGFzIGNvbmZpZ3VyYcOnw7Vlc1xuICAgICAgICAgICAgYXdhaXQgdXBkYXRlRG9jKHVzZXJSZWYsIHtcbiAgICAgICAgICAgICAgbGFzdFVzZWRNb2RlbDogbnVsbFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAvLyBDYXJyZWdhciBvIG1vZGVsbyBwYWRyw6NvIGRvIGVuZHBvaW50IGF0aXZvXG4gICAgICAgICAgICBsb2FkRGVmYXVsdE1vZGVsRnJvbUFjdGl2ZUVuZHBvaW50KCk7XG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIFNlIG7Do28gaMOhIMO6bHRpbW8gbW9kZWxvIHVzYWRvLCBjYXJyZWdhciBvIG1vZGVsbyBwYWRyw6NvIGRvIGVuZHBvaW50IGF0aXZvXG4gICAgICAgICAgbG9hZERlZmF1bHRNb2RlbEZyb21BY3RpdmVFbmRwb2ludCgpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBTZSBuw6NvIGjDoSBjb25maWd1cmHDp8O1ZXMsIGNhcnJlZ2FyIG8gbW9kZWxvIHBhZHLDo28gZG8gZW5kcG9pbnQgYXRpdm9cbiAgICAgICAgbG9hZERlZmF1bHRNb2RlbEZyb21BY3RpdmVFbmRwb2ludCgpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGdsb2JhbCBsYXN0IHVzZWQgbW9kZWw6JywgZXJyb3IpO1xuICAgICAgLy8gRmFsbGJhY2sgcGFyYSBjYXJyZWdhciBvIG1vZGVsbyBwYWRyw6NvIGRvIGVuZHBvaW50IGF0aXZvXG4gICAgICBsb2FkRGVmYXVsdE1vZGVsRnJvbUFjdGl2ZUVuZHBvaW50KCk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHdyYXBwZXIgcGFyYSBzZXRTZWxlY3RlZE1vZGVsIHF1ZSB0YW1iw6ltIHNhbHZhIG5vIEZpcmVzdG9yZVxuICBjb25zdCBoYW5kbGVNb2RlbENoYW5nZSA9IChtb2RlbElkOiBzdHJpbmcpID0+IHtcbiAgICBzZXRTZWxlY3RlZE1vZGVsKG1vZGVsSWQpO1xuXG4gICAgLy8gU2FsdmFyIG5vIGNoYXQgZXNwZWPDrWZpY28gc2UgaG91dmVyIHVtIGNoYXQgYXRpdm9cbiAgICBpZiAoYWN0dWFsQ2hhdElkKSB7XG4gICAgICBzYXZlTGFzdFVzZWRNb2RlbEZvckNoYXQobW9kZWxJZCwgYWN0dWFsQ2hhdElkKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBjcmlhciB1bSBjaGF0IGF1dG9tYXRpY2FtZW50ZVxuICBjb25zdCBjcmVhdGVBdXRvQ2hhdCA9IGFzeW5jIChmaXJzdE1lc3NhZ2U6IHN0cmluZyk6IFByb21pc2U8c3RyaW5nIHwgbnVsbD4gPT4ge1xuICAgIGlmICghdXNlcj8uZW1haWwpIHJldHVybiBudWxsO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIEJ1c2NhciB1c2VybmFtZSBkbyB1c3XDoXJpb1xuICAgICAgY29uc3QgdXN1YXJpb3NSZWYgPSBjb2xsZWN0aW9uKGRiLCAndXN1YXJpb3MnKTtcbiAgICAgIGNvbnN0IHEgPSBxdWVyeSh1c3Vhcmlvc1JlZiwgd2hlcmUoJ2VtYWlsJywgJz09JywgdXNlci5lbWFpbCkpO1xuICAgICAgY29uc3QgcXVlcnlTbmFwc2hvdCA9IGF3YWl0IGdldERvY3MocSk7XG5cbiAgICAgIGlmIChxdWVyeVNuYXBzaG90LmVtcHR5KSByZXR1cm4gbnVsbDtcblxuICAgICAgY29uc3QgdXNlckRvYyA9IHF1ZXJ5U25hcHNob3QuZG9jc1swXTtcbiAgICAgIGNvbnN0IHVzZXJEYXRhID0gdXNlckRvYy5kYXRhKCk7XG4gICAgICBjb25zdCB1c2VybmFtZSA9IHVzZXJEYXRhLnVzZXJuYW1lO1xuXG4gICAgICAvLyBHZXJhciBJRCDDum5pY28gcGFyYSBvIGNoYXRcbiAgICAgIGNvbnN0IHRpbWVzdGFtcCA9IERhdGUubm93KCk7XG4gICAgICBjb25zdCByYW5kb20gPSBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMiwgOCk7XG4gICAgICBjb25zdCBjaGF0SWQgPSBgY2hhdF8ke3RpbWVzdGFtcH1fJHtyYW5kb219YDtcbiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKTtcblxuICAgICAgLy8gR2VyYXIgbm9tZSBkbyBjaGF0IGJhc2VhZG8gbmEgcHJpbWVpcmEgbWVuc2FnZW0gKHByaW1laXJhcyAzLTQgcGFsYXZyYXMpXG4gICAgICBsZXQgZmluYWxDaGF0TmFtZSA9ICdOb3ZhIENvbnZlcnNhJztcbiAgICAgIGlmIChmaXJzdE1lc3NhZ2UudHJpbSgpLmxlbmd0aCA+IDApIHtcbiAgICAgICAgY29uc3Qgd29yZHMgPSBmaXJzdE1lc3NhZ2UudHJpbSgpLnNwbGl0KCcgJyk7XG4gICAgICAgIGNvbnN0IGNoYXROYW1lID0gd29yZHMuc2xpY2UoMCwgTWF0aC5taW4oNCwgd29yZHMubGVuZ3RoKSkuam9pbignICcpO1xuICAgICAgICBmaW5hbENoYXROYW1lID0gY2hhdE5hbWUubGVuZ3RoID4gMzAgPyBjaGF0TmFtZS5zdWJzdHJpbmcoMCwgMzApICsgJy4uLicgOiBjaGF0TmFtZTtcbiAgICAgIH1cblxuICAgICAgLy8gRGFkb3MgcGFyYSBvIEZpcmVzdG9yZVxuICAgICAgY29uc3QgZmlyZXN0b3JlRGF0YSA9IHtcbiAgICAgICAgY29udGV4dDogJycsXG4gICAgICAgIGNyZWF0ZWRBdDogbm93LFxuICAgICAgICBmb2xkZXJJZDogbnVsbCxcbiAgICAgICAgZnJlcXVlbmN5UGVuYWx0eTogMS4wLFxuICAgICAgICBpc0ZpeGVkOiBmYWxzZSxcbiAgICAgICAgbGFzdFVwZGF0ZWRBdDogbm93LFxuICAgICAgICBsYXN0VXNlZE1vZGVsOiBzZWxlY3RlZE1vZGVsLFxuICAgICAgICBsYXRleEluc3RydWN0aW9uczogZmFsc2UsXG4gICAgICAgIG1heFRva2VuczogMjA0OCxcbiAgICAgICAgbmFtZTogZmluYWxDaGF0TmFtZSxcbiAgICAgICAgcGFzc3dvcmQ6ICcnLFxuICAgICAgICByZXBldGl0aW9uUGVuYWx0eTogMS4wLFxuICAgICAgICBzZXNzaW9uVGltZToge1xuICAgICAgICAgIGxhc3RTZXNzaW9uU3RhcnQ6IG5vdyxcbiAgICAgICAgICBsYXN0VXBkYXRlZDogbm93LFxuICAgICAgICAgIHRvdGFsVGltZTogMFxuICAgICAgICB9LFxuICAgICAgICBzeXN0ZW1Qcm9tcHQ6ICcnLFxuICAgICAgICB0ZW1wZXJhdHVyZTogMS4wLFxuICAgICAgICB1bHRpbWFNZW5zYWdlbTogZmlyc3RNZXNzYWdlIHx8ICdBbmV4byBlbnZpYWRvJyxcbiAgICAgICAgdWx0aW1hTWVuc2FnZW1FbTogbm93LFxuICAgICAgICB1cGRhdGVkQXQ6IG5vd1xuICAgICAgfTtcblxuICAgICAgLy8gQ3JpYXIgZG9jdW1lbnRvIG5vIEZpcmVzdG9yZVxuICAgICAgYXdhaXQgc2V0RG9jKGRvYyhkYiwgJ3VzdWFyaW9zJywgdXNlcm5hbWUsICdjb252ZXJzYXMnLCBjaGF0SWQpLCBmaXJlc3RvcmVEYXRhKTtcblxuICAgICAgLy8gQ3JpYXIgYXJxdWl2byBjaGF0Lmpzb24gbm8gU3RvcmFnZVxuICAgICAgY29uc3QgY2hhdEpzb25EYXRhID0ge1xuICAgICAgICBpZDogY2hhdElkLFxuICAgICAgICBuYW1lOiBmaW5hbENoYXROYW1lLFxuICAgICAgICBtZXNzYWdlczogW10sXG4gICAgICAgIGNyZWF0ZWRBdDogbm93LFxuICAgICAgICBsYXN0VXBkYXRlZDogbm93XG4gICAgICB9O1xuXG4gICAgICBjb25zdCBjaGF0SnNvbkJsb2IgPSBuZXcgQmxvYihbSlNPTi5zdHJpbmdpZnkoY2hhdEpzb25EYXRhLCBudWxsLCAyKV0sIHtcbiAgICAgICAgdHlwZTogJ2FwcGxpY2F0aW9uL2pzb24nXG4gICAgICB9KTtcblxuICAgICAgY29uc3QgZmlyZWJhc2VTdG9yYWdlUmVmID0gc3RvcmFnZVJlZihzdG9yYWdlLCBgdXN1YXJpb3MvJHt1c2VybmFtZX0vY29udmVyc2FzLyR7Y2hhdElkfS9jaGF0Lmpzb25gKTtcbiAgICAgIGF3YWl0IHVwbG9hZEJ5dGVzKGZpcmViYXNlU3RvcmFnZVJlZiwgY2hhdEpzb25CbG9iKTtcblxuICAgICAgY29uc29sZS5sb2coJ0NoYXQgY3JpYWRvIGF1dG9tYXRpY2FtZW50ZTonLCBjaGF0SWQpO1xuICAgICAgLy8gRGVmaW5pciBvIG5vbWUgZG8gY2hhdCBpbWVkaWF0YW1lbnRlIGFww7NzIGNyaWHDp8Ojb1xuICAgICAgc2V0Q2hhdE5hbWUoZmluYWxDaGF0TmFtZSk7XG4gICAgICByZXR1cm4gY2hhdElkO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gY3JpYXIgY2hhdCBhdXRvbWF0aWNhbWVudGU6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNlbmRNZXNzYWdlID0gYXN5bmMgKGF0dGFjaG1lbnRzPzogaW1wb3J0KCdAL2xpYi90eXBlcy9jaGF0JykuQXR0YWNobWVudE1ldGFkYXRhW10sIHdlYlNlYXJjaEVuYWJsZWQ/OiBib29sZWFuKSA9PiB7XG5cbiAgICAvLyBPYnRlciBhbmV4b3MgaGlzdMOzcmljb3MgYXRpdm9zXG4gICAgY29uc3QgaGlzdG9yaWNhbEF0dGFjaG1lbnRzID0gZ2V0QWxsQ2hhdEF0dGFjaG1lbnRzKCkuZmlsdGVyKGF0dCA9PiBhdHQuaXNBY3RpdmUgIT09IGZhbHNlKTtcblxuICAgIC8vIENvbWJpbmFyIGFuZXhvcyBub3ZvcyBjb20gYW5leG9zIGhpc3TDs3JpY29zIGF0aXZvc1xuICAgIGNvbnN0IGFsbEF0dGFjaG1lbnRzVG9TZW5kID0gW1xuICAgICAgLi4uKGF0dGFjaG1lbnRzIHx8IFtdKSwgLy8gQW5leG9zIG5vdm9zIChzZW1wcmUgaW5jbHXDrWRvcylcbiAgICAgIC4uLmhpc3RvcmljYWxBdHRhY2htZW50cyAvLyBBbmV4b3MgaGlzdMOzcmljb3MgYXRpdm9zXG4gICAgXTtcblxuICAgIC8vIFJlbW92ZXIgZHVwbGljYXRhcyBiYXNlYWRvIG5vIElEXG4gICAgY29uc3QgdW5pcXVlQXR0YWNobWVudHMgPSBhbGxBdHRhY2htZW50c1RvU2VuZC5maWx0ZXIoKGF0dGFjaG1lbnQsIGluZGV4LCBzZWxmKSA9PlxuICAgICAgaW5kZXggPT09IHNlbGYuZmluZEluZGV4KGEgPT4gYS5pZCA9PT0gYXR0YWNobWVudC5pZClcbiAgICApO1xuXG4gICAgaWYgKCghbWVzc2FnZS50cmltKCkgJiYgKCFhdHRhY2htZW50cyB8fCBhdHRhY2htZW50cy5sZW5ndGggPT09IDApKSB8fCBpc0xvYWRpbmcgfHwgaXNTdHJlYW1pbmcpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgaWYgKCF1c2VyPy5lbWFpbCkgcmV0dXJuO1xuXG4gICAgY29uc3QgdXNlck1lc3NhZ2U6IE1lc3NhZ2UgPSB7XG4gICAgICBpZDogYWlTZXJ2aWNlLmdlbmVyYXRlTWVzc2FnZUlkKCksXG4gICAgICBjb250ZW50OiBtZXNzYWdlLnRyaW0oKSxcbiAgICAgIHNlbmRlcjogJ3VzZXInLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICBhdHRhY2htZW50czogYXR0YWNobWVudHMgfHwgW10sIC8vIFNhbHZhciBhcGVuYXMgYW5leG9zIG5vdm9zIG5hIG1lbnNhZ2VtXG4gICAgfTtcblxuICAgIC8vIFNlIG7Do28gaMOhIGNoYXQgYXR1YWwsIGNyaWFyIHVtIGF1dG9tYXRpY2FtZW50ZVxuICAgIGxldCBjaGF0SWRUb1VzZSA9IGFjdHVhbENoYXRJZDtcbiAgICBpZiAoIWNoYXRJZFRvVXNlKSB7XG4gICAgICBjb25zdCBtZXNzYWdlRm9yQ2hhdCA9IG1lc3NhZ2UudHJpbSgpIHx8IChhdHRhY2htZW50cyAmJiBhdHRhY2htZW50cy5sZW5ndGggPiAwID8gJ0FuZXhvIGVudmlhZG8nIDogJ05vdmEgY29udmVyc2EnKTtcbiAgICAgIGNoYXRJZFRvVXNlID0gYXdhaXQgY3JlYXRlQXV0b0NoYXQobWVzc2FnZUZvckNoYXQpO1xuICAgICAgaWYgKGNoYXRJZFRvVXNlKSB7XG4gICAgICAgIHNldEFjdHVhbENoYXRJZChjaGF0SWRUb1VzZSk7XG4gICAgICAgIC8vIE8gbm9tZSBqw6EgZm9pIGRlZmluaWRvIG5hIGZ1bsOnw6NvIGNyZWF0ZUF1dG9DaGF0LCBtYXMgdmFtb3MgZ2FyYW50aXIgY2FycmVnYW5kbyB0YW1iw6ltXG4gICAgICAgIGxvYWRDaGF0TmFtZShjaGF0SWRUb1VzZSk7XG4gICAgICAgIG9uQ2hhdENyZWF0ZWQ/LihjaGF0SWRUb1VzZSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKCFjaGF0SWRUb1VzZSkge1xuICAgICAgY29uc29sZS5lcnJvcignTsOjbyBmb2kgcG9zc8OtdmVsIGNyaWFyIG91IG9idGVyIGNoYXQgSUQnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBBZGljaW9uYXIgbWVuc2FnZW0gZG8gdXN1w6FyaW9cbiAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCB1c2VyTWVzc2FnZV0pO1xuICAgIGNvbnN0IGN1cnJlbnRNZXNzYWdlID0gbWVzc2FnZS50cmltKCkgfHwgXCJcIjsgLy8gUGVybWl0aXIgbWVuc2FnZW0gdmF6aWEgc2UgaG91dmVyIGFuZXhvc1xuICAgIHNldE1lc3NhZ2UoJycpO1xuICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICBzZXRJc1N0cmVhbWluZyh0cnVlKTtcblxuICAgIC8vIE1vdmVyIGNoYXQgcGFyYSBvIHRvcG8gY29tIGFuaW1hw6fDo29cbiAgICBpZiAoY2hhdElkVG9Vc2UgJiYgc2lkZWJhclJlZj8uY3VycmVudCkge1xuICAgICAgc2lkZWJhclJlZi5jdXJyZW50Lm1vdmVUb1RvcChjaGF0SWRUb1VzZSk7XG4gICAgfVxuXG4gICAgLy8gUHJlcGFyYXIgSUQgcGFyYSBhIG1lbnNhZ2VtIGRhIElBIHF1ZSBzZXLDoSBjcmlhZGEgZHVyYW50ZSBvIHN0cmVhbWluZ1xuICAgIGNvbnN0IGFpTWVzc2FnZUlkID0gYWlTZXJ2aWNlLmdlbmVyYXRlTWVzc2FnZUlkKCk7XG4gICAgc2V0U3RyZWFtaW5nTWVzc2FnZUlkKGFpTWVzc2FnZUlkKTtcblxuICAgIC8vIEJ1c2NhciB1c2VybmFtZSBjb3JyZXRvIGRvIHVzdcOhcmlvXG4gICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcblxuICAgIC8vIEVudmlhciBwYXJhIGEgSUEgKGluY2x1aW5kbyBhbmV4b3MgaGlzdMOzcmljb3MgYXRpdm9zICsgYW5leG9zIG5vdm9zKVxuICAgIGF3YWl0IGFpU2VydmljZS5zZW5kTWVzc2FnZVNhZmUoXG4gICAgICB7XG4gICAgICAgIHVzZXJuYW1lOiB1c2VybmFtZSxcbiAgICAgICAgY2hhdElkOiBjaGF0SWRUb1VzZSxcbiAgICAgICAgbWVzc2FnZTogY3VycmVudE1lc3NhZ2UsXG4gICAgICAgIG1vZGVsOiBzZWxlY3RlZE1vZGVsLFxuICAgICAgICBhdHRhY2htZW50czogdW5pcXVlQXR0YWNobWVudHMsXG4gICAgICAgIHdlYlNlYXJjaEVuYWJsZWQ6IHdlYlNlYXJjaEVuYWJsZWQsXG4gICAgICAgIHVzZXJNZXNzYWdlSWQ6IHVzZXJNZXNzYWdlLmlkLCAvLyBQYXNzYXIgbyBJRCBkYSBtZW5zYWdlbSBkbyB1c3XDoXJpb1xuICAgICAgfSxcbiAgICAgIC8vIG9uQ2h1bmsgLSBjcmlhciBtZW5zYWdlbSBuYSBwcmltZWlyYSBjaHVuayBlIGF0dWFsaXphciBjb20gY2FkYSBjaHVua1xuICAgICAgKGNodW5rOiBzdHJpbmcpID0+IHtcbiAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiB7XG4gICAgICAgICAgLy8gVmVyaWZpY2FyIHNlIGEgbWVuc2FnZW0gZGEgSUEgasOhIGV4aXN0ZVxuICAgICAgICAgIGNvbnN0IGV4aXN0aW5nTWVzc2FnZUluZGV4ID0gcHJldi5maW5kSW5kZXgobXNnID0+IG1zZy5pZCA9PT0gYWlNZXNzYWdlSWQpO1xuXG4gICAgICAgICAgaWYgKGV4aXN0aW5nTWVzc2FnZUluZGV4ICE9PSAtMSkge1xuICAgICAgICAgICAgLy8gQXR1YWxpemFyIG1lbnNhZ2VtIGV4aXN0ZW50ZVxuICAgICAgICAgICAgcmV0dXJuIHByZXYubWFwKG1zZyA9PlxuICAgICAgICAgICAgICBtc2cuaWQgPT09IGFpTWVzc2FnZUlkXG4gICAgICAgICAgICAgICAgPyB7IC4uLm1zZywgY29udGVudDogbXNnLmNvbnRlbnQgKyBjaHVuayB9XG4gICAgICAgICAgICAgICAgOiBtc2dcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIC8vIENyaWFyIG5vdmEgbWVuc2FnZW0gZGEgSUEgbmEgcHJpbWVpcmEgY2h1bmtcbiAgICAgICAgICAgIC8vIFJlbW92ZXIgbyBpbmRpY2Fkb3IgZGUgbG9hZGluZyBhc3NpbSBxdWUgYSBwcmltZWlyYSBjaHVuayBjaGVnYXJcbiAgICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG5cbiAgICAgICAgICAgIGNvbnN0IGFpTWVzc2FnZTogTWVzc2FnZSA9IHtcbiAgICAgICAgICAgICAgaWQ6IGFpTWVzc2FnZUlkLFxuICAgICAgICAgICAgICBjb250ZW50OiBjaHVuayxcbiAgICAgICAgICAgICAgc2VuZGVyOiAnYWknLFxuICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgICAgaGFzV2ViU2VhcmNoOiB3ZWJTZWFyY2hFbmFibGVkLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIHJldHVybiBbLi4ucHJldiwgYWlNZXNzYWdlXTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgfSxcbiAgICAgIC8vIG9uQ29tcGxldGUgLSBmaW5hbGl6YXIgc3RyZWFtaW5nXG4gICAgICAoZnVsbFJlc3BvbnNlOiBzdHJpbmcpID0+IHtcbiAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBwcmV2Lm1hcChtc2cgPT5cbiAgICAgICAgICBtc2cuaWQgPT09IGFpTWVzc2FnZUlkXG4gICAgICAgICAgICA/IHsgLi4ubXNnLCBjb250ZW50OiBmdWxsUmVzcG9uc2UgfVxuICAgICAgICAgICAgOiBtc2dcbiAgICAgICAgKSk7XG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICAgIHNldElzU3RyZWFtaW5nKGZhbHNlKTtcbiAgICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZUlkKG51bGwpO1xuXG4gICAgICAgIC8vIFNhbHZhciBvIG1vZGVsbyB1c2FkbyBubyBjaGF0IGVzcGVjw61maWNvXG4gICAgICAgIGlmIChjaGF0SWRUb1VzZSkge1xuICAgICAgICAgIHNhdmVMYXN0VXNlZE1vZGVsRm9yQ2hhdChzZWxlY3RlZE1vZGVsLCBjaGF0SWRUb1VzZSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBBdHVhbGl6YXIgc2FsZG8gZG8gT3BlblJvdXRlciBhcMOzcyBhIHJlc3Bvc3RhIGNvbSBkZWxheSBkZSA1IHNlZ3VuZG9zXG4gICAgICAgIGlmIChvblVwZGF0ZU9wZW5Sb3V0ZXJCYWxhbmNlKSB7XG4gICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICBvblVwZGF0ZU9wZW5Sb3V0ZXJCYWxhbmNlKCk7XG4gICAgICAgICAgfSwgNTAwMCk7XG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICAvLyBvbkVycm9yIC0gdHJhdGFyIGVycm9zXG4gICAgICAoZXJyb3I6IHN0cmluZykgPT4ge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIG5hIElBOicsIGVycm9yKTtcbiAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBwcmV2Lm1hcChtc2cgPT5cbiAgICAgICAgICBtc2cuaWQgPT09IGFpTWVzc2FnZUlkXG4gICAgICAgICAgICA/IHsgLi4ubXNnLCBjb250ZW50OiBg4p2MIEVycm86ICR7ZXJyb3J9YCB9XG4gICAgICAgICAgICA6IG1zZ1xuICAgICAgICApKTtcbiAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xuICAgICAgICBzZXRTdHJlYW1pbmdNZXNzYWdlSWQobnVsbCk7XG4gICAgICB9XG4gICAgKTtcbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIGNhbmNlbGFyIHN0cmVhbWluZ1xuICBjb25zdCBoYW5kbGVDYW5jZWxTdHJlYW1pbmcgPSAoKSA9PiB7XG4gICAgYWlTZXJ2aWNlLmNhbmNlbFJlcXVlc3QoKTtcbiAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIHNldElzU3RyZWFtaW5nKGZhbHNlKTtcbiAgICBzZXRTdHJlYW1pbmdNZXNzYWdlSWQobnVsbCk7XG4gIH07XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBjYXJyZWdhciBvIG5vbWUgZG8gY2hhdCBkbyBGaXJlc3RvcmVcbiAgY29uc3QgbG9hZENoYXROYW1lID0gYXN5bmMgKGNoYXRJZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCF1c2VyPy5lbWFpbCkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG4gICAgICBjb25zdCBjaGF0RG9jID0gYXdhaXQgZ2V0RG9jKGRvYyhkYiwgJ3VzdWFyaW9zJywgdXNlcm5hbWUsICdjb252ZXJzYXMnLCBjaGF0SWQpKTtcblxuICAgICAgaWYgKGNoYXREb2MuZXhpc3RzKCkpIHtcbiAgICAgICAgY29uc3QgY2hhdERhdGEgPSBjaGF0RG9jLmRhdGEoKTtcbiAgICAgICAgY29uc3QgY2hhdE5hbWUgPSBjaGF0RGF0YS5uYW1lIHx8ICdDb252ZXJzYSBzZW0gbm9tZSc7XG4gICAgICAgIHNldENoYXROYW1lKGNoYXROYW1lKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldENoYXROYW1lKCdDb252ZXJzYSBuw6NvIGVuY29udHJhZGEnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBjYXJyZWdhciBub21lIGRvIGNoYXQ6JywgZXJyb3IpO1xuICAgICAgc2V0Q2hhdE5hbWUoJ0Vycm8gYW8gY2FycmVnYXIgbm9tZScpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIGNhcnJlZ2FyIG1lbnNhZ2VucyBleGlzdGVudGVzIGRvIGNoYXRcbiAgY29uc3QgbG9hZENoYXRNZXNzYWdlcyA9IGFzeW5jIChjaGF0SWQ6IHN0cmluZykgPT4ge1xuICAgIGlmICghdXNlcj8uZW1haWwpIHJldHVybjtcblxuICAgIHNldElzTG9hZGluZ0NoYXQodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG4gICAgICBjb25zdCBjaGF0TWVzc2FnZXMgPSBhd2FpdCBhaVNlcnZpY2UubG9hZENoYXRNZXNzYWdlcyh1c2VybmFtZSwgY2hhdElkKTtcbiAgICAgIGNvbnN0IGNvbnZlcnRlZE1lc3NhZ2VzID0gYWlTZXJ2aWNlLmNvbnZlcnRGcm9tQUlGb3JtYXQoY2hhdE1lc3NhZ2VzKTtcbiAgICAgIHNldE1lc3NhZ2VzKGNvbnZlcnRlZE1lc3NhZ2VzKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIEVycm8gYW8gY2FycmVnYXIgbWVuc2FnZW5zIGRvIGNoYXQ6JywgZXJyb3IpO1xuICAgICAgc2V0TWVzc2FnZXMoW10pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmdDaGF0KGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gQ2FycmVnYXIgw7psdGltbyBtb2RlbG8gdXNhZG8gZ2xvYmFsbWVudGUgcXVhbmRvIG8gY29tcG9uZW50ZSBtb250YXIgKGFwZW5hcyBzZSBuw6NvIGjDoSBjaGF0KVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh1c2VyICYmICFjdXJyZW50Q2hhdCkge1xuICAgICAgLy8gTGltcGFyIG1vZGVsb3MgaW52w6FsaWRvcyB1bWEgdmV6IHF1YW5kbyBvIHVzdcOhcmlvIGZheiBsb2dpblxuICAgICAgY2xlYW51cEludmFsaWRNb2RlbHNGcm9tQWxsQ2hhdHMoKTtcbiAgICAgIGxvYWRHbG9iYWxMYXN0VXNlZE1vZGVsKCk7XG4gICAgfVxuICB9LCBbdXNlciwgY3VycmVudENoYXRdKTtcblxuICAvLyBDYXJyZWdhciBtZW5zYWdlbnMgcXVhbmRvIG8gY2hhdCBhdHVhbCBtdWRhclxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChjdXJyZW50Q2hhdCAmJiBjdXJyZW50Q2hhdCAhPT0gYWN0dWFsQ2hhdElkKSB7XG4gICAgICBzZXRBY3R1YWxDaGF0SWQoY3VycmVudENoYXQpO1xuICAgICAgc2V0SXNMb2FkaW5nQ2hhdCh0cnVlKTtcbiAgICAgIC8vIExpbXBhciBtZW5zYWdlbnMgaW1lZGlhdGFtZW50ZSBwYXJhIG1vc3RyYXIgbyBlc3RhZG8gZGUgY2FycmVnYW1lbnRvXG4gICAgICBzZXRNZXNzYWdlcyhbXSk7XG4gICAgICBsb2FkQ2hhdE1lc3NhZ2VzKGN1cnJlbnRDaGF0KTtcbiAgICAgIGxvYWRDaGF0TmFtZShjdXJyZW50Q2hhdCk7XG4gICAgICAvLyBDYXJyZWdhciBvIG1vZGVsbyBlc3BlY8OtZmljbyBkbyBjaGF0XG4gICAgICBsb2FkTGFzdFVzZWRNb2RlbEZvckNoYXQoY3VycmVudENoYXQpO1xuICAgIH0gZWxzZSBpZiAoIWN1cnJlbnRDaGF0ICYmIGFjdHVhbENoYXRJZCkge1xuICAgICAgLy8gU8OzIHJlc2V0YXIgc2UgcmVhbG1lbnRlIG7Do28gaMOhIGNoYXQgZSBoYXZpYSB1bSBjaGF0IGFudGVzXG4gICAgICBzZXRBY3R1YWxDaGF0SWQobnVsbCk7XG4gICAgICBzZXRNZXNzYWdlcyhbXSk7XG4gICAgICBzZXRDaGF0TmFtZSgnTm92YSBDb252ZXJzYScpO1xuICAgICAgc2V0SXNMb2FkaW5nQ2hhdChmYWxzZSk7XG4gICAgICAvLyBDYXJyZWdhciBtb2RlbG8gZ2xvYmFsIHF1YW5kbyBuw6NvIGjDoSBjaGF0IGVzcGVjw61maWNvXG4gICAgICBsb2FkR2xvYmFsTGFzdFVzZWRNb2RlbCgpO1xuICAgIH1cbiAgfSwgW2N1cnJlbnRDaGF0LCB1c2VyPy5lbWFpbF0pO1xuXG4gIC8vIEZ1bsOnw7VlcyBwYXJhIG1hbmlwdWxhciBtZW5zYWdlbnNcbiAgY29uc3QgaGFuZGxlRGVsZXRlTWVzc2FnZSA9IGFzeW5jIChtZXNzYWdlSWQ6IHN0cmluZykgPT4ge1xuICAgIGlmICghYWN0dWFsQ2hhdElkIHx8ICF1c2VyPy5lbWFpbCkgcmV0dXJuO1xuXG4gICAgLy8gUmVtb3ZlciB2aXN1YWxtZW50ZSBwcmltZWlybyBwYXJhIG1lbGhvciBVWFxuICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gcHJldi5maWx0ZXIobXNnID0+IG1zZy5pZCAhPT0gbWVzc2FnZUlkKSk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcbiAgICAgIGNvbnN0IHN1Y2Nlc3MgPSBhd2FpdCBhaVNlcnZpY2UuZGVsZXRlTWVzc2FnZSh1c2VybmFtZSwgYWN0dWFsQ2hhdElkLCBtZXNzYWdlSWQpO1xuXG4gICAgICBpZiAoIXN1Y2Nlc3MpIHtcbiAgICAgICAgLy8gU2UgZmFsaG91LCByZXN0YXVyYXIgYSBtZW5zYWdlbVxuICAgICAgICBsb2FkQ2hhdE1lc3NhZ2VzKGFjdHVhbENoYXRJZCk7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhbGhhIGFvIGRlbGV0YXIgbWVuc2FnZW0gbm8gc2Vydmlkb3InKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgLy8gU2UgZmFsaG91LCByZXN0YXVyYXIgYSBtZW5zYWdlbVxuICAgICAgbG9hZENoYXRNZXNzYWdlcyhhY3R1YWxDaGF0SWQpO1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBkZWxldGFyIG1lbnNhZ2VtOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlUmVnZW5lcmF0ZU1lc3NhZ2UgPSBhc3luYyAobWVzc2FnZUlkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIWFjdHVhbENoYXRJZCB8fCAhdXNlcj8uZW1haWwpIHJldHVybjtcblxuICAgIC8vIOKchSBDT1JSRcOHw4NPOiBSZWNhcnJlZ2FyIG1lbnNhZ2VucyBkbyBGaXJlYmFzZSBTdG9yYWdlIHBhcmEgZ2FyYW50aXIgZXN0YWRvIGF0dWFsaXphZG9cbiAgICBjb25zb2xlLmxvZygn8J+UhCBSZWNhcnJlZ2FuZG8gbWVuc2FnZW5zIGFudGVzIGRhIHJlZ2VuZXJhw6fDo28gcGFyYSBnYXJhbnRpciBlc3RhZG8gYXR1YWxpemFkby4uLicpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG5cbiAgICAgIC8vIENhcnJlZ2FyIG1lbnNhZ2VucyBkaXJldGFtZW50ZSBkbyBGaXJlYmFzZSBTdG9yYWdlXG4gICAgICBjb25zdCBmcmVzaE1lc3NhZ2VzID0gYXdhaXQgYWlTZXJ2aWNlLmxvYWRDaGF0TWVzc2FnZXModXNlcm5hbWUsIGFjdHVhbENoYXRJZCk7XG4gICAgICBjb25zdCBjb252ZXJ0ZWRGcmVzaE1lc3NhZ2VzID0gYWlTZXJ2aWNlLmNvbnZlcnRGcm9tQUlGb3JtYXQoZnJlc2hNZXNzYWdlcyk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5OlIE1lbnNhZ2VucyByZWNhcnJlZ2FkYXMgZG8gU3RvcmFnZTonLCBjb252ZXJ0ZWRGcmVzaE1lc3NhZ2VzLmxlbmd0aCk7XG5cbiAgICAgIC8vIEJ1c2NhciBvIMOtbmRpY2UgZGEgbWVuc2FnZW0gbmFzIG1lbnNhZ2VucyBmcmVzY2FzXG4gICAgICBjb25zdCBtZXNzYWdlSW5kZXggPSBjb252ZXJ0ZWRGcmVzaE1lc3NhZ2VzLmZpbmRJbmRleChtc2cgPT4gbXNnLmlkID09PSBtZXNzYWdlSWQpO1xuICAgICAgaWYgKG1lc3NhZ2VJbmRleCA9PT0gLTEpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIE1lbnNhZ2VtIG7Do28gZW5jb250cmFkYSBhcMOzcyByZWNhcnJlZ2FyOicsIG1lc3NhZ2VJZCk7XG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICAgIHNldElzU3RyZWFtaW5nKGZhbHNlKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBtZXNzYWdlVG9SZWdlbmVyYXRlID0gY29udmVydGVkRnJlc2hNZXNzYWdlc1ttZXNzYWdlSW5kZXhdO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk50gTWVuc2FnZW0gcXVlIHNlcsOhIHJlZ2VuZXJhZGE6Jywge1xuICAgICAgICBpZDogbWVzc2FnZVRvUmVnZW5lcmF0ZS5pZCxcbiAgICAgICAgY29udGVudDogbWVzc2FnZVRvUmVnZW5lcmF0ZS5jb250ZW50LnN1YnN0cmluZygwLCAxMDApICsgJy4uLicsXG4gICAgICAgIGluZGV4OiBtZXNzYWdlSW5kZXhcbiAgICAgIH0pO1xuXG4gICAgICAvLyBSZW1vdmVyIGFwZW5hcyBhcyBtZW5zYWdlbnMgQVDDk1MgYSBtZW5zYWdlbSBzZWxlY2lvbmFkYSAobsOjbyBpbmNsdWluZG8gZWxhKVxuICAgICAgY29uc3QgbWVzc2FnZXNCZWZvcmVSZWdlbmVyYXRpb24gPSBjb252ZXJ0ZWRGcmVzaE1lc3NhZ2VzLnNsaWNlKDAsIG1lc3NhZ2VJbmRleCArIDEpO1xuICAgICAgc2V0TWVzc2FnZXMobWVzc2FnZXNCZWZvcmVSZWdlbmVyYXRpb24pO1xuXG4gICAgICAvLyBQcmVwYXJhciBvIGNvbnRlw7pkbyBkYSBtZW5zYWdlbSBwYXJhIHJlZ2VuZXJhclxuICAgICAgc2V0TWVzc2FnZShtZXNzYWdlVG9SZWdlbmVyYXRlLmNvbnRlbnQpO1xuICAgICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgICAgc2V0SXNTdHJlYW1pbmcodHJ1ZSk7XG5cbiAgICAgIC8vIFByZXBhcmFyIElEIHBhcmEgYSBub3ZhIG1lbnNhZ2VtIGRhIElBIHF1ZSBzZXLDoSBjcmlhZGEgZHVyYW50ZSBvIHN0cmVhbWluZ1xuICAgICAgY29uc3QgYWlNZXNzYWdlSWQgPSBhaVNlcnZpY2UuZ2VuZXJhdGVNZXNzYWdlSWQoKTtcbiAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2VJZChhaU1lc3NhZ2VJZCk7XG5cbiAgICAgIC8vIERlbGV0YXIgYXBlbmFzIGFzIG1lbnNhZ2VucyBQT1NURVJJT1JFUyBkbyBGaXJlYmFzZSBTdG9yYWdlIChuw6NvIGEgbWVuc2FnZW0gYXR1YWwpXG4gICAgICBjb25zb2xlLmxvZyhg8J+Xke+4jyBEZWxldGFuZG8gJHtjb252ZXJ0ZWRGcmVzaE1lc3NhZ2VzLmxlbmd0aCAtIG1lc3NhZ2VJbmRleCAtIDF9IG1lbnNhZ2VucyBwb3N0ZXJpb3Jlcy4uLmApO1xuICAgICAgZm9yIChsZXQgaSA9IG1lc3NhZ2VJbmRleCArIDE7IGkgPCBjb252ZXJ0ZWRGcmVzaE1lc3NhZ2VzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IG1zZ1RvRGVsZXRlID0gY29udmVydGVkRnJlc2hNZXNzYWdlc1tpXTtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfl5HvuI8gRGVsZXRhbmRvIG1lbnNhZ2VtOicsIG1zZ1RvRGVsZXRlLmlkKTtcbiAgICAgICAgYXdhaXQgYWlTZXJ2aWNlLmRlbGV0ZU1lc3NhZ2UodXNlcm5hbWUsIGFjdHVhbENoYXRJZCwgbXNnVG9EZWxldGUuaWQpO1xuICAgICAgfVxuXG4gICAgICAvLyBFbnZpYXIgcGFyYSBhIElBIHBhcmEgcmVnZW5lcmFyXG4gICAgICBhd2FpdCBhaVNlcnZpY2Uuc2VuZE1lc3NhZ2VTYWZlKFxuICAgICAgICB7XG4gICAgICAgICAgdXNlcm5hbWU6IHVzZXJuYW1lLFxuICAgICAgICAgIGNoYXRJZDogYWN0dWFsQ2hhdElkLFxuICAgICAgICAgIG1lc3NhZ2U6IG1lc3NhZ2VUb1JlZ2VuZXJhdGUuY29udGVudCxcbiAgICAgICAgICBtb2RlbDogc2VsZWN0ZWRNb2RlbCxcbiAgICAgICAgICBpc1JlZ2VuZXJhdGlvbjogdHJ1ZSxcbiAgICAgICAgICB1c2VyTWVzc2FnZUlkOiBtZXNzYWdlVG9SZWdlbmVyYXRlLmlkLCAvLyBQYXNzYXIgbyBJRCBkYSBtZW5zYWdlbSBxdWUgZXN0w6Egc2VuZG8gcmVnZW5lcmFkYVxuICAgICAgICB9LFxuICAgICAgICAvLyBvbkNodW5rIC0gY3JpYXIgbWVuc2FnZW0gbmEgcHJpbWVpcmEgY2h1bmsgZSBhdHVhbGl6YXIgY29tIGNhZGEgY2h1bmtcbiAgICAgICAgKGNodW5rOiBzdHJpbmcpID0+IHtcbiAgICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHtcbiAgICAgICAgICAgIC8vIFZlcmlmaWNhciBzZSBhIG1lbnNhZ2VtIGRhIElBIGrDoSBleGlzdGVcbiAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nTWVzc2FnZUluZGV4ID0gcHJldi5maW5kSW5kZXgobXNnID0+IG1zZy5pZCA9PT0gYWlNZXNzYWdlSWQpO1xuXG4gICAgICAgICAgICBpZiAoZXhpc3RpbmdNZXNzYWdlSW5kZXggIT09IC0xKSB7XG4gICAgICAgICAgICAgIC8vIEF0dWFsaXphciBtZW5zYWdlbSBleGlzdGVudGVcbiAgICAgICAgICAgICAgcmV0dXJuIHByZXYubWFwKG1zZyA9PlxuICAgICAgICAgICAgICAgIG1zZy5pZCA9PT0gYWlNZXNzYWdlSWRcbiAgICAgICAgICAgICAgICAgID8geyAuLi5tc2csIGNvbnRlbnQ6IG1zZy5jb250ZW50ICsgY2h1bmsgfVxuICAgICAgICAgICAgICAgICAgOiBtc2dcbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgIC8vIENyaWFyIG5vdmEgbWVuc2FnZW0gZGEgSUEgbmEgcHJpbWVpcmEgY2h1bmtcbiAgICAgICAgICAgICAgLy8gUmVtb3ZlciBvIGluZGljYWRvciBkZSBsb2FkaW5nIGFzc2ltIHF1ZSBhIHByaW1laXJhIGNodW5rIGNoZWdhclxuICAgICAgICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuXG4gICAgICAgICAgICAgIGNvbnN0IGFpTWVzc2FnZTogTWVzc2FnZSA9IHtcbiAgICAgICAgICAgICAgICBpZDogYWlNZXNzYWdlSWQsXG4gICAgICAgICAgICAgICAgY29udGVudDogY2h1bmssXG4gICAgICAgICAgICAgICAgc2VuZGVyOiAnYWknLFxuICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICAgIGhhc1dlYlNlYXJjaDogZmFsc2UsIC8vIFJlZ2VuZXJhw6fDo28gbsOjbyB1c2Egd2ViIHNlYXJjaCBwb3IgcGFkcsOjb1xuICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICByZXR1cm4gWy4uLnByZXYsIGFpTWVzc2FnZV07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSk7XG4gICAgICAgIH0sXG4gICAgICAgIC8vIG9uQ29tcGxldGUgLSBmaW5hbGl6YXIgc3RyZWFtaW5nXG4gICAgICAgIChmdWxsUmVzcG9uc2U6IHN0cmluZykgPT4ge1xuICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gcHJldi5tYXAobXNnID0+XG4gICAgICAgICAgICBtc2cuaWQgPT09IGFpTWVzc2FnZUlkXG4gICAgICAgICAgICAgID8geyAuLi5tc2csIGNvbnRlbnQ6IGZ1bGxSZXNwb25zZSB9XG4gICAgICAgICAgICAgIDogbXNnXG4gICAgICAgICAgKSk7XG4gICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgICAgICBzZXRJc1N0cmVhbWluZyhmYWxzZSk7XG4gICAgICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZUlkKG51bGwpO1xuICAgICAgICAgIHNldE1lc3NhZ2UoJycpOyAvLyBMaW1wYXIgbyBjYW1wbyBkZSBpbnB1dFxuXG4gICAgICAgICAgLy8gQXR1YWxpemFyIHNhbGRvIGRvIE9wZW5Sb3V0ZXIgYXDDs3MgYSByZWdlbmVyYcOnw6NvIGNvbSBkZWxheSBkZSA1IHNlZ3VuZG9zXG4gICAgICAgICAgaWYgKG9uVXBkYXRlT3BlblJvdXRlckJhbGFuY2UpIHtcbiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgICBvblVwZGF0ZU9wZW5Sb3V0ZXJCYWxhbmNlKCk7XG4gICAgICAgICAgICB9LCA1MDAwKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIC8vIG9uRXJyb3IgLSB0cmF0YXIgZXJyb3NcbiAgICAgICAgKGVycm9yOiBzdHJpbmcpID0+IHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIG5hIHJlZ2VuZXJhw6fDo286JywgZXJyb3IpO1xuICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gcHJldi5tYXAobXNnID0+XG4gICAgICAgICAgICBtc2cuaWQgPT09IGFpTWVzc2FnZUlkXG4gICAgICAgICAgICAgID8geyAuLi5tc2csIGNvbnRlbnQ6IGDinYwgRXJybyBuYSByZWdlbmVyYcOnw6NvOiAke2Vycm9yfWAgfVxuICAgICAgICAgICAgICA6IG1zZ1xuICAgICAgICAgICkpO1xuICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xuICAgICAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2VJZChudWxsKTtcbiAgICAgICAgICBzZXRNZXNzYWdlKCcnKTsgLy8gTGltcGFyIG8gY2FtcG8gZGUgaW5wdXRcbiAgICAgICAgfVxuICAgICAgKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJybyBhbyByZWdlbmVyYXIgbWVuc2FnZW06JywgZXJyb3IpO1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgIHNldElzU3RyZWFtaW5nKGZhbHNlKTtcbiAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2VJZChudWxsKTtcbiAgICAgIHNldE1lc3NhZ2UoJycpOyAvLyBMaW1wYXIgbyBjYW1wbyBkZSBpbnB1dFxuXG4gICAgICAvLyBSZWNhcnJlZ2FyIG1lbnNhZ2VucyBlbSBjYXNvIGRlIGVycm9cbiAgICAgIGxvYWRDaGF0TWVzc2FnZXMoYWN0dWFsQ2hhdElkKTtcbiAgICB9XG4gIH07XG5cblxuXG4gIGNvbnN0IGhhbmRsZUVkaXRNZXNzYWdlID0gYXN5bmMgKG1lc3NhZ2VJZDogc3RyaW5nLCBuZXdDb250ZW50OiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+ID0+IHtcbiAgICBpZiAoIWFjdHVhbENoYXRJZCB8fCAhdXNlcj8uZW1haWwpIHJldHVybiBmYWxzZTtcblxuICAgIGNvbnNvbGUubG9nKCfinI/vuI8gSW5pY2lhbmRvIGVkacOnw6NvIGRlIG1lbnNhZ2VtOicsIHtcbiAgICAgIG1lc3NhZ2VJZCxcbiAgICAgIGNoYXRJZDogYWN0dWFsQ2hhdElkLFxuICAgICAgbmV3Q29udGVudExlbmd0aDogbmV3Q29udGVudC5sZW5ndGgsXG4gICAgICBuZXdDb250ZW50UHJldmlldzogbmV3Q29udGVudC5zdWJzdHJpbmcoMCwgMTAwKSArICcuLi4nXG4gICAgfSk7XG5cbiAgICAvLyBBdHVhbGl6YXIgdmlzdWFsbWVudGUgcHJpbWVpcm8gcGFyYSBtZWxob3IgVVhcbiAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHByZXYubWFwKG1zZyA9PlxuICAgICAgbXNnLmlkID09PSBtZXNzYWdlSWQgPyB7IC4uLm1zZywgY29udGVudDogbmV3Q29udGVudCB9IDogbXNnXG4gICAgKSk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OkIEVudmlhbmRvIGF0dWFsaXphw6fDo28gcGFyYSBvIHNlcnZpZG9yLi4uJyk7XG5cbiAgICAgIGNvbnN0IHN1Y2Nlc3MgPSBhd2FpdCBhaVNlcnZpY2UudXBkYXRlTWVzc2FnZSh1c2VybmFtZSwgYWN0dWFsQ2hhdElkLCBtZXNzYWdlSWQsIG5ld0NvbnRlbnQpO1xuXG4gICAgICBpZiAoIXN1Y2Nlc3MpIHtcbiAgICAgICAgLy8gU2UgZmFsaG91LCByZXN0YXVyYXIgbyBjb250ZcO6ZG8gb3JpZ2luYWxcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEZhbGhhIGFvIGF0dWFsaXphciBtZW5zYWdlbSBubyBzZXJ2aWRvcicpO1xuICAgICAgICBsb2FkQ2hhdE1lc3NhZ2VzKGFjdHVhbENoYXRJZCk7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgTWVuc2FnZW0gZWRpdGFkYSBlIHNhbHZhIGNvbSBzdWNlc3NvIG5vIEZpcmViYXNlIFN0b3JhZ2U6Jywge1xuICAgICAgICAgIG1lc3NhZ2VJZCxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIC8vIFNlIGZhbGhvdSwgcmVzdGF1cmFyIG8gY29udGXDumRvIG9yaWdpbmFsXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJybyBhbyBhdHVhbGl6YXIgbWVuc2FnZW06JywgZXJyb3IpO1xuICAgICAgbG9hZENoYXRNZXNzYWdlcyhhY3R1YWxDaGF0SWQpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVFZGl0QW5kUmVnZW5lcmF0ZSA9IGFzeW5jIChtZXNzYWdlSWQ6IHN0cmluZywgbmV3Q29udGVudDogc3RyaW5nKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgaWYgKCFhY3R1YWxDaGF0SWQgfHwgIXVzZXI/LmVtYWlsKSByZXR1cm47XG5cbiAgICBjb25zb2xlLmxvZygn4pyP77iP8J+UhCBJbmljaWFuZG8gZWRpw6fDo28gZSByZWdlbmVyYcOnw6NvIGRlIG1lbnNhZ2VtOicsIHtcbiAgICAgIG1lc3NhZ2VJZCxcbiAgICAgIGNoYXRJZDogYWN0dWFsQ2hhdElkLFxuICAgICAgbmV3Q29udGVudExlbmd0aDogbmV3Q29udGVudC5sZW5ndGgsXG4gICAgICBuZXdDb250ZW50UHJldmlldzogbmV3Q29udGVudC5zdWJzdHJpbmcoMCwgMTAwKSArICcuLi4nXG4gICAgfSk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gMS4gUHJpbWVpcm8sIHNhbHZhciBhIGVkacOnw6NvXG4gICAgICBjb25zdCBlZGl0U3VjY2VzcyA9IGF3YWl0IGhhbmRsZUVkaXRNZXNzYWdlKG1lc3NhZ2VJZCwgbmV3Q29udGVudCk7XG4gICAgICBpZiAoIWVkaXRTdWNjZXNzKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBGYWxoYSBhbyBlZGl0YXIgbWVuc2FnZW0sIGNhbmNlbGFuZG8gcmVnZW5lcmHDp8OjbycpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfinIUgTWVuc2FnZW0gZWRpdGFkYSBjb20gc3VjZXNzbywgaW5pY2lhbmRvIHJlZ2VuZXJhw6fDo28uLi4nKTtcblxuICAgICAgLy8gMi4gQWd1YXJkYXIgdW0gcG91Y28gcGFyYSBnYXJhbnRpciBxdWUgYSBlZGnDp8OjbyBmb2kgc2FsdmFcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAyMDApKTtcblxuICAgICAgLy8gMy4gUmVjYXJyZWdhciBtZW5zYWdlbnMgZG8gRmlyZWJhc2UgU3RvcmFnZSBwYXJhIGdhcmFudGlyIGVzdGFkbyBhdHVhbGl6YWRvXG4gICAgICBjb25zb2xlLmxvZygn8J+UhCBSZWNhcnJlZ2FuZG8gbWVuc2FnZW5zIGFudGVzIGRhIHJlZ2VuZXJhw6fDo28uLi4nKTtcblxuICAgICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcblxuICAgICAgLy8gQ2FycmVnYXIgbWVuc2FnZW5zIGRpcmV0YW1lbnRlIGRvIEZpcmViYXNlIFN0b3JhZ2VcbiAgICAgIGNvbnN0IGZyZXNoTWVzc2FnZXMgPSBhd2FpdCBhaVNlcnZpY2UubG9hZENoYXRNZXNzYWdlcyh1c2VybmFtZSwgYWN0dWFsQ2hhdElkKTtcbiAgICAgIGNvbnN0IGNvbnZlcnRlZEZyZXNoTWVzc2FnZXMgPSBhaVNlcnZpY2UuY29udmVydEZyb21BSUZvcm1hdChmcmVzaE1lc3NhZ2VzKTtcblxuICAgICAgY29uc29sZS5sb2coJ/Cfk6UgTWVuc2FnZW5zIHJlY2FycmVnYWRhcyBkbyBTdG9yYWdlOicsIGNvbnZlcnRlZEZyZXNoTWVzc2FnZXMubGVuZ3RoKTtcblxuICAgICAgLy8gNC4gQnVzY2FyIG8gw61uZGljZSBkYSBtZW5zYWdlbSBuYXMgbWVuc2FnZW5zIGZyZXNjYXNcbiAgICAgIGNvbnN0IG1lc3NhZ2VJbmRleCA9IGNvbnZlcnRlZEZyZXNoTWVzc2FnZXMuZmluZEluZGV4KG1zZyA9PiBtc2cuaWQgPT09IG1lc3NhZ2VJZCk7XG4gICAgICBpZiAobWVzc2FnZUluZGV4ID09PSAtMSkge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgTWVuc2FnZW0gbsOjbyBlbmNvbnRyYWRhIGFww7NzIHJlY2FycmVnYXI6JywgbWVzc2FnZUlkKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBtZXNzYWdlVG9SZWdlbmVyYXRlID0gY29udmVydGVkRnJlc2hNZXNzYWdlc1ttZXNzYWdlSW5kZXhdO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk50gTWVuc2FnZW0gcXVlIHNlcsOhIHJlZ2VuZXJhZGE6Jywge1xuICAgICAgICBpZDogbWVzc2FnZVRvUmVnZW5lcmF0ZS5pZCxcbiAgICAgICAgY29udGVudDogbWVzc2FnZVRvUmVnZW5lcmF0ZS5jb250ZW50LnN1YnN0cmluZygwLCAxMDApICsgJy4uLicsXG4gICAgICAgIGluZGV4OiBtZXNzYWdlSW5kZXhcbiAgICAgIH0pO1xuXG4gICAgICAvLyA1LiBWZXJpZmljYXIgc2UgaMOhIG1lbnNhZ2VucyBhcMOzcyBlc3RhIG1lbnNhZ2VtXG4gICAgICBjb25zdCBoYXNNZXNzYWdlc0FmdGVyID0gbWVzc2FnZUluZGV4IDwgY29udmVydGVkRnJlc2hNZXNzYWdlcy5sZW5ndGggLSAxO1xuICAgICAgY29uc29sZS5sb2coYPCfk4ogTWVuc2FnZW5zIGFww7NzIGVzdGE6ICR7Y29udmVydGVkRnJlc2hNZXNzYWdlcy5sZW5ndGggLSBtZXNzYWdlSW5kZXggLSAxfWApO1xuXG4gICAgICAvLyA2LiBSZW1vdmVyIGFwZW5hcyBhcyBtZW5zYWdlbnMgQVDDk1MgYSBtZW5zYWdlbSBzZWxlY2lvbmFkYSAobsOjbyBpbmNsdWluZG8gZWxhKVxuICAgICAgY29uc3QgbWVzc2FnZXNCZWZvcmVSZWdlbmVyYXRpb24gPSBjb252ZXJ0ZWRGcmVzaE1lc3NhZ2VzLnNsaWNlKDAsIG1lc3NhZ2VJbmRleCArIDEpO1xuICAgICAgc2V0TWVzc2FnZXMobWVzc2FnZXNCZWZvcmVSZWdlbmVyYXRpb24pO1xuXG4gICAgICAvLyA3LiBQcmVwYXJhciBvIGNvbnRlw7pkbyBkYSBtZW5zYWdlbSBwYXJhIHJlZ2VuZXJhclxuICAgICAgc2V0TWVzc2FnZShtZXNzYWdlVG9SZWdlbmVyYXRlLmNvbnRlbnQpO1xuICAgICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgICAgc2V0SXNTdHJlYW1pbmcodHJ1ZSk7XG5cbiAgICAgIC8vIDguIFByZXBhcmFyIElEIHBhcmEgYSBub3ZhIG1lbnNhZ2VtIGRhIElBIHF1ZSBzZXLDoSBjcmlhZGEgZHVyYW50ZSBvIHN0cmVhbWluZ1xuICAgICAgY29uc3QgYWlNZXNzYWdlSWQgPSBhaVNlcnZpY2UuZ2VuZXJhdGVNZXNzYWdlSWQoKTtcbiAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2VJZChhaU1lc3NhZ2VJZCk7XG5cbiAgICAgIC8vIDkuIERlbGV0YXIgYXBlbmFzIGFzIG1lbnNhZ2VucyBQT1NURVJJT1JFUyBkbyBGaXJlYmFzZSBTdG9yYWdlIChzZSBob3V2ZXIpXG4gICAgICBpZiAoaGFzTWVzc2FnZXNBZnRlcikge1xuICAgICAgICBjb25zb2xlLmxvZyhg8J+Xke+4jyBEZWxldGFuZG8gJHtjb252ZXJ0ZWRGcmVzaE1lc3NhZ2VzLmxlbmd0aCAtIG1lc3NhZ2VJbmRleCAtIDF9IG1lbnNhZ2VucyBwb3N0ZXJpb3Jlcy4uLmApO1xuICAgICAgICBmb3IgKGxldCBpID0gbWVzc2FnZUluZGV4ICsgMTsgaSA8IGNvbnZlcnRlZEZyZXNoTWVzc2FnZXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICBjb25zdCBtc2dUb0RlbGV0ZSA9IGNvbnZlcnRlZEZyZXNoTWVzc2FnZXNbaV07XG4gICAgICAgICAgY29uc29sZS5sb2coJ/Cfl5HvuI8gRGVsZXRhbmRvIG1lbnNhZ2VtOicsIG1zZ1RvRGVsZXRlLmlkKTtcbiAgICAgICAgICBhd2FpdCBhaVNlcnZpY2UuZGVsZXRlTWVzc2FnZSh1c2VybmFtZSwgYWN0dWFsQ2hhdElkLCBtc2dUb0RlbGV0ZS5pZCk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gMTAuIEVudmlhciBwYXJhIGEgSUEgcGFyYSByZWdlbmVyYXJcbiAgICAgIGF3YWl0IGFpU2VydmljZS5zZW5kTWVzc2FnZVNhZmUoXG4gICAgICAgIHtcbiAgICAgICAgICB1c2VybmFtZTogdXNlcm5hbWUsXG4gICAgICAgICAgY2hhdElkOiBhY3R1YWxDaGF0SWQsXG4gICAgICAgICAgbWVzc2FnZTogbWVzc2FnZVRvUmVnZW5lcmF0ZS5jb250ZW50LFxuICAgICAgICAgIG1vZGVsOiBzZWxlY3RlZE1vZGVsLFxuICAgICAgICAgIGlzUmVnZW5lcmF0aW9uOiB0cnVlLFxuICAgICAgICAgIHVzZXJNZXNzYWdlSWQ6IG1lc3NhZ2VUb1JlZ2VuZXJhdGUuaWQsIC8vIFBhc3NhciBvIElEIGRhIG1lbnNhZ2VtIHF1ZSBlc3TDoSBzZW5kbyByZWdlbmVyYWRhXG4gICAgICAgIH0sXG4gICAgICAgIC8vIG9uQ2h1bmsgLSBjcmlhciBtZW5zYWdlbSBuYSBwcmltZWlyYSBjaHVuayBlIGF0dWFsaXphciBjb20gY2FkYSBjaHVua1xuICAgICAgICAoY2h1bms6IHN0cmluZykgPT4ge1xuICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4ge1xuICAgICAgICAgICAgLy8gVmVyaWZpY2FyIHNlIGEgbWVuc2FnZW0gZGEgSUEgasOhIGV4aXN0ZVxuICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdNZXNzYWdlSW5kZXggPSBwcmV2LmZpbmRJbmRleChtc2cgPT4gbXNnLmlkID09PSBhaU1lc3NhZ2VJZCk7XG5cbiAgICAgICAgICAgIGlmIChleGlzdGluZ01lc3NhZ2VJbmRleCAhPT0gLTEpIHtcbiAgICAgICAgICAgICAgLy8gQXR1YWxpemFyIG1lbnNhZ2VtIGV4aXN0ZW50ZVxuICAgICAgICAgICAgICByZXR1cm4gcHJldi5tYXAobXNnID0+XG4gICAgICAgICAgICAgICAgbXNnLmlkID09PSBhaU1lc3NhZ2VJZFxuICAgICAgICAgICAgICAgICAgPyB7IC4uLm1zZywgY29udGVudDogbXNnLmNvbnRlbnQgKyBjaHVuayB9XG4gICAgICAgICAgICAgICAgICA6IG1zZ1xuICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgLy8gQ3JpYXIgbm92YSBtZW5zYWdlbSBkYSBJQSBuYSBwcmltZWlyYSBjaHVua1xuICAgICAgICAgICAgICAvLyBSZW1vdmVyIG8gaW5kaWNhZG9yIGRlIGxvYWRpbmcgYXNzaW0gcXVlIGEgcHJpbWVpcmEgY2h1bmsgY2hlZ2FyXG4gICAgICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG5cbiAgICAgICAgICAgICAgY29uc3QgYWlNZXNzYWdlOiBNZXNzYWdlID0ge1xuICAgICAgICAgICAgICAgIGlkOiBhaU1lc3NhZ2VJZCxcbiAgICAgICAgICAgICAgICBjb250ZW50OiBjaHVuayxcbiAgICAgICAgICAgICAgICBzZW5kZXI6ICdhaScsXG4gICAgICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICAgICAgaGFzV2ViU2VhcmNoOiBmYWxzZSwgLy8gUmVnZW5lcmHDp8OjbyBuw6NvIHVzYSB3ZWIgc2VhcmNoIHBvciBwYWRyw6NvXG4gICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgIHJldHVybiBbLi4ucHJldiwgYWlNZXNzYWdlXTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcbiAgICAgICAgfSxcbiAgICAgICAgLy8gb25Db21wbGV0ZSAtIGZpbmFsaXphciBzdHJlYW1pbmdcbiAgICAgICAgKGZ1bGxSZXNwb25zZTogc3RyaW5nKSA9PiB7XG4gICAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBwcmV2Lm1hcChtc2cgPT5cbiAgICAgICAgICAgIG1zZy5pZCA9PT0gYWlNZXNzYWdlSWRcbiAgICAgICAgICAgICAgPyB7IC4uLm1zZywgY29udGVudDogZnVsbFJlc3BvbnNlIH1cbiAgICAgICAgICAgICAgOiBtc2dcbiAgICAgICAgICApKTtcbiAgICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgICAgIHNldElzU3RyZWFtaW5nKGZhbHNlKTtcbiAgICAgICAgICBzZXRTdHJlYW1pbmdNZXNzYWdlSWQobnVsbCk7XG4gICAgICAgICAgc2V0TWVzc2FnZSgnJyk7IC8vIExpbXBhciBvIGNhbXBvIGRlIGlucHV0XG5cbiAgICAgICAgICAvLyBBdHVhbGl6YXIgc2FsZG8gZG8gT3BlblJvdXRlciBhcMOzcyBhIHJlZ2VuZXJhw6fDo28gY29tIGRlbGF5IGRlIDUgc2VndW5kb3NcbiAgICAgICAgICBpZiAob25VcGRhdGVPcGVuUm91dGVyQmFsYW5jZSkge1xuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgIG9uVXBkYXRlT3BlblJvdXRlckJhbGFuY2UoKTtcbiAgICAgICAgICAgIH0sIDUwMDApO1xuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgLy8gb25FcnJvciAtIHRyYXRhciBlcnJvc1xuICAgICAgICAoZXJyb3I6IHN0cmluZykgPT4ge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gbmEgcmVnZW5lcmHDp8OjbzonLCBlcnJvcik7XG4gICAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBwcmV2Lm1hcChtc2cgPT5cbiAgICAgICAgICAgIG1zZy5pZCA9PT0gYWlNZXNzYWdlSWRcbiAgICAgICAgICAgICAgPyB7IC4uLm1zZywgY29udGVudDogYOKdjCBFcnJvIG5hIHJlZ2VuZXJhw6fDo286ICR7ZXJyb3J9YCB9XG4gICAgICAgICAgICAgIDogbXNnXG4gICAgICAgICAgKSk7XG4gICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgICAgICBzZXRJc1N0cmVhbWluZyhmYWxzZSk7XG4gICAgICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZUlkKG51bGwpO1xuICAgICAgICAgIHNldE1lc3NhZ2UoJycpOyAvLyBMaW1wYXIgbyBjYW1wbyBkZSBpbnB1dFxuICAgICAgICB9XG4gICAgICApO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvIGFvIGVkaXRhciBlIHJlZ2VuZXJhciBtZW5zYWdlbTonLCBlcnJvcik7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xuICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZUlkKG51bGwpO1xuICAgICAgc2V0TWVzc2FnZSgnJyk7IC8vIExpbXBhciBvIGNhbXBvIGRlIGlucHV0XG5cbiAgICAgIC8vIFJlY2FycmVnYXIgbWVuc2FnZW5zIGVtIGNhc28gZGUgZXJyb1xuICAgICAgbG9hZENoYXRNZXNzYWdlcyhhY3R1YWxDaGF0SWQpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVDb3B5TWVzc2FnZSA9IChjb250ZW50OiBzdHJpbmcpID0+IHtcbiAgICBuYXZpZ2F0b3IuY2xpcGJvYXJkLndyaXRlVGV4dChjb250ZW50KS50aGVuKCgpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKCdNZW5zYWdlbSBjb3BpYWRhIHBhcmEgYSDDoXJlYSBkZSB0cmFuc2ZlcsOqbmNpYScpO1xuICAgIH0pO1xuICB9O1xuXG4gIC8vIEZ1bsOnw7VlcyBkZSBuYXZlZ2HDp8Ojb1xuICBjb25zdCBoYW5kbGVTY3JvbGxUb1RvcCA9ICgpID0+IHtcbiAgICBjb25zdCBzY3JvbGxDb250YWluZXIgPSBjaGF0SW50ZXJmYWNlUmVmLmN1cnJlbnQ/LnF1ZXJ5U2VsZWN0b3IoJy5vdmVyZmxvdy15LWF1dG8nKTtcbiAgICBpZiAoc2Nyb2xsQ29udGFpbmVyKSB7XG4gICAgICBzY3JvbGxDb250YWluZXIuc2Nyb2xsVG8oeyB0b3A6IDAsIGJlaGF2aW9yOiAnc21vb3RoJyB9KTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2Nyb2xsVG9Cb3R0b20gPSAoKSA9PiB7XG4gICAgY29uc3Qgc2Nyb2xsQ29udGFpbmVyID0gY2hhdEludGVyZmFjZVJlZi5jdXJyZW50Py5xdWVyeVNlbGVjdG9yKCcub3ZlcmZsb3cteS1hdXRvJyk7XG4gICAgaWYgKHNjcm9sbENvbnRhaW5lcikge1xuICAgICAgc2Nyb2xsQ29udGFpbmVyLnNjcm9sbFRvKHtcbiAgICAgICAgdG9wOiBzY3JvbGxDb250YWluZXIuc2Nyb2xsSGVpZ2h0LFxuICAgICAgICBiZWhhdmlvcjogJ3Ntb290aCdcbiAgICAgIH0pO1xuICAgIH1cbiAgfTtcblxuXG5cbiAgLy8gRnVuw6fDo28gcGFyYSBjb252ZXJ0ZXIgbWVuc2FnZW5zIHBhcmEgbyBmb3JtYXRvIENoYXRNZXNzYWdlXG4gIGNvbnN0IGNvbnZlcnRUb0NoYXRNZXNzYWdlcyA9IChtZXNzYWdlczogTWVzc2FnZVtdKTogQ2hhdE1lc3NhZ2VbXSA9PiB7XG4gICAgcmV0dXJuIG1lc3NhZ2VzLm1hcChtc2cgPT4gKHtcbiAgICAgIGlkOiBtc2cuaWQsXG4gICAgICBjb250ZW50OiBtc2cuY29udGVudCxcbiAgICAgIHJvbGU6IG1zZy5zZW5kZXIgPT09ICd1c2VyJyA/ICd1c2VyJyA6ICdhc3Npc3RhbnQnLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZShtc2cudGltZXN0YW1wKS5nZXRUaW1lKCksXG4gICAgICBpc0Zhdm9yaXRlOiBtc2cuaXNGYXZvcml0ZSB8fCBmYWxzZSxcbiAgICAgIGF0dGFjaG1lbnRzOiBtc2cuYXR0YWNobWVudHMgfHwgW11cbiAgICB9KSk7XG4gIH07XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBhYnJpciBvIG1vZGFsIGRlIGRvd25sb2FkXG4gIGNvbnN0IGhhbmRsZURvd25sb2FkTW9kYWwgPSAoY2hhdElkPzogc3RyaW5nKSA9PiB7XG4gICAgLy8gU2UgdW0gY2hhdElkIGVzcGVjw61maWNvIGZvaSBmb3JuZWNpZG8gZSDDqSBkaWZlcmVudGUgZG8gYXR1YWwsIG11ZGEgcGFyYSBlc3NlIGNoYXQgcHJpbWVpcm9cbiAgICBpZiAoY2hhdElkICYmIGNoYXRJZCAhPT0gY3VycmVudENoYXQgJiYgb25DaGF0U2VsZWN0KSB7XG4gICAgICBvbkNoYXRTZWxlY3QoY2hhdElkKTtcbiAgICAgIC8vIEFicmUgbyBtb2RhbCBhcMOzcyB1bSBwZXF1ZW5vIGRlbGF5IHBhcmEgZ2FyYW50aXIgcXVlIG8gY2hhdCBmb2kgY2FycmVnYWRvXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgc2V0SXNEb3dubG9hZE1vZGFsT3Blbih0cnVlKTtcbiAgICAgIH0sIDEwMCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHNldElzRG93bmxvYWRNb2RhbE9wZW4odHJ1ZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgYWJyaXIgbyBtb2RhbCBkZSBhbmV4b3NcbiAgY29uc3QgaGFuZGxlQXR0YWNobWVudHNNb2RhbCA9IChjaGF0SWQ/OiBzdHJpbmcpID0+IHtcbiAgICAvLyBTZSB1bSBjaGF0SWQgZXNwZWPDrWZpY28gZm9pIGZvcm5lY2lkbyBlIMOpIGRpZmVyZW50ZSBkbyBhdHVhbCwgbXVkYSBwYXJhIGVzc2UgY2hhdCBwcmltZWlyb1xuICAgIGlmIChjaGF0SWQgJiYgY2hhdElkICE9PSBjdXJyZW50Q2hhdCAmJiBvbkNoYXRTZWxlY3QpIHtcbiAgICAgIG9uQ2hhdFNlbGVjdChjaGF0SWQpO1xuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHNldElzQXR0YWNobWVudHNNb2RhbE9wZW4odHJ1ZSk7XG4gICAgICB9LCAxMDApO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZXRJc0F0dGFjaG1lbnRzTW9kYWxPcGVuKHRydWUpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIGFicmlyIG8gbW9kYWwgZGUgZXN0YXTDrXN0aWNhc1xuICBjb25zdCBoYW5kbGVTdGF0aXN0aWNzTW9kYWwgPSAoY2hhdElkPzogc3RyaW5nKSA9PiB7XG4gICAgLy8gU2UgdW0gY2hhdElkIGVzcGVjw61maWNvIGZvaSBmb3JuZWNpZG8gZSDDqSBkaWZlcmVudGUgZG8gYXR1YWwsIG11ZGEgcGFyYSBlc3NlIGNoYXQgcHJpbWVpcm9cbiAgICBpZiAoY2hhdElkICYmIGNoYXRJZCAhPT0gY3VycmVudENoYXQgJiYgb25DaGF0U2VsZWN0KSB7XG4gICAgICBvbkNoYXRTZWxlY3QoY2hhdElkKTtcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBzZXRJc1N0YXRpc3RpY3NNb2RhbE9wZW4odHJ1ZSk7XG4gICAgICB9LCAxMDApO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZXRJc1N0YXRpc3RpY3NNb2RhbE9wZW4odHJ1ZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEV4cG9yIGFzIGZ1bsOnw7VlcyBwYXJhIG8gY29tcG9uZW50ZSBwYWlcbiAgdXNlSW1wZXJhdGl2ZUhhbmRsZShyZWYsICgpID0+ICh7XG4gICAgaGFuZGxlRG93bmxvYWRNb2RhbCxcbiAgICBoYW5kbGVBdHRhY2htZW50c01vZGFsLFxuICAgIGhhbmRsZVN0YXRpc3RpY3NNb2RhbFxuICB9KSk7XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBvYnRlciB0b2RvcyBvcyBhbmV4b3MgZG8gY2hhdFxuICBjb25zdCBnZXRBbGxDaGF0QXR0YWNobWVudHMgPSAoKTogaW1wb3J0KCdAL2xpYi90eXBlcy9jaGF0JykuQXR0YWNobWVudE1ldGFkYXRhW10gPT4ge1xuICAgIGNvbnN0IGFsbEF0dGFjaG1lbnRzOiBpbXBvcnQoJ0AvbGliL3R5cGVzL2NoYXQnKS5BdHRhY2htZW50TWV0YWRhdGFbXSA9IFtdO1xuXG4gICAgbWVzc2FnZXMuZm9yRWFjaChtZXNzYWdlID0+IHtcbiAgICAgIGlmIChtZXNzYWdlLmF0dGFjaG1lbnRzICYmIG1lc3NhZ2UuYXR0YWNobWVudHMubGVuZ3RoID4gMCkge1xuICAgICAgICBhbGxBdHRhY2htZW50cy5wdXNoKC4uLm1lc3NhZ2UuYXR0YWNobWVudHMpO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgLy8gUmVtb3ZlciBkdXBsaWNhdGFzIGJhc2VhZG8gbm8gSURcbiAgICBjb25zdCB1bmlxdWVBdHRhY2htZW50cyA9IGFsbEF0dGFjaG1lbnRzLmZpbHRlcigoYXR0YWNobWVudCwgaW5kZXgsIHNlbGYpID0+XG4gICAgICBpbmRleCA9PT0gc2VsZi5maW5kSW5kZXgoYSA9PiBhLmlkID09PSBhdHRhY2htZW50LmlkKVxuICAgICk7XG5cbiAgICByZXR1cm4gdW5pcXVlQXR0YWNobWVudHM7XG4gIH07XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBzYWx2YXIgbyBlc3RhZG8gZG9zIGFuZXhvcyBubyBGaXJlYmFzZSBTdG9yYWdlXG4gIGNvbnN0IHNhdmVBdHRhY2htZW50U3RhdGVzID0gYXN5bmMgKHVwZGF0ZWRNZXNzYWdlczogTWVzc2FnZVtdKSA9PiB7XG4gICAgaWYgKCFjdXJyZW50VXNlcm5hbWUgfHwgIWFjdHVhbENoYXRJZCkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIFByZXBhcmFyIGRhZG9zIGRvIGNoYXQgcGFyYSBzYWx2YXJcbiAgICAgIGNvbnN0IGNoYXREYXRhID0ge1xuICAgICAgICBpZDogYWN0dWFsQ2hhdElkLFxuICAgICAgICBuYW1lOiBjaGF0TmFtZSB8fCAnQ2hhdCcsXG4gICAgICAgIG1lc3NhZ2VzOiB1cGRhdGVkTWVzc2FnZXMubWFwKG1zZyA9PiAoe1xuICAgICAgICAgIGlkOiBtc2cuaWQsXG4gICAgICAgICAgY29udGVudDogbXNnLmNvbnRlbnQsXG4gICAgICAgICAgcm9sZTogbXNnLnNlbmRlciA9PT0gJ3VzZXInID8gJ3VzZXInIDogJ2Fzc2lzdGFudCcsXG4gICAgICAgICAgdGltZXN0YW1wOiBtc2cudGltZXN0YW1wLFxuICAgICAgICAgIGlzRmF2b3JpdGU6IG1zZy5pc0Zhdm9yaXRlLFxuICAgICAgICAgIGF0dGFjaG1lbnRzOiBtc2cuYXR0YWNobWVudHNcbiAgICAgICAgfSkpLFxuICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgbGFzdFVwZGF0ZWQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfTtcblxuICAgICAgLy8gU2FsdmFyIG5vIEZpcmViYXNlIFN0b3JhZ2VcbiAgICAgIGNvbnN0IGNoYXRKc29uQmxvYiA9IG5ldyBCbG9iKFtKU09OLnN0cmluZ2lmeShjaGF0RGF0YSwgbnVsbCwgMildLCB7XG4gICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi9qc29uJ1xuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IGNoYXRKc29uUmVmID0gc3RvcmFnZVJlZihzdG9yYWdlLCBgdXN1YXJpb3MvJHtjdXJyZW50VXNlcm5hbWV9L2NvbnZlcnNhcy8ke2FjdHVhbENoYXRJZH0vY2hhdC5qc29uYCk7XG4gICAgICBhd2FpdCB1cGxvYWRCeXRlcyhjaGF0SnNvblJlZiwgY2hhdEpzb25CbG9iKTtcblxuICAgICAgY29uc29sZS5sb2coJ+KchSBFc3RhZG8gZG9zIGFuZXhvcyBzYWx2byBubyBGaXJlYmFzZSBTdG9yYWdlJyk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TgSBEYWRvcyBzYWx2b3M6Jywge1xuICAgICAgICBjaGF0SWQ6IGFjdHVhbENoYXRJZCxcbiAgICAgICAgdG90YWxNZXNzYWdlczogY2hhdERhdGEubWVzc2FnZXMubGVuZ3RoLFxuICAgICAgICBtZXNzYWdlc1dpdGhBdHRhY2htZW50czogY2hhdERhdGEubWVzc2FnZXMuZmlsdGVyKG1zZyA9PiBtc2cuYXR0YWNobWVudHMgJiYgbXNnLmF0dGFjaG1lbnRzLmxlbmd0aCA+IDApLmxlbmd0aFxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvIGFvIHNhbHZhciBlc3RhZG8gZG9zIGFuZXhvczonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgYWx0ZXJuYXIgbyBlc3RhZG8gYXRpdm8gZGUgdW0gYW5leG9cbiAgY29uc3QgaGFuZGxlVG9nZ2xlQXR0YWNobWVudCA9IChhdHRhY2htZW50SWQ6IHN0cmluZykgPT4ge1xuICAgIHNldE1lc3NhZ2VzKHByZXZNZXNzYWdlcyA9PiB7XG4gICAgICBjb25zdCB1cGRhdGVkTWVzc2FnZXMgPSBwcmV2TWVzc2FnZXMubWFwKG1lc3NhZ2UgPT4ge1xuICAgICAgICBpZiAobWVzc2FnZS5hdHRhY2htZW50cyAmJiBtZXNzYWdlLmF0dGFjaG1lbnRzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICBjb25zdCB1cGRhdGVkQXR0YWNobWVudHMgPSBtZXNzYWdlLmF0dGFjaG1lbnRzLm1hcChhdHRhY2htZW50ID0+IHtcbiAgICAgICAgICAgIGlmIChhdHRhY2htZW50LmlkID09PSBhdHRhY2htZW50SWQpIHtcbiAgICAgICAgICAgICAgLy8gU2UgaXNBY3RpdmUgbsOjbyBlc3TDoSBkZWZpbmlkbywgY29uc2lkZXJhciBjb21vIHRydWUgKGF0aXZvIHBvciBwYWRyw6NvKVxuICAgICAgICAgICAgICBjb25zdCBjdXJyZW50U3RhdGUgPSBhdHRhY2htZW50LmlzQWN0aXZlICE9PSBmYWxzZTtcbiAgICAgICAgICAgICAgcmV0dXJuIHsgLi4uYXR0YWNobWVudCwgaXNBY3RpdmU6ICFjdXJyZW50U3RhdGUgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBhdHRhY2htZW50O1xuICAgICAgICAgIH0pO1xuICAgICAgICAgIHJldHVybiB7IC4uLm1lc3NhZ2UsIGF0dGFjaG1lbnRzOiB1cGRhdGVkQXR0YWNobWVudHMgfTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbWVzc2FnZTtcbiAgICAgIH0pO1xuXG4gICAgICAvLyBTYWx2YXIgbyBlc3RhZG8gYXR1YWxpemFkbyBubyBGaXJlYmFzZSBTdG9yYWdlXG4gICAgICBzYXZlQXR0YWNobWVudFN0YXRlcyh1cGRhdGVkTWVzc2FnZXMpO1xuXG4gICAgICByZXR1cm4gdXBkYXRlZE1lc3NhZ2VzO1xuICAgIH0pO1xuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgZmlsdHJhciBhbmV4b3MgYXRpdm9zIHBhcmEgZW52aW8gw6AgSUFcbiAgY29uc3QgZ2V0QWN0aXZlQXR0YWNobWVudHMgPSAoYXR0YWNobWVudHM/OiBpbXBvcnQoJ0AvbGliL3R5cGVzL2NoYXQnKS5BdHRhY2htZW50TWV0YWRhdGFbXSk6IGltcG9ydCgnQC9saWIvdHlwZXMvY2hhdCcpLkF0dGFjaG1lbnRNZXRhZGF0YVtdID0+IHtcbiAgICBpZiAoIWF0dGFjaG1lbnRzKSByZXR1cm4gW107XG5cbiAgICAvLyBQYXJhIGFuZXhvcyBub3ZvcyAoc2VtIGlzQWN0aXZlIGRlZmluaWRvKSwgaW5jbHVpciBwb3IgcGFkcsOjb1xuICAgIC8vIFBhcmEgYW5leG9zIGV4aXN0ZW50ZXMsIHZlcmlmaWNhciBzZSBpc0FjdGl2ZSBuw6NvIMOpIGZhbHNlXG4gICAgcmV0dXJuIGF0dGFjaG1lbnRzLmZpbHRlcihhdHRhY2htZW50ID0+IHtcbiAgICAgIC8vIFNlIGlzQWN0aXZlIG7Do28gZXN0w6EgZGVmaW5pZG8gKGFuZXhvIG5vdm8pLCBpbmNsdWlyXG4gICAgICBpZiAoYXR0YWNobWVudC5pc0FjdGl2ZSA9PT0gdW5kZWZpbmVkKSByZXR1cm4gdHJ1ZTtcbiAgICAgIC8vIFNlIGlzQWN0aXZlIGVzdMOhIGRlZmluaWRvLCBpbmNsdWlyIGFwZW5hcyBzZSBuw6NvIGZvciBmYWxzZVxuICAgICAgcmV0dXJuIGF0dGFjaG1lbnQuaXNBY3RpdmUgIT09IGZhbHNlO1xuICAgIH0pO1xuICB9O1xuXG5cblxuICAvLyBGdW7Dp8OjbyBwYXJhIG9idGVyIElEcyBkb3MgYW5leG9zIGF0aXZvc1xuICBjb25zdCBnZXRBY3RpdmVBdHRhY2htZW50SWRzID0gKCk6IHN0cmluZ1tdID0+IHtcbiAgICBjb25zdCBhbGxBdHRhY2htZW50cyA9IGdldEFsbENoYXRBdHRhY2htZW50cygpO1xuICAgIHJldHVybiBhbGxBdHRhY2htZW50cy5maWx0ZXIoYXR0ID0+IGF0dC5pc0FjdGl2ZSAhPT0gZmFsc2UpLm1hcChhdHQgPT4gYXR0LmlkKTtcbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIHJlc2V0YXIgZXN0YWRvIGRlIGRyYWdcbiAgY29uc3QgcmVzZXREcmFnU3RhdGUgPSAoKSA9PiB7XG4gICAgc2V0SXNEcmFnT3ZlcihmYWxzZSk7XG4gICAgc2V0RHJhZ0NvdW50ZXIoMCk7XG4gICAgaWYgKGRyYWdUaW1lb3V0KSB7XG4gICAgICBjbGVhclRpbWVvdXQoZHJhZ1RpbWVvdXQpO1xuICAgICAgc2V0RHJhZ1RpbWVvdXQobnVsbCk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZ1bsOnw7VlcyBwYXJhIGRyYWctbi1kcm9wXG4gIGNvbnN0IGhhbmRsZURyYWdFbnRlciA9IChlOiBSZWFjdC5EcmFnRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcblxuICAgIHNldERyYWdDb3VudGVyKHByZXYgPT4gcHJldiArIDEpO1xuXG4gICAgLy8gVmVyaWZpY2FyIHNlIGjDoSBhcnF1aXZvcyBzZW5kbyBhcnJhc3RhZG9zXG4gICAgaWYgKGUuZGF0YVRyYW5zZmVyLml0ZW1zICYmIGUuZGF0YVRyYW5zZmVyLml0ZW1zLmxlbmd0aCA+IDApIHtcbiAgICAgIC8vIFZlcmlmaWNhciBzZSBwZWxvIG1lbm9zIHVtIGl0ZW0gw6kgdW0gYXJxdWl2b1xuICAgICAgY29uc3QgaGFzRmlsZXMgPSBBcnJheS5mcm9tKGUuZGF0YVRyYW5zZmVyLml0ZW1zKS5zb21lKGl0ZW0gPT4gaXRlbS5raW5kID09PSAnZmlsZScpO1xuICAgICAgaWYgKGhhc0ZpbGVzKSB7XG4gICAgICAgIHNldElzRHJhZ092ZXIodHJ1ZSk7XG5cbiAgICAgICAgLy8gTGltcGFyIHRpbWVvdXQgYW50ZXJpb3Igc2UgZXhpc3RpclxuICAgICAgICBpZiAoZHJhZ1RpbWVvdXQpIHtcbiAgICAgICAgICBjbGVhclRpbWVvdXQoZHJhZ1RpbWVvdXQpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gRGVmaW5pciB0aW1lb3V0IGRlIHNlZ3VyYW7Dp2EgcGFyYSByZXNldGFyIG8gZXN0YWRvIGFww7NzIDMgc2VndW5kb3NcbiAgICAgICAgY29uc3QgdGltZW91dCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgIHJlc2V0RHJhZ1N0YXRlKCk7XG4gICAgICAgIH0sIDMwMDApO1xuICAgICAgICBzZXREcmFnVGltZW91dCh0aW1lb3V0KTtcbiAgICAgIH1cbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRHJhZ0xlYXZlID0gKGU6IFJlYWN0LkRyYWdFdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuXG4gICAgc2V0RHJhZ0NvdW50ZXIocHJldiA9PiB7XG4gICAgICBjb25zdCBuZXdDb3VudCA9IHByZXYgLSAxO1xuICAgICAgLy8gU8OzIHJlbW92ZSBvIG92ZXJsYXkgcXVhbmRvIG8gY29udGFkb3IgY2hlZ2EgYSAwXG4gICAgICBpZiAobmV3Q291bnQgPD0gMCkge1xuICAgICAgICByZXNldERyYWdTdGF0ZSgpO1xuICAgICAgICByZXR1cm4gMDtcbiAgICAgIH1cbiAgICAgIHJldHVybiBuZXdDb3VudDtcbiAgICB9KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEcmFnT3ZlciA9IChlOiBSZWFjdC5EcmFnRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcblxuICAgIC8vIFZlcmlmaWNhciBzZSBow6EgYXJxdWl2b3Mgc2VuZG8gYXJyYXN0YWRvc1xuICAgIGlmIChlLmRhdGFUcmFuc2Zlci5pdGVtcyAmJiBlLmRhdGFUcmFuc2Zlci5pdGVtcy5sZW5ndGggPiAwKSB7XG4gICAgICBjb25zdCBoYXNGaWxlcyA9IEFycmF5LmZyb20oZS5kYXRhVHJhbnNmZXIuaXRlbXMpLnNvbWUoaXRlbSA9PiBpdGVtLmtpbmQgPT09ICdmaWxlJyk7XG4gICAgICBpZiAoaGFzRmlsZXMpIHtcbiAgICAgICAgZS5kYXRhVHJhbnNmZXIuZHJvcEVmZmVjdCA9ICdjb3B5JztcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGUuZGF0YVRyYW5zZmVyLmRyb3BFZmZlY3QgPSAnbm9uZSc7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURyb3AgPSBhc3luYyAoZTogUmVhY3QuRHJhZ0V2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG5cbiAgICAvLyBTZW1wcmUgcmVzZXRhciBvIGVzdGFkbyBkZSBkcmFnLCBpbmRlcGVuZGVudGVtZW50ZSBkbyBxdWUgYWNvbnRlY2VyXG4gICAgcmVzZXREcmFnU3RhdGUoKTtcblxuICAgIGNvbnN0IGZpbGVzID0gQXJyYXkuZnJvbShlLmRhdGFUcmFuc2Zlci5maWxlcyk7XG5cbiAgICAvLyBTZSBuw6NvIGjDoSBhcnF1aXZvcywgYXBlbmFzIHNhaXJcbiAgICBpZiAoZmlsZXMubGVuZ3RoID09PSAwKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gVmVyaWZpY2FyIHNlIHRlbW9zIHVzZXJuYW1lIG5lY2Vzc8OhcmlvXG4gICAgaWYgKCFjdXJyZW50VXNlcm5hbWUpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1VzZXJuYW1lIG7Do28gZGlzcG9uw612ZWwgcGFyYSB1cGxvYWQgZGUgYW5leG9zJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gU2UgbsOjbyBow6EgY2hhdCBhdHVhbCwgY3JpYXIgdW0gYXV0b21hdGljYW1lbnRlIHBhcmEgcG9kZXIgZmF6ZXIgdXBsb2FkIGRvcyBhbmV4b3NcbiAgICBsZXQgY2hhdElkVG9Vc2UgPSBhY3R1YWxDaGF0SWQ7XG4gICAgaWYgKCFjaGF0SWRUb1VzZSkge1xuICAgICAgY29uc3QgbWVzc2FnZUZvckNoYXQgPSBmaWxlcy5sZW5ndGggPT09IDFcbiAgICAgICAgPyBgQXJxdWl2byBhbmV4YWRvOiAke2ZpbGVzWzBdLm5hbWV9YFxuICAgICAgICA6IGAke2ZpbGVzLmxlbmd0aH0gYXJxdWl2b3MgYW5leGFkb3NgO1xuICAgICAgY2hhdElkVG9Vc2UgPSBhd2FpdCBjcmVhdGVBdXRvQ2hhdChtZXNzYWdlRm9yQ2hhdCk7XG4gICAgICBpZiAoY2hhdElkVG9Vc2UpIHtcbiAgICAgICAgc2V0QWN0dWFsQ2hhdElkKGNoYXRJZFRvVXNlKTtcbiAgICAgICAgbG9hZENoYXROYW1lKGNoYXRJZFRvVXNlKTtcbiAgICAgICAgb25DaGF0Q3JlYXRlZD8uKGNoYXRJZFRvVXNlKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoIWNoYXRJZFRvVXNlKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdOw6NvIGZvaSBwb3Nzw612ZWwgY3JpYXIgb3Ugb2J0ZXIgY2hhdCBJRCBwYXJhIGFuZXhvcycpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICAvLyBJbXBvcnRhciBvIGF0dGFjaG1lbnRTZXJ2aWNlIGRpbmFtaWNhbWVudGVcbiAgICAgIGNvbnN0IHsgZGVmYXVsdDogYXR0YWNobWVudFNlcnZpY2UgfSA9IGF3YWl0IGltcG9ydCgnQC9saWIvc2VydmljZXMvYXR0YWNobWVudFNlcnZpY2UnKTtcblxuICAgICAgLy8gRmF6ZXIgdXBsb2FkIGRvcyBhcnF1aXZvc1xuICAgICAgY29uc3QgdXBsb2FkZWRBdHRhY2htZW50cyA9IGF3YWl0IGF0dGFjaG1lbnRTZXJ2aWNlLnVwbG9hZE11bHRpcGxlQXR0YWNobWVudHMoXG4gICAgICAgIGZpbGVzLFxuICAgICAgICBjdXJyZW50VXNlcm5hbWUsXG4gICAgICAgIGNoYXRJZFRvVXNlXG4gICAgICApO1xuXG4gICAgICAvLyBFbSB2ZXogZGUgZW52aWFyIGEgbWVuc2FnZW0sIHZhbW9zIG5vdGlmaWNhciBvIElucHV0QmFyIHNvYnJlIG9zIG5vdm9zIGFuZXhvc1xuICAgICAgLy8gSXNzbyBzZXLDoSBmZWl0byBhdHJhdsOpcyBkZSB1bSBldmVudG8gY3VzdG9taXphZG9cbiAgICAgIGNvbnN0IGF0dGFjaG1lbnRNZXRhZGF0YSA9IHVwbG9hZGVkQXR0YWNobWVudHMubWFwKGF0dCA9PiBhdHQubWV0YWRhdGEpO1xuXG4gICAgICAvLyBEaXNwYXJhciBldmVudG8gY3VzdG9taXphZG8gcGFyYSBvIElucHV0QmFyIGNhcHR1cmFyXG4gICAgICBjb25zdCBldmVudCA9IG5ldyBDdXN0b21FdmVudCgnZHJhZ0Ryb3BBdHRhY2htZW50cycsIHtcbiAgICAgICAgZGV0YWlsOiB7XG4gICAgICAgICAgYXR0YWNobWVudHM6IGF0dGFjaG1lbnRNZXRhZGF0YSxcbiAgICAgICAgICBjaGF0SWQ6IGNoYXRJZFRvVXNlLFxuICAgICAgICAgIHVzZXJuYW1lOiBjdXJyZW50VXNlcm5hbWVcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChldmVudCk7XG5cbiAgICAgIGNvbnNvbGUubG9nKGDinIUgJHtmaWxlcy5sZW5ndGh9IGFycXVpdm8ocykgYWRpY2lvbmFkbyhzKSBjb21vIGFuZXhvIHZpYSBkcmFnLW4tZHJvcGApO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvIGFvIHByb2Nlc3NhciBhcnF1aXZvcyB2aWEgZHJhZy1uLWRyb3A6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGZsZXgtY29sIGgtc2NyZWVuIHJlbGF0aXZlXCJcbiAgICAgIG9uRHJhZ0VudGVyPXtoYW5kbGVEcmFnRW50ZXJ9XG4gICAgICBvbkRyYWdMZWF2ZT17aGFuZGxlRHJhZ0xlYXZlfVxuICAgICAgb25EcmFnT3Zlcj17aGFuZGxlRHJhZ092ZXJ9XG4gICAgICBvbkRyb3A9e2hhbmRsZURyb3B9XG4gICAgPlxuICAgICAgey8qIERyYWctbi1Ecm9wIE92ZXJsYXkgKi99XG4gICAgICB7aXNEcmFnT3ZlciAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCB6LTUwIGJnLWJsdWUtOTAwLzgwIGJhY2tkcm9wLWJsdXItc20gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWJsdWUtODAwLzkwIGJhY2tkcm9wLWJsdXItbWQgcm91bmRlZC0yeGwgcC04IGJvcmRlci0yIGJvcmRlci1kYXNoZWQgYm9yZGVyLWJsdWUtNDAwIHNoYWRvdy0yeGxcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgdGV4dC1ibHVlLTMwMCBteC1hdXRvIGFuaW1hdGUtYm91bmNlXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNyAxNmE0IDQgMCAwMS0uODgtNy45MDNBNSA1IDAgMTExNS45IDZMMTYgNmE1IDUgMCAwMTEgOS45TTE1IDEzbC0zLTNtMCAwbC0zIDNtMy0zdjEyXCIgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ibHVlLTEwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgU29sdGUgb3MgYXJxdWl2b3MgYXF1aVxuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtMzAwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICBPcyBhcnF1aXZvcyBzZXLDo28gYWRpY2lvbmFkb3MgY29tbyBhbmV4b3Mgw6AgY29udmVyc2FcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuXG5cbiAgICAgIHsvKiBDaGF0SW50ZXJmYWNlICovfVxuICAgICAgPGRpdiByZWY9e2NoYXRJbnRlcmZhY2VSZWZ9IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4taC0wXCIgc3R5bGU9e3sgaGVpZ2h0OiAnY2FsYygxMDB2aCAtIDE0MHB4KScgfX0+XG4gICAgICAgIDxDaGF0SW50ZXJmYWNlXG4gICAgICAgICAgY2hhdElkPXthY3R1YWxDaGF0SWQgfHwgJ3RlbXAtY2hhdCd9XG4gICAgICAgICAgbWVzc2FnZXM9e21lc3NhZ2VzfVxuICAgICAgICAgIGlzTG9hZGluZz17aXNMb2FkaW5nfVxuICAgICAgICAgIGlzTG9hZGluZ0NoYXQ9e2lzTG9hZGluZ0NoYXR9XG4gICAgICAgICAgaXNTdHJlYW1pbmc9e2lzU3RyZWFtaW5nfVxuICAgICAgICAgIHN0cmVhbWluZ01lc3NhZ2VJZD17c3RyZWFtaW5nTWVzc2FnZUlkIHx8IHVuZGVmaW5lZH1cbiAgICAgICAgICBlbmFibGVTZXNzaW9ucz17YXBwZWFyYW5jZVNldHRpbmdzLnNlc3Npb25zRW5hYmxlZH1cbiAgICAgICAgICBvbkRlbGV0ZU1lc3NhZ2U9e2hhbmRsZURlbGV0ZU1lc3NhZ2V9XG4gICAgICAgICAgb25SZWdlbmVyYXRlTWVzc2FnZT17aGFuZGxlUmVnZW5lcmF0ZU1lc3NhZ2V9XG4gICAgICAgICAgb25FZGl0TWVzc2FnZT17aGFuZGxlRWRpdE1lc3NhZ2V9XG4gICAgICAgICAgb25FZGl0QW5kUmVnZW5lcmF0ZT17aGFuZGxlRWRpdEFuZFJlZ2VuZXJhdGV9XG4gICAgICAgICAgb25Db3B5TWVzc2FnZT17aGFuZGxlQ29weU1lc3NhZ2V9XG4gICAgICAgIC8+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIElucHV0QmFyICovfVxuICAgICAgPElucHV0QmFyXG4gICAgICAgIG1lc3NhZ2U9e21lc3NhZ2V9XG4gICAgICAgIHNldE1lc3NhZ2U9e3NldE1lc3NhZ2V9XG4gICAgICAgIG9uU2VuZE1lc3NhZ2U9e2hhbmRsZVNlbmRNZXNzYWdlfVxuICAgICAgICBpc0xvYWRpbmc9e2lzTG9hZGluZ31cbiAgICAgICAgc2VsZWN0ZWRNb2RlbD17c2VsZWN0ZWRNb2RlbH1cbiAgICAgICAgb25Nb2RlbENoYW5nZT17aGFuZGxlTW9kZWxDaGFuZ2V9XG4gICAgICAgIG9uU2Nyb2xsVG9Ub3A9e2hhbmRsZVNjcm9sbFRvVG9wfVxuICAgICAgICBvblNjcm9sbFRvQm90dG9tPXtoYW5kbGVTY3JvbGxUb0JvdHRvbX1cbiAgICAgICAgaXNTdHJlYW1pbmc9e2lzU3RyZWFtaW5nfVxuICAgICAgICBvbkNhbmNlbFN0cmVhbWluZz17aGFuZGxlQ2FuY2VsU3RyZWFtaW5nfVxuICAgICAgICBvbk9wZW5Nb2RlbE1vZGFsPXsoKSA9PiBzZXRJc01vZGVsTW9kYWxPcGVuKHRydWUpfVxuICAgICAgICBvbk9wZW5BdHRhY2htZW50c01vZGFsPXtoYW5kbGVBdHRhY2htZW50c01vZGFsfVxuICAgICAgICB1c2VybmFtZT17Y3VycmVudFVzZXJuYW1lfVxuICAgICAgICBjaGF0SWQ9e2FjdHVhbENoYXRJZCB8fCB1bmRlZmluZWR9XG4gICAgICAgIGFjdGl2ZUF0dGFjaG1lbnRzQ291bnQ9e2dldEFjdGl2ZUF0dGFjaG1lbnRJZHMoKS5sZW5ndGh9XG4gICAgICAvPlxuXG4gICAgICB7LyogRG93bmxvYWQgTW9kYWwgKi99XG4gICAgICA8RG93bmxvYWRNb2RhbFxuICAgICAgICBpc09wZW49e2lzRG93bmxvYWRNb2RhbE9wZW59XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldElzRG93bmxvYWRNb2RhbE9wZW4oZmFsc2UpfVxuICAgICAgICBtZXNzYWdlcz17Y29udmVydFRvQ2hhdE1lc3NhZ2VzKG1lc3NhZ2VzKX1cbiAgICAgICAgY2hhdE5hbWU9e2NoYXROYW1lfVxuICAgICAgLz5cblxuICAgICAgey8qIE1vZGVsIFNlbGVjdGlvbiBNb2RhbCAqL31cbiAgICAgIDxNb2RlbFNlbGVjdGlvbk1vZGFsXG4gICAgICAgIGlzT3Blbj17aXNNb2RlbE1vZGFsT3Blbn1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNNb2RlbE1vZGFsT3BlbihmYWxzZSl9XG4gICAgICAgIGN1cnJlbnRNb2RlbD17c2VsZWN0ZWRNb2RlbH1cbiAgICAgICAgb25Nb2RlbFNlbGVjdD17aGFuZGxlTW9kZWxDaGFuZ2V9XG4gICAgICAvPlxuXG4gICAgICB7LyogQXR0YWNobWVudHMgTW9kYWwgKi99XG4gICAgICA8QXR0YWNobWVudHNNb2RhbFxuICAgICAgICBpc09wZW49e2lzQXR0YWNobWVudHNNb2RhbE9wZW59XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldElzQXR0YWNobWVudHNNb2RhbE9wZW4oZmFsc2UpfVxuICAgICAgICBhdHRhY2htZW50cz17Z2V0QWxsQ2hhdEF0dGFjaG1lbnRzKCl9XG4gICAgICAgIGFjdGl2ZUF0dGFjaG1lbnRzPXtnZXRBY3RpdmVBdHRhY2htZW50SWRzKCl9XG4gICAgICAgIG9uVG9nZ2xlQXR0YWNobWVudD17aGFuZGxlVG9nZ2xlQXR0YWNobWVudH1cbiAgICAgIC8+XG5cbiAgICAgIHsvKiBTdGF0aXN0aWNzIE1vZGFsICovfVxuICAgICAgPFN0YXRpc3RpY3NNb2RhbFxuICAgICAgICBpc09wZW49e2lzU3RhdGlzdGljc01vZGFsT3Blbn1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNTdGF0aXN0aWNzTW9kYWxPcGVuKGZhbHNlKX1cbiAgICAgICAgbWVzc2FnZXM9e2NvbnZlcnRUb0NoYXRNZXNzYWdlcyhtZXNzYWdlcyl9XG4gICAgICAgIGNoYXROYW1lPXtjaGF0TmFtZX1cbiAgICAgIC8+XG4gICAgPC9kaXY+XG4gICk7XG59KTtcblxuQ2hhdEFyZWEuZGlzcGxheU5hbWUgPSAnQ2hhdEFyZWEnO1xuXG5leHBvcnQgZGVmYXVsdCBDaGF0QXJlYTtcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsImZvcndhcmRSZWYiLCJ1c2VJbXBlcmF0aXZlSGFuZGxlIiwiZG9jIiwic2V0RG9jIiwiY29sbGVjdGlvbiIsImdldERvYyIsInVwZGF0ZURvYyIsInF1ZXJ5Iiwid2hlcmUiLCJnZXREb2NzIiwicmVmIiwic3RvcmFnZVJlZiIsInVwbG9hZEJ5dGVzIiwiZGIiLCJzdG9yYWdlIiwidXNlQXV0aCIsInVzZUFwcGVhcmFuY2UiLCJDaGF0SW50ZXJmYWNlIiwiSW5wdXRCYXIiLCJEb3dubG9hZE1vZGFsIiwiTW9kZWxTZWxlY3Rpb25Nb2RhbCIsIkF0dGFjaG1lbnRzTW9kYWwiLCJTdGF0aXN0aWNzTW9kYWwiLCJhaVNlcnZpY2UiLCJDaGF0QXJlYSIsImN1cnJlbnRDaGF0Iiwib25DaGF0Q3JlYXRlZCIsIm9uVXBkYXRlT3BlblJvdXRlckJhbGFuY2UiLCJvbkNoYXRTZWxlY3QiLCJzaWRlYmFyUmVmIiwidXNlciIsInNldHRpbmdzIiwiYXBwZWFyYW5jZVNldHRpbmdzIiwibWVzc2FnZSIsInNldE1lc3NhZ2UiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJtZXNzYWdlcyIsInNldE1lc3NhZ2VzIiwic2VsZWN0ZWRNb2RlbCIsInNldFNlbGVjdGVkTW9kZWwiLCJhY3R1YWxDaGF0SWQiLCJzZXRBY3R1YWxDaGF0SWQiLCJpc0Rvd25sb2FkTW9kYWxPcGVuIiwic2V0SXNEb3dubG9hZE1vZGFsT3BlbiIsImlzTW9kZWxNb2RhbE9wZW4iLCJzZXRJc01vZGVsTW9kYWxPcGVuIiwiaXNBdHRhY2htZW50c01vZGFsT3BlbiIsInNldElzQXR0YWNobWVudHNNb2RhbE9wZW4iLCJpc1N0YXRpc3RpY3NNb2RhbE9wZW4iLCJzZXRJc1N0YXRpc3RpY3NNb2RhbE9wZW4iLCJpc1N0cmVhbWluZyIsInNldElzU3RyZWFtaW5nIiwic3RyZWFtaW5nTWVzc2FnZUlkIiwic2V0U3RyZWFtaW5nTWVzc2FnZUlkIiwiY2hhdE5hbWUiLCJzZXRDaGF0TmFtZSIsImlzTG9hZGluZ0NoYXQiLCJzZXRJc0xvYWRpbmdDaGF0IiwiY3VycmVudFVzZXJuYW1lIiwic2V0Q3VycmVudFVzZXJuYW1lIiwidW5kZWZpbmVkIiwiY2hhdEludGVyZmFjZVJlZiIsImlzRHJhZ092ZXIiLCJzZXRJc0RyYWdPdmVyIiwiZHJhZ0NvdW50ZXIiLCJzZXREcmFnQ291bnRlciIsImRyYWdUaW1lb3V0Iiwic2V0RHJhZ1RpbWVvdXQiLCJsb2FkVXNlcm5hbWUiLCJlbWFpbCIsInVzZXJuYW1lIiwiZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlIiwiaGFuZGxlV2luZG93RHJhZ0xlYXZlIiwiZSIsInJlbGF0ZWRUYXJnZXQiLCJoYW5kbGVXaW5kb3dEcmFnRW5kIiwiaGFuZGxlV2luZG93RHJvcCIsIndpbmRvdyIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwidXN1YXJpb3NSZWYiLCJxIiwicXVlcnlTbmFwc2hvdCIsImVtcHR5IiwidXNlckRvYyIsImRvY3MiLCJ1c2VyRGF0YSIsImRhdGEiLCJzcGxpdCIsImVycm9yIiwiY29uc29sZSIsInNhdmVMYXN0VXNlZE1vZGVsRm9yQ2hhdCIsIm1vZGVsSWQiLCJjaGF0SWQiLCJjaGF0UmVmIiwibGFzdFVzZWRNb2RlbCIsImxhc3RNb2RlbFVwZGF0ZUF0IiwiRGF0ZSIsIm5vdyIsImxvYWRMYXN0VXNlZE1vZGVsRm9yQ2hhdCIsImNoYXREb2MiLCJleGlzdHMiLCJpc1ZhbGlkIiwiaXNWYWxpZE1vZGVsIiwibG9hZERlZmF1bHRNb2RlbEZyb21BY3RpdmVFbmRwb2ludCIsInVzZXJSZWYiLCJlbmRwb2ludHMiLCJhY3RpdmVFbmRwb2ludCIsIk9iamVjdCIsInZhbHVlcyIsImZpbmQiLCJlbmRwb2ludCIsImF0aXZvIiwibW9kZWxvUGFkcmFvIiwiaW52YWxpZE1vZGVscyIsImluY2x1ZGVzIiwiY2xlYW51cEludmFsaWRNb2RlbHNGcm9tQWxsQ2hhdHMiLCJjaGF0c1JlZiIsImNoYXRzU25hcHNob3QiLCJ1cGRhdGVQcm9taXNlcyIsInB1c2giLCJpZCIsImxlbmd0aCIsIlByb21pc2UiLCJhbGwiLCJsb2FkR2xvYmFsTGFzdFVzZWRNb2RlbCIsImhhbmRsZU1vZGVsQ2hhbmdlIiwiY3JlYXRlQXV0b0NoYXQiLCJmaXJzdE1lc3NhZ2UiLCJ0aW1lc3RhbXAiLCJyYW5kb20iLCJNYXRoIiwidG9TdHJpbmciLCJzdWJzdHJpbmciLCJ0b0lTT1N0cmluZyIsImZpbmFsQ2hhdE5hbWUiLCJ0cmltIiwid29yZHMiLCJzbGljZSIsIm1pbiIsImpvaW4iLCJmaXJlc3RvcmVEYXRhIiwiY29udGV4dCIsImNyZWF0ZWRBdCIsImZvbGRlcklkIiwiZnJlcXVlbmN5UGVuYWx0eSIsImlzRml4ZWQiLCJsYXN0VXBkYXRlZEF0IiwibGF0ZXhJbnN0cnVjdGlvbnMiLCJtYXhUb2tlbnMiLCJuYW1lIiwicGFzc3dvcmQiLCJyZXBldGl0aW9uUGVuYWx0eSIsInNlc3Npb25UaW1lIiwibGFzdFNlc3Npb25TdGFydCIsImxhc3RVcGRhdGVkIiwidG90YWxUaW1lIiwic3lzdGVtUHJvbXB0IiwidGVtcGVyYXR1cmUiLCJ1bHRpbWFNZW5zYWdlbSIsInVsdGltYU1lbnNhZ2VtRW0iLCJ1cGRhdGVkQXQiLCJjaGF0SnNvbkRhdGEiLCJjaGF0SnNvbkJsb2IiLCJCbG9iIiwiSlNPTiIsInN0cmluZ2lmeSIsInR5cGUiLCJmaXJlYmFzZVN0b3JhZ2VSZWYiLCJsb2ciLCJoYW5kbGVTZW5kTWVzc2FnZSIsImF0dGFjaG1lbnRzIiwid2ViU2VhcmNoRW5hYmxlZCIsImhpc3RvcmljYWxBdHRhY2htZW50cyIsImdldEFsbENoYXRBdHRhY2htZW50cyIsImZpbHRlciIsImF0dCIsImlzQWN0aXZlIiwiYWxsQXR0YWNobWVudHNUb1NlbmQiLCJ1bmlxdWVBdHRhY2htZW50cyIsImF0dGFjaG1lbnQiLCJpbmRleCIsInNlbGYiLCJmaW5kSW5kZXgiLCJhIiwidXNlck1lc3NhZ2UiLCJnZW5lcmF0ZU1lc3NhZ2VJZCIsImNvbnRlbnQiLCJzZW5kZXIiLCJjaGF0SWRUb1VzZSIsIm1lc3NhZ2VGb3JDaGF0IiwibG9hZENoYXROYW1lIiwicHJldiIsImN1cnJlbnRNZXNzYWdlIiwiY3VycmVudCIsIm1vdmVUb1RvcCIsImFpTWVzc2FnZUlkIiwic2VuZE1lc3NhZ2VTYWZlIiwibW9kZWwiLCJ1c2VyTWVzc2FnZUlkIiwiY2h1bmsiLCJleGlzdGluZ01lc3NhZ2VJbmRleCIsIm1zZyIsIm1hcCIsImFpTWVzc2FnZSIsImhhc1dlYlNlYXJjaCIsImZ1bGxSZXNwb25zZSIsInNldFRpbWVvdXQiLCJoYW5kbGVDYW5jZWxTdHJlYW1pbmciLCJjYW5jZWxSZXF1ZXN0IiwiY2hhdERhdGEiLCJsb2FkQ2hhdE1lc3NhZ2VzIiwiY2hhdE1lc3NhZ2VzIiwiY29udmVydGVkTWVzc2FnZXMiLCJjb252ZXJ0RnJvbUFJRm9ybWF0IiwiaGFuZGxlRGVsZXRlTWVzc2FnZSIsIm1lc3NhZ2VJZCIsInN1Y2Nlc3MiLCJkZWxldGVNZXNzYWdlIiwiaGFuZGxlUmVnZW5lcmF0ZU1lc3NhZ2UiLCJmcmVzaE1lc3NhZ2VzIiwiY29udmVydGVkRnJlc2hNZXNzYWdlcyIsIm1lc3NhZ2VJbmRleCIsIm1lc3NhZ2VUb1JlZ2VuZXJhdGUiLCJtZXNzYWdlc0JlZm9yZVJlZ2VuZXJhdGlvbiIsImkiLCJtc2dUb0RlbGV0ZSIsImlzUmVnZW5lcmF0aW9uIiwiaGFuZGxlRWRpdE1lc3NhZ2UiLCJuZXdDb250ZW50IiwibmV3Q29udGVudExlbmd0aCIsIm5ld0NvbnRlbnRQcmV2aWV3IiwidXBkYXRlTWVzc2FnZSIsImhhbmRsZUVkaXRBbmRSZWdlbmVyYXRlIiwiZWRpdFN1Y2Nlc3MiLCJyZXNvbHZlIiwiaGFzTWVzc2FnZXNBZnRlciIsImhhbmRsZUNvcHlNZXNzYWdlIiwibmF2aWdhdG9yIiwiY2xpcGJvYXJkIiwid3JpdGVUZXh0IiwidGhlbiIsImhhbmRsZVNjcm9sbFRvVG9wIiwic2Nyb2xsQ29udGFpbmVyIiwicXVlcnlTZWxlY3RvciIsInNjcm9sbFRvIiwidG9wIiwiYmVoYXZpb3IiLCJoYW5kbGVTY3JvbGxUb0JvdHRvbSIsInNjcm9sbEhlaWdodCIsImNvbnZlcnRUb0NoYXRNZXNzYWdlcyIsInJvbGUiLCJnZXRUaW1lIiwiaXNGYXZvcml0ZSIsImhhbmRsZURvd25sb2FkTW9kYWwiLCJoYW5kbGVBdHRhY2htZW50c01vZGFsIiwiaGFuZGxlU3RhdGlzdGljc01vZGFsIiwiYWxsQXR0YWNobWVudHMiLCJmb3JFYWNoIiwic2F2ZUF0dGFjaG1lbnRTdGF0ZXMiLCJ1cGRhdGVkTWVzc2FnZXMiLCJjaGF0SnNvblJlZiIsInRvdGFsTWVzc2FnZXMiLCJtZXNzYWdlc1dpdGhBdHRhY2htZW50cyIsImhhbmRsZVRvZ2dsZUF0dGFjaG1lbnQiLCJhdHRhY2htZW50SWQiLCJwcmV2TWVzc2FnZXMiLCJ1cGRhdGVkQXR0YWNobWVudHMiLCJjdXJyZW50U3RhdGUiLCJnZXRBY3RpdmVBdHRhY2htZW50cyIsImdldEFjdGl2ZUF0dGFjaG1lbnRJZHMiLCJyZXNldERyYWdTdGF0ZSIsImNsZWFyVGltZW91dCIsImhhbmRsZURyYWdFbnRlciIsInByZXZlbnREZWZhdWx0Iiwic3RvcFByb3BhZ2F0aW9uIiwiZGF0YVRyYW5zZmVyIiwiaXRlbXMiLCJoYXNGaWxlcyIsIkFycmF5IiwiZnJvbSIsInNvbWUiLCJpdGVtIiwia2luZCIsInRpbWVvdXQiLCJoYW5kbGVEcmFnTGVhdmUiLCJuZXdDb3VudCIsImhhbmRsZURyYWdPdmVyIiwiZHJvcEVmZmVjdCIsImhhbmRsZURyb3AiLCJmaWxlcyIsImRlZmF1bHQiLCJhdHRhY2htZW50U2VydmljZSIsInVwbG9hZGVkQXR0YWNobWVudHMiLCJ1cGxvYWRNdWx0aXBsZUF0dGFjaG1lbnRzIiwiYXR0YWNobWVudE1ldGFkYXRhIiwibWV0YWRhdGEiLCJldmVudCIsIkN1c3RvbUV2ZW50IiwiZGV0YWlsIiwiZGlzcGF0Y2hFdmVudCIsImRpdiIsImNsYXNzTmFtZSIsIm9uRHJhZ0VudGVyIiwib25EcmFnTGVhdmUiLCJvbkRyYWdPdmVyIiwib25Ecm9wIiwic3ZnIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIiwiaDMiLCJwIiwic3R5bGUiLCJoZWlnaHQiLCJlbmFibGVTZXNzaW9ucyIsInNlc3Npb25zRW5hYmxlZCIsIm9uRGVsZXRlTWVzc2FnZSIsIm9uUmVnZW5lcmF0ZU1lc3NhZ2UiLCJvbkVkaXRNZXNzYWdlIiwib25FZGl0QW5kUmVnZW5lcmF0ZSIsIm9uQ29weU1lc3NhZ2UiLCJvblNlbmRNZXNzYWdlIiwib25Nb2RlbENoYW5nZSIsIm9uU2Nyb2xsVG9Ub3AiLCJvblNjcm9sbFRvQm90dG9tIiwib25DYW5jZWxTdHJlYW1pbmciLCJvbk9wZW5Nb2RlbE1vZGFsIiwib25PcGVuQXR0YWNobWVudHNNb2RhbCIsImFjdGl2ZUF0dGFjaG1lbnRzQ291bnQiLCJpc09wZW4iLCJvbkNsb3NlIiwiY3VycmVudE1vZGVsIiwib25Nb2RlbFNlbGVjdCIsImFjdGl2ZUF0dGFjaG1lbnRzIiwib25Ub2dnbGVBdHRhY2htZW50IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});