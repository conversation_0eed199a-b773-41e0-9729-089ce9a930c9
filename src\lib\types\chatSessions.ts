import { ChatMessage } from './chat';

/**
 * Interface para uma sessão de chat
 * IMPORTANTE: Sessões são apenas para visualização/performance da UI.
 * Para envio à IA, sempre usar TODAS as mensagens do chat como contexto.
 */
export interface ChatSession {
  /** ID único da sessão */
  id: string;
  
  /** Índice da primeira mensagem na sessão (baseado no array original) */
  startMessageIndex: number;
  
  /** Índice da última mensagem na sessão (baseado no array original) */
  endMessageIndex: number;
  
  /** Número total de palavras na sessão */
  wordCount: number;
  
  /** Mensagens da sessão (apenas para visualização) */
  messages: ChatMessage[];
  
  /** Timestamp de criação da sessão */
  createdAt: number;
  
  /** Timestamp da última atualização */
  lastUpdated: number;
  
  /** Se a sessão está carregada na memória para visualização */
  isLoaded: boolean;
  
  /** Se <PERSON> a sessão mais recente (ativa) */
  isActive: boolean;
}

/**
 * Interface para metadados de sessões de um chat
 */
export interface ChatSessionsMetadata {
  /** ID do chat */
  chatId: string;
  
  /** Número total de sessões */
  totalSessions: number;
  
  /** Número de palavras por sessão configurado */
  wordsPerSession: number;
  
  /** ID da sessão ativa (mais recente) */
  activeSessionId: string;
  
  /** Lista de IDs das sessões em ordem cronológica */
  sessionIds: string[];
  
  /** Timestamp da última divisão em sessões */
  lastSessionUpdate: number;
  
  /** Número total de mensagens no chat */
  totalMessages: number;
  
  /** Número total de palavras no chat */
  totalWords: number;
}

/**
 * Interface para configurações do sistema de sessões
 */
export interface SessionsConfig {
  /** Se o sistema de sessões está habilitado */
  enabled: boolean;
  
  /** Número de palavras por sessão */
  wordsPerSession: number;
  
  /** Número máximo de sessões a manter carregadas simultaneamente */
  maxLoadedSessions: number;
  
  /** Se deve carregar automaticamente sessões anteriores quando necessário */
  autoLoadPreviousSessions: boolean;
}

/**
 * Interface para resultado de divisão de mensagens em sessões
 */
export interface SessionDivisionResult {
  /** Sessões criadas */
  sessions: ChatSession[];
  
  /** Metadados das sessões */
  metadata: ChatSessionsMetadata;
  
  /** Estatísticas da divisão */
  stats: {
    totalSessions: number;
    averageWordsPerSession: number;
    totalWords: number;
    totalMessages: number;
  };
}

/**
 * Interface para estado de carregamento de sessões
 */
export interface SessionLoadingState {
  /** Se está carregando sessões */
  isLoading: boolean;
  
  /** ID da sessão sendo carregada */
  loadingSessionId?: string;
  
  /** Erro de carregamento, se houver */
  error?: string;
  
  /** Progresso do carregamento (0-100) */
  progress: number;
}

/**
 * Interface para navegação entre sessões
 */
export interface SessionNavigation {
  /** Sessão atual */
  currentSession: ChatSession | null;
  
  /** Se há sessão anterior */
  hasPrevious: boolean;
  
  /** Se há próxima sessão */
  hasNext: boolean;
  
  /** Índice da sessão atual */
  currentIndex: number;
  
  /** Total de sessões */
  totalSessions: number;
}

/**
 * Interface para contexto completo do chat (para IA)
 * IMPORTANTE: Sempre usar todas as mensagens, independente das sessões
 */
export interface ChatContext {
  /** Todas as mensagens do chat (contexto completo para IA) */
  allMessages: ChatMessage[];
  
  /** Sessões apenas para visualização */
  sessions: ChatSession[];
  
  /** Metadados das sessões */
  metadata: ChatSessionsMetadata;
  
  /** Sessão atualmente visualizada */
  currentViewSession: ChatSession | null;
}

/**
 * Tipo para eventos do sistema de sessões
 */
export type SessionEvent = 
  | { type: 'SESSION_CREATED'; session: ChatSession }
  | { type: 'SESSION_LOADED'; sessionId: string }
  | { type: 'SESSION_UNLOADED'; sessionId: string }
  | { type: 'SESSIONS_REORGANIZED'; metadata: ChatSessionsMetadata }
  | { type: 'SESSION_NAVIGATION_CHANGED'; navigation: SessionNavigation };

/**
 * Interface para serviço de gerenciamento de sessões
 */
export interface ChatSessionsService {
  /** Dividir mensagens em sessões (apenas para visualização) */
  divideIntoSessions(messages: ChatMessage[], wordsPerSession: number): SessionDivisionResult;
  
  /** Carregar uma sessão específica para visualização */
  loadSessionForView(chatId: string, sessionId: string): Promise<ChatSession>;
  
  /** Descarregar uma sessão da memória */
  unloadSession(sessionId: string): void;
  
  /** Obter metadados das sessões de um chat */
  getSessionsMetadata(chatId: string): Promise<ChatSessionsMetadata | null>;
  
  /** Reorganizar sessões após mudanças nas mensagens */
  reorganizeSessions(chatId: string, messages: ChatMessage[]): Promise<SessionDivisionResult>;
  
  /** Navegar para uma sessão específica (apenas visualização) */
  navigateToSession(sessionId: string): Promise<SessionNavigation>;
  
  /** Obter sessão ativa (mais recente) para visualização */
  getActiveSession(chatId: string): Promise<ChatSession | null>;
  
  /** Obter contexto completo do chat (TODAS as mensagens para IA) */
  getFullChatContext(chatId: string): Promise<ChatContext>;
}
