"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/Sidebar.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _CreateChatModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CreateChatModal */ \"(app-pages-browser)/./src/components/dashboard/CreateChatModal.tsx\");\n/* harmony import */ var _CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CreateFolderModal */ \"(app-pages-browser)/./src/components/dashboard/CreateFolderModal.tsx\");\n/* harmony import */ var _ConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ConfirmDeleteModal */ \"(app-pages-browser)/./src/components/dashboard/ConfirmDeleteModal.tsx\");\n/* harmony import */ var _PasswordProtectedModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./PasswordProtectedModal */ \"(app-pages-browser)/./src/components/dashboard/PasswordProtectedModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst Sidebar = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { userData, isOpen, isCollapsed = false, onToggle, onSettingsOpen, onChatSelect, currentChat, onUpdateOpenRouterBalance, showCloseButton = true, onDownloadModal, onAttachmentsModal, onStatisticsModal } = param;\n    _s();\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unorganizedChats, setUnorganizedChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openRouterBalance, setOpenRouterBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        balance: 0,\n        isLoading: true,\n        error: undefined\n    });\n    const [createChatModalOpen, setCreateChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editChatModalOpen, setEditChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingChat, setEditingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [createFolderModalOpen, setCreateFolderModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editFolderModalOpen, setEditFolderModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingFolder, setEditingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [draggedChat, setDraggedChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverFolder, setDragOverFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredFolder, setHoveredFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animatingChat, setAnimatingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [configDropdownOpen, setConfigDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estados para modais de confirmação\n    const [deleteConfirmModal, setDeleteConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        type: \"chat\",\n        id: \"\",\n        name: \"\"\n    });\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para modal de senha\n    const [passwordModal, setPasswordModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        chatId: \"\",\n        chatName: \"\",\n        action: \"access\"\n    });\n    // Ref para controlar requisições em andamento\n    const fetchingBalanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const fetchOpenRouterBalance = async ()=>{\n        // Evitar múltiplas requisições simultâneas\n        if (fetchingBalanceRef.current) {\n            console.log(\"Requisi\\xe7\\xe3o de saldo j\\xe1 em andamento, ignorando...\");\n            return;\n        }\n        try {\n            fetchingBalanceRef.current = true;\n            console.log(\"Iniciando busca do saldo do OpenRouter...\");\n            setOpenRouterBalance((prev)=>({\n                    ...prev,\n                    isLoading: true,\n                    error: undefined\n                }));\n            let openRouterApiKey = \"\";\n            // Primeiro, tentar buscar na nova estrutura (endpoints)\n            try {\n                const endpointsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"endpoints\");\n                const endpointsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(endpointsRef);\n                endpointsSnapshot.forEach((doc)=>{\n                    const data = doc.data();\n                    if (data.isActive && data.url && data.url.includes(\"openrouter.ai\")) {\n                        openRouterApiKey = data.apiKey;\n                    }\n                });\n            } catch (error) {\n                console.log(\"Nova estrutura n\\xe3o encontrada, tentando estrutura antiga...\");\n            }\n            // Se não encontrou na nova estrutura, buscar na estrutura antiga\n            if (!openRouterApiKey) {\n                try {\n                    const { doc: docImport, getDoc } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n                    const configRef = docImport(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"configuracoes\", \"settings\");\n                    const configDoc = await getDoc(configRef);\n                    if (configDoc.exists()) {\n                        const config = configDoc.data();\n                        if (config.endpoints) {\n                            // Buscar endpoint ativo do OpenRouter na estrutura antiga\n                            Object.values(config.endpoints).forEach((endpoint)=>{\n                                if (endpoint.ativo && endpoint.url && endpoint.url.includes(\"openrouter.ai\")) {\n                                    openRouterApiKey = endpoint.apiKey;\n                                }\n                            });\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"Estrutura antiga tamb\\xe9m n\\xe3o encontrada\");\n                }\n            }\n            if (!openRouterApiKey) {\n                setOpenRouterBalance((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: \"Nenhuma API key do OpenRouter configurada\"\n                    }));\n                return;\n            }\n            console.log(\"API Key encontrada, buscando saldo...\");\n            // Fazer requisição para buscar créditos\n            const response = await fetch(\"/api/openrouter/credits\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    apiKey: openRouterApiKey\n                })\n            });\n            const data = await response.json();\n            console.log(\"Resposta da API:\", data);\n            if (data.success) {\n                setOpenRouterBalance({\n                    balance: data.balance,\n                    isLoading: false,\n                    error: undefined\n                });\n                console.log(\"Saldo carregado com sucesso:\", data.balance);\n            } else {\n                setOpenRouterBalance((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: data.error || \"Erro ao buscar saldo\"\n                    }));\n                console.log(\"Erro ao buscar saldo:\", data.error);\n            }\n        } catch (error) {\n            console.error(\"Erro ao buscar saldo do OpenRouter:\", error);\n            setOpenRouterBalance((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: \"Erro ao conectar com OpenRouter\"\n                }));\n        } finally{\n            fetchingBalanceRef.current = false;\n        }\n    };\n    const loadChats = async ()=>{\n        try {\n            // Carregar chats\n            const chatsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\");\n            const chatsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(chatsRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)(\"lastUpdatedAt\", \"desc\"));\n            const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(chatsQuery);\n            const chats = [];\n            chatsSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                chats.push({\n                    id: doc.id,\n                    name: data.name || \"Conversa sem nome\",\n                    lastMessage: data.ultimaMensagem || \"Nenhuma mensagem ainda\",\n                    lastMessageTime: data.ultimaMensagemEm || data.createdAt,\n                    folder: data.folderId,\n                    password: data.password\n                });\n            });\n            // Carregar pastas\n            const foldersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\");\n            const foldersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(foldersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)(\"createdAt\", \"asc\"));\n            const foldersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(foldersQuery);\n            const loadedFolders = [];\n            foldersSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                const folderChats = chats.filter((chat)=>chat.folder === doc.id);\n                loadedFolders.push({\n                    id: doc.id,\n                    name: data.name,\n                    description: data.description,\n                    color: data.color || \"#3B82F6\",\n                    isExpanded: data.expandedByDefault !== false,\n                    chats: folderChats\n                });\n            });\n            // Chats sem pasta\n            const unorganized = chats.filter((chat)=>!chat.folder);\n            setFolders(loadedFolders);\n            setUnorganizedChats(unorganized);\n        } catch (error) {\n            console.error(\"Erro ao carregar chats e pastas:\", error);\n        }\n    };\n    // Ref para controlar se já foi carregado\n    const balanceLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (userData.username) {\n            loadChats();\n            // Carregar saldo apenas se ainda não foi carregado\n            if (!balanceLoadedRef.current) {\n                console.log(\"Carregando saldo do OpenRouter pela primeira vez...\");\n                fetchOpenRouterBalance();\n                balanceLoadedRef.current = true;\n            }\n        }\n    }, [\n        userData.username\n    ]);\n    // Função para mover chat para o topo com animação\n    const moveToTop = async (chatId)=>{\n        try {\n            // Animar o chat\n            setAnimatingChat(chatId);\n            // Atualizar o timestamp no Firestore para mover para o topo\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", chatId), {\n                lastUpdatedAt: new Date().toISOString()\n            });\n            // Recarregar chats após um pequeno delay para mostrar a animação\n            setTimeout(()=>{\n                loadChats();\n                setAnimatingChat(null);\n            }, 300);\n        } catch (error) {\n            console.error(\"Erro ao mover chat para o topo:\", error);\n            setAnimatingChat(null);\n        }\n    };\n    // Dropdown de configurações agora é gerenciado individualmente por cada ChatItem\n    // Expor as funções para o componente pai\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            reloadChats: loadChats,\n            updateOpenRouterBalance: fetchOpenRouterBalance,\n            moveToTop: moveToTop\n        }));\n    const handleNewChat = ()=>{\n        setCreateChatModalOpen(true);\n    };\n    const handleNewFolder = ()=>{\n        setCreateFolderModalOpen(true);\n    };\n    const handleFolderCreated = (folderId)=>{\n        console.log(\"Pasta criada:\", folderId);\n        loadChats(); // Recarregar para mostrar a nova pasta\n    };\n    const handleFolderUpdated = (folderId)=>{\n        console.log(\"Pasta atualizada:\", folderId);\n        loadChats(); // Recarregar para mostrar as alterações\n        setEditFolderModalOpen(false);\n        setEditingFolder(null);\n    };\n    const handleEditFolder = async (folderId)=>{\n        try {\n            var _folderDoc_docs_find;\n            // Buscar dados da pasta no Firestore\n            const folderDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\")));\n            const folderData = (_folderDoc_docs_find = folderDoc.docs.find((doc)=>doc.id === folderId)) === null || _folderDoc_docs_find === void 0 ? void 0 : _folderDoc_docs_find.data();\n            if (folderData) {\n                setEditingFolder({\n                    id: folderId,\n                    name: folderData.name,\n                    description: folderData.description,\n                    color: folderData.color || \"#3B82F6\",\n                    expandedByDefault: folderData.expandedByDefault !== false\n                });\n                setEditFolderModalOpen(true);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar dados da pasta:\", error);\n            alert(\"Erro ao carregar dados da pasta.\");\n        }\n    };\n    const handleDeleteFolder = (folderId, folderName)=>{\n        setDeleteConfirmModal({\n            isOpen: true,\n            type: \"folder\",\n            id: folderId,\n            name: folderName\n        });\n    };\n    const handleDeleteChat = (chatId, chatName)=>{\n        setDeleteConfirmModal({\n            isOpen: true,\n            type: \"chat\",\n            id: chatId,\n            name: chatName\n        });\n    };\n    // Função para deletar todos os anexos de um chat\n    const deleteChatAttachments = async (chatId)=>{\n        try {\n            const attachmentsRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(userData.username, \"/conversas/\").concat(chatId, \"/anexos\"));\n            const attachmentsList = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.listAll)(attachmentsRef);\n            // Deletar todos os arquivos de anexos\n            const deletePromises = attachmentsList.items.map((item)=>(0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.deleteObject)(item));\n            await Promise.all(deletePromises);\n            console.log(\"\".concat(attachmentsList.items.length, \" anexos deletados do chat \").concat(chatId));\n        } catch (error) {\n            console.log(\"Erro ao deletar anexos ou pasta de anexos n\\xe3o encontrada:\", error);\n        }\n    };\n    const confirmDelete = async ()=>{\n        setIsDeleting(true);\n        try {\n            if (deleteConfirmModal.type === \"folder\") {\n                var _folders_find;\n                // Deletar pasta\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\", deleteConfirmModal.id));\n                // Mover todos os chats da pasta para \"sem pasta\"\n                const chatsInFolder = ((_folders_find = folders.find((f)=>f.id === deleteConfirmModal.id)) === null || _folders_find === void 0 ? void 0 : _folders_find.chats) || [];\n                for (const chat of chatsInFolder){\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", chat.id), {\n                        folderId: null,\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n                console.log(\"Pasta deletada:\", deleteConfirmModal.id);\n            } else {\n                // Deletar chat\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", deleteConfirmModal.id));\n                // Deletar arquivo chat.json do Storage\n                try {\n                    const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(userData.username, \"/conversas/\").concat(deleteConfirmModal.id, \"/chat.json\"));\n                    await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.deleteObject)(chatJsonRef);\n                } catch (storageError) {\n                    console.log(\"Arquivo chat.json no storage n\\xe3o encontrado:\", storageError);\n                }\n                // Deletar todos os anexos do chat\n                await deleteChatAttachments(deleteConfirmModal.id);\n                // Se era o chat ativo, limpar seleção\n                if (currentChat === deleteConfirmModal.id) {\n                    onChatSelect(\"\");\n                }\n                console.log(\"Chat deletado:\", deleteConfirmModal.id);\n            }\n            // Recarregar dados\n            loadChats();\n            // Fechar modal\n            setDeleteConfirmModal({\n                isOpen: false,\n                type: \"chat\",\n                id: \"\",\n                name: \"\"\n            });\n        } catch (error) {\n            console.error(\"Erro ao deletar:\", error);\n            alert(\"Erro ao deletar. Tente novamente.\");\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // Funções de drag and drop\n    const handleDragStart = (chatId)=>{\n        setDraggedChat(chatId);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedChat(null);\n        setDragOverFolder(null);\n    };\n    const handleDragOver = (e, folderId)=>{\n        e.preventDefault();\n        setDragOverFolder(folderId);\n    };\n    const handleDragLeave = ()=>{\n        setDragOverFolder(null);\n    };\n    const handleDrop = async (e, folderId)=>{\n        e.preventDefault();\n        if (!draggedChat) return;\n        try {\n            // Atualizar o chat no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", draggedChat), {\n                folderId: folderId,\n                updatedAt: new Date().toISOString()\n            });\n            console.log(\"Chat \".concat(draggedChat, \" movido para pasta \").concat(folderId || \"sem pasta\"));\n            // Recarregar chats para refletir a mudança\n            loadChats();\n        } catch (error) {\n            console.error(\"Erro ao mover chat:\", error);\n        } finally{\n            setDraggedChat(null);\n            setDragOverFolder(null);\n        }\n    };\n    const handleChatCreated = (chatId)=>{\n        console.log(\"Chat criado:\", chatId);\n        // Recarregar a lista de chats\n        loadChats();\n        onChatSelect(chatId);\n    };\n    const handleEditChat = (chat)=>{\n        setEditingChat(chat);\n        setEditChatModalOpen(true);\n    };\n    const handleChatUpdated = (chatId)=>{\n        console.log(\"Chat atualizado:\", chatId);\n        // Recarregar a lista de chats\n        loadChats();\n        setEditChatModalOpen(false);\n        setEditingChat(null);\n    };\n    const handleHomeClick = ()=>{\n        onChatSelect(null); // Limpar chat atual para ir para área inicial\n    };\n    const toggleFolder = (folderId)=>{\n        setFolders((prev)=>prev.map((folder)=>folder.id === folderId ? {\n                    ...folder,\n                    isExpanded: !folder.isExpanded\n                } : folder));\n    };\n    const formatTime = (timeString)=>{\n        const date = new Date(timeString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return date.toLocaleTimeString(\"pt-BR\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffInHours < 168) {\n            return date.toLocaleDateString(\"pt-BR\", {\n                weekday: \"short\"\n            });\n        } else {\n            return date.toLocaleDateString(\"pt-BR\", {\n                day: \"2-digit\",\n                month: \"2-digit\"\n            });\n        }\n    };\n    const getFolderHexColor = (colorName)=>{\n        const colorMap = {\n            \"blue\": \"#3B82F6\",\n            \"green\": \"#10B981\",\n            \"yellow\": \"#F59E0B\",\n            \"red\": \"#EF4444\",\n            \"purple\": \"#8B5CF6\",\n            \"cyan\": \"#06B6D4\",\n            \"lime\": \"#84CC16\",\n            \"orange\": \"#F97316\",\n            \"pink\": \"#EC4899\",\n            \"gray\": \"#6B7280\"\n        };\n        return colorMap[colorName] || colorName;\n    };\n    // Funções para chat protegido por senha\n    const checkChatPassword = async (chatId, inputPassword)=>{\n        try {\n            var _chatDoc_docs_find;\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\")));\n            const chatData = (_chatDoc_docs_find = chatDoc.docs.find((doc)=>doc.id === chatId)) === null || _chatDoc_docs_find === void 0 ? void 0 : _chatDoc_docs_find.data();\n            if (chatData && chatData.password) {\n                return chatData.password === inputPassword;\n            }\n            return true; // Se não tem senha, permite acesso\n        } catch (error) {\n            console.error(\"Erro ao verificar senha:\", error);\n            return false;\n        }\n    };\n    const handleProtectedAction = (chatId, chatName, action)=>{\n        const chat = [\n            ...unorganizedChats,\n            ...folders.flatMap((f)=>f.chats)\n        ].find((c)=>c.id === chatId);\n        if (chat === null || chat === void 0 ? void 0 : chat.password) {\n            // Chat protegido, abrir modal de senha\n            setPasswordModal({\n                isOpen: true,\n                chatId,\n                chatName,\n                action\n            });\n        } else {\n            // Chat não protegido, executar ação diretamente\n            executeAction(chatId, chatName, action);\n        }\n    };\n    const executeAction = (chatId, chatName, action)=>{\n        switch(action){\n            case \"access\":\n                onChatSelect(chatId);\n                break;\n            case \"edit\":\n                const chat = [\n                    ...unorganizedChats,\n                    ...folders.flatMap((f)=>f.chats)\n                ].find((c)=>c.id === chatId);\n                if (chat) {\n                    handleEditChat(chat);\n                }\n                break;\n            case \"delete\":\n                handleDeleteChat(chatId, chatName);\n                break;\n        }\n    };\n    const handlePasswordSuccess = ()=>{\n        executeAction(passwordModal.chatId, passwordModal.chatName, passwordModal.action);\n        setPasswordModal({\n            isOpen: false,\n            chatId: \"\",\n            chatName: \"\",\n            action: \"access\"\n        });\n    };\n    const formatBalance = (balance)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        }).format(balance);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n        fixed top-0 left-0 h-full w-80 bg-gradient-to-b from-blue-950/95 via-blue-900/95 to-blue-950/95\\n        backdrop-blur-xl border-r border-blue-700/30 z-50 transform transition-all duration-300 shadow-2xl\\n        \".concat(isOpen ? \"translate-x-0\" : \"-translate-x-full\", \"\\n        \").concat(isCollapsed ? \"lg:-translate-x-full\" : \"lg:translate-x-0\", \"\\n      \"),\n                children: [\n                    showCloseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onToggle,\n                        className: \"absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-blue-600 hover:bg-blue-500 rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-xl border-2 border-white/20 z-10 group\",\n                        title: \"Fechar sidebar\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-white transition-transform duration-300 group-hover:rotate-180\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M15 19l-7-7 7-7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 674,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 668,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-semibold text-lg\",\n                                                children: userData.username.charAt(0).toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-semibold text-lg\",\n                                                    children: userData.username\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                openRouterBalance.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 border border-blue-300 border-t-transparent rounded-full animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-blue-200 text-sm\",\n                                                            children: \"Carregando...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 19\n                                                }, undefined) : openRouterBalance.error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-400 text-sm\",\n                                                    title: openRouterBalance.error,\n                                                    children: \"$0.00\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-200 text-sm\",\n                                                    children: [\n                                                        \"$\",\n                                                        openRouterBalance.balance.toFixed(4)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onSettingsOpen,\n                                            className: \"text-blue-200 hover:text-white transition-colors p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 710,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-2 border-b border-white/5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleHomeClick,\n                                        className: \"group relative w-full bg-gradient-to-r from-blue-600/90 to-cyan-600/90 hover:from-blue-500 hover:to-cyan-500 text-white rounded-xl transition-all duration-300 overflow-hidden shadow-md shadow-blue-900/15 hover:shadow-lg hover:shadow-blue-800/25 border border-blue-400/20 hover:border-cyan-400/40 backdrop-blur-sm hover:-translate-y-0.5 active:translate-y-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center justify-center py-2.5 px-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 rounded-lg bg-white/15 flex items-center justify-center group-hover:bg-white/25 group-hover:scale-110 transition-all duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    strokeWidth: 2.5,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 738,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-semibold text-white/95 group-hover:text-white transition-colors duration-300\",\n                                                                children: \"\\xc1rea Inicial\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute right-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-1 group-hover:translate-x-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-white/80\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2.5,\n                                                                d: \"M9 5l7 7-7 7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 749,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-1.5 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleNewChat,\n                                                className: \"group relative bg-gradient-to-br from-blue-600/80 to-cyan-600/80 hover:from-blue-500/90 hover:to-cyan-500/90 text-white rounded-lg transition-all duration-300 overflow-hidden shadow-sm shadow-blue-900/10 hover:shadow-md hover:shadow-blue-800/20 border border-blue-400/15 hover:border-cyan-400/30 backdrop-blur-sm hover:-translate-y-0.5 active:translate-y-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex flex-col items-center justify-center py-2.5 px-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-md bg-white/15 flex items-center justify-center group-hover:bg-white/25 group-hover:scale-110 transition-all duration-300 mb-1.5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    strokeWidth: 2.5,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        d: \"M12 4v16m8-8H4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 774,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 773,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-white/90 group-hover:text-white transition-colors duration-300 text-center leading-tight\",\n                                                                children: [\n                                                                    \"Nova\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 778,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    \"Conversa\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 777,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleNewFolder,\n                                                className: \"group relative bg-gradient-to-br from-blue-600/80 to-cyan-600/80 hover:from-blue-500/90 hover:to-cyan-500/90 text-white rounded-lg transition-all duration-300 overflow-hidden shadow-sm shadow-blue-900/10 hover:shadow-md hover:shadow-blue-800/20 border border-blue-400/15 hover:border-cyan-400/30 backdrop-blur-sm hover:-translate-y-0.5 active:translate-y-0\",\n                                                title: \"Criar Nova Pasta\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 793,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex flex-col items-center justify-center py-2.5 px-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-md bg-white/15 flex items-center justify-center group-hover:bg-white/25 group-hover:scale-110 transition-all duration-300 mb-1.5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    strokeWidth: 2.5,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 800,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 799,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-white/90 group-hover:text-white transition-colors duration-300 text-center leading-tight\",\n                                                                children: [\n                                                                    \"Nova\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 805,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    \"Pasta\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 796,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 783,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 719,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-2 border-b border-white/10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-blue-300 text-sm font-bold uppercase tracking-wider flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 818,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Conversas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 817,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-2 py-2\",\n                                        children: folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-2\",\n                                                onDragOver: (e)=>handleDragOver(e, folder.id),\n                                                onDragLeave: handleDragLeave,\n                                                onDrop: (e)=>handleDrop(e, folder.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"group relative rounded-lg transition-all duration-300 cursor-pointer \".concat(hoveredFolder === folder.id ? \"bg-blue-800/40\" : \"hover:bg-blue-800/30\", \" \").concat(dragOverFolder === folder.id ? \"bg-blue-500/30 ring-2 ring-blue-400/50\" : \"\"),\n                                                        onMouseEnter: ()=>setHoveredFolder(folder.id),\n                                                        onMouseLeave: ()=>setHoveredFolder(null),\n                                                        onClick: ()=>toggleFolder(folder.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-shrink-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4 text-blue-300 transition-transform duration-200 \".concat(folder.isExpanded ? \"rotate-90\" : \"\"),\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M9 5l7 7-7 7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 855,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 847,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 846,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-6 h-6 rounded flex items-center justify-center flex-shrink-0\",\n                                                                            style: {\n                                                                                backgroundColor: getFolderHexColor(folder.color) + \"40\"\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4\",\n                                                                                style: {\n                                                                                    color: getFolderHexColor(folder.color)\n                                                                                },\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 870,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 864,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 860,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"text-base font-semibold text-blue-100 truncate\",\n                                                                                            children: folder.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 877,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs text-blue-300/70 bg-blue-900/50 px-2 py-0.5 rounded-full\",\n                                                                                            children: folder.chats.length\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 880,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 876,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                folder.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-blue-300/60 truncate mt-0.5\",\n                                                                                    children: folder.description\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 885,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 875,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 844,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 transition-all duration-300 \".concat(hoveredFolder === folder.id ? \"opacity-100 translate-x-0\" : \"opacity-0 translate-x-2\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200\",\n                                                                            title: \"Editar pasta\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                handleEditFolder(folder.id);\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-3.5 h-3.5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        strokeWidth: 2,\n                                                                                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 905,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        strokeWidth: 2,\n                                                                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 906,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 904,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 896,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200\",\n                                                                            title: \"Deletar pasta\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                handleDeleteFolder(folder.id, folder.name);\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-3.5 h-3.5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 918,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 917,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 909,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 893,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 843,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    folder.isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-6 mt-2 space-y-1\",\n                                                        children: folder.chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatItem, {\n                                                                chat: chat,\n                                                                isActive: currentChat === chat.id,\n                                                                onClick: ()=>handleProtectedAction(chat.id, chat.name, \"access\"),\n                                                                onEdit: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"edit\"),\n                                                                onDelete: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"delete\"),\n                                                                onDragStart: handleDragStart,\n                                                                onDragEnd: handleDragEnd,\n                                                                isDragging: draggedChat === chat.id,\n                                                                isAnimating: animatingChat === chat.id,\n                                                                configDropdownOpen: configDropdownOpen,\n                                                                setConfigDropdownOpen: setConfigDropdownOpen,\n                                                                onDownloadModal: onDownloadModal,\n                                                                onAttachmentsModal: onAttachmentsModal,\n                                                                onStatisticsModal: onStatisticsModal\n                                                            }, chat.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 929,\n                                                                columnNumber: 25\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 927,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, folder.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 827,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 825,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-2 py-2\",\n                                        onDragOver: (e)=>handleDragOver(e, null),\n                                        onDragLeave: handleDragLeave,\n                                        onDrop: (e)=>handleDrop(e, null),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-3 py-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-blue-300 text-xs font-bold uppercase tracking-wider transition-all duration-200 flex items-center space-x-2 \".concat(dragOverFolder === null && draggedChat ? \"text-blue-400\" : \"\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 964,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Sem Pasta \",\n                                                                dragOverFolder === null && draggedChat ? \"(Solte aqui para remover da pasta)\" : \"\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 965,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 961,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 960,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1 min-h-[60px] transition-all duration-200 \".concat(dragOverFolder === null && draggedChat ? \"bg-blue-500/10 rounded-lg p-2 border-2 border-dashed border-blue-400/50\" : \"\"),\n                                                children: unorganizedChats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-white/30\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 975,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 974,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 973,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-white/40 text-xs\",\n                                                            children: \"Nenhuma conversa sem pasta\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 979,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 972,\n                                                    columnNumber: 19\n                                                }, undefined) : unorganizedChats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatItem, {\n                                                        chat: chat,\n                                                        isActive: currentChat === chat.id,\n                                                        onClick: ()=>handleProtectedAction(chat.id, chat.name, \"access\"),\n                                                        onEdit: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"edit\"),\n                                                        onDelete: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"delete\"),\n                                                        onDragStart: handleDragStart,\n                                                        onDragEnd: handleDragEnd,\n                                                        isDragging: draggedChat === chat.id,\n                                                        isAnimating: animatingChat === chat.id,\n                                                        configDropdownOpen: configDropdownOpen,\n                                                        setConfigDropdownOpen: setConfigDropdownOpen,\n                                                        onDownloadModal: onDownloadModal,\n                                                        onAttachmentsModal: onAttachmentsModal,\n                                                        onStatisticsModal: onStatisticsModal\n                                                    }, chat.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 983,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 968,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 954,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    folders.length === 0 && unorganizedChats.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white/30 mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-12 h-12 mx-auto\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 1010,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1009,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 1008,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/50 text-sm\",\n                                                children: \"Nenhuma conversa ainda\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 1014,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/30 text-xs mt-1\",\n                                                children: 'Clique em \"Nova Conversa\" para come\\xe7ar'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 1015,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 1007,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 813,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden p-4 border-t border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggle,\n                                    className: \"w-full bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg  transition-all duration-200 flex items-center justify-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 1028,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1027,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Fechar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1030,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1022,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1021,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 679,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 651,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateChatModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: createChatModalOpen,\n                onClose: ()=>setCreateChatModalOpen(false),\n                username: userData.username,\n                onChatCreated: handleChatCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1037,\n                columnNumber: 7\n            }, undefined),\n            editingChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateChatModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: editChatModalOpen,\n                onClose: ()=>{\n                    setEditChatModalOpen(false);\n                    setEditingChat(null);\n                },\n                username: userData.username,\n                onChatCreated: handleChatUpdated,\n                editingChat: editingChat\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1046,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: createFolderModalOpen,\n                onClose: ()=>setCreateFolderModalOpen(false),\n                username: userData.username,\n                onFolderCreated: handleFolderCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1059,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: editFolderModalOpen,\n                onClose: ()=>{\n                    setEditFolderModalOpen(false);\n                    setEditingFolder(null);\n                },\n                username: userData.username,\n                onFolderCreated: handleFolderUpdated,\n                editingFolder: editingFolder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1067,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: deleteConfirmModal.isOpen,\n                onClose: ()=>setDeleteConfirmModal({\n                        isOpen: false,\n                        type: \"chat\",\n                        id: \"\",\n                        name: \"\"\n                    }),\n                onConfirm: confirmDelete,\n                title: deleteConfirmModal.type === \"folder\" ? \"Deletar Pasta\" : \"Deletar Conversa\",\n                message: deleteConfirmModal.type === \"folder\" ? 'Tem certeza que deseja deletar esta pasta? Todas as conversas dentro dela ser\\xe3o movidas para \"Sem Pasta\".' : \"Tem certeza que deseja deletar esta conversa? Todas as mensagens ser\\xe3o perdidas permanentemente.\",\n                itemName: deleteConfirmModal.name,\n                isLoading: isDeleting\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1079,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PasswordProtectedModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: passwordModal.isOpen,\n                onClose: ()=>setPasswordModal({\n                        isOpen: false,\n                        chatId: \"\",\n                        chatName: \"\",\n                        action: \"access\"\n                    }),\n                onSuccess: handlePasswordSuccess,\n                chatName: passwordModal.chatName,\n                onPasswordSubmit: (password)=>checkChatPassword(passwordModal.chatId, password)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1094,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n}, \"PnQLWBuot+6j6fSnAuBQlETMF5k=\")), \"PnQLWBuot+6j6fSnAuBQlETMF5k=\");\n_c1 = Sidebar;\nSidebar.displayName = \"Sidebar\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (Sidebar);\nfunction ChatItem(param) {\n    let { chat, isActive, onClick, onEdit, onDelete, onDragStart, onDragEnd, isDragging, isAnimating = false, configDropdownOpen, setConfigDropdownOpen, onDownloadModal, onAttachmentsModal, onStatisticsModal } = param;\n    _s1();\n    // Ref específica para este item de chat\n    const itemConfigDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Fechar dropdown deste item quando clicar fora (mas não quando o mouse sai da janela)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            // Verificar se o clique foi realmente fora do dropdown\n            if (itemConfigDropdownRef.current && !itemConfigDropdownRef.current.contains(event.target)) {\n                // Verificar se o target ainda está no documento (não foi um evento de drag para fora da janela)\n                if (document.contains(event.target) && configDropdownOpen === chat.id) {\n                    setConfigDropdownOpen(null);\n                }\n            }\n        };\n        const handleWindowBlur = ()=>{\n        // Não fechar dropdown quando a janela perde o foco\n        // Isso previne o fechamento quando você move o mouse para fora da aba\n        };\n        if (configDropdownOpen === chat.id) {\n            // Usar um pequeno delay para evitar fechamento imediato\n            const timeoutId = setTimeout(()=>{\n                document.addEventListener(\"mousedown\", handleClickOutside);\n                window.addEventListener(\"blur\", handleWindowBlur);\n            }, 100);\n            return ()=>{\n                clearTimeout(timeoutId);\n                document.removeEventListener(\"mousedown\", handleClickOutside);\n                window.removeEventListener(\"blur\", handleWindowBlur);\n            };\n        }\n    }, [\n        configDropdownOpen,\n        chat.id,\n        setConfigDropdownOpen\n    ]);\n    const formatTime = (timeString)=>{\n        const date = new Date(timeString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return date.toLocaleTimeString(\"pt-BR\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffInHours < 168) {\n            return \"h\\xe1 \".concat(Math.floor(diffInHours / 24), \" dias\");\n        } else {\n            return date.toLocaleDateString(\"pt-BR\", {\n                day: \"2-digit\",\n                month: \"2-digit\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: true,\n        onDragStart: ()=>onDragStart(chat.id),\n        onDragEnd: onDragEnd,\n        className: \"group relative rounded-xl transition-all duration-300 cursor-move \".concat(isActive ? \"bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border border-blue-500/30 shadow-lg\" : \"hover:bg-blue-800/30 border border-transparent\", \" \").concat(isDragging ? \"opacity-50 scale-95\" : \"\", \" \").concat(isAnimating ? \"animate-pulse bg-blue-500/20 scale-105\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onClick,\n                className: \"w-full text-left p-3 flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative \".concat(isActive ? \"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30\" : \"bg-blue-700/50 group-hover:bg-blue-600/70\"),\n                        children: [\n                            chat.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-2 h-2 text-white\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 3,\n                                        d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 1220,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1219,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1218,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 text-white\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                strokeWidth: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1226,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1225,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1211,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-hidden flex-1 min-w-0 transition-all duration-300 group-hover:pr-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"truncate text-sm font-semibold \".concat(isActive ? \"text-white\" : \"text-blue-100 group-hover:text-white\"),\n                                    children: chat.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1232,\n                                    columnNumber: 15\n                                }, this),\n                                chat.lastMessageTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs flex-shrink-0 ml-2 \".concat(isActive ? \"text-blue-300/60\" : \"text-blue-400/50 group-hover:text-blue-300/70\"),\n                                    children: formatTime(chat.lastMessageTime)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1240,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1231,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1230,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 transition-all duration-300 \".concat(configDropdownOpen === chat.id ? \"opacity-100\" : \"opacity-0 group-hover:opacity-100\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        ref: itemConfigDropdownRef,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-1.5 rounded-lg transition-all duration-200 backdrop-blur-sm \".concat(configDropdownOpen === chat.id ? \"bg-cyan-600/90 text-cyan-200\" : \"bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white\"),\n                                title: \"Configura\\xe7\\xf5es\",\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    setConfigDropdownOpen(configDropdownOpen === chat.id ? null : chat.id);\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-3.5 h-3.5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1271,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1272,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1270,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1258,\n                                columnNumber: 11\n                            }, this),\n                            configDropdownOpen === chat.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-full right-0 mb-2 bg-blue-900/95 backdrop-blur-xl rounded-xl border border-blue-600/30 shadow-2xl shadow-blue-900/60 p-2 min-w-[180px] z-50 animate-in slide-in-from-bottom-2 duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                onEdit(chat.id, chat.name);\n                                                setConfigDropdownOpen(null);\n                                            },\n                                            className: \"w-full flex items-center space-x-3 p-2.5 rounded-lg bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg border border-blue-600/20 hover:scale-[1.02]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 1290,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 1291,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1289,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Configura\\xe7\\xe3o\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1293,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1281,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                onStatisticsModal === null || onStatisticsModal === void 0 ? void 0 : onStatisticsModal(chat.id);\n                                                setConfigDropdownOpen(null);\n                                            },\n                                            className: \"w-full flex items-center space-x-3 p-2.5 rounded-lg bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg border border-blue-600/20 hover:scale-[1.02]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 1306,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1305,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Estat\\xedsticas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1308,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1297,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                onDownloadModal === null || onDownloadModal === void 0 ? void 0 : onDownloadModal(chat.id);\n                                                setConfigDropdownOpen(null);\n                                            },\n                                            className: \"w-full flex items-center space-x-3 p-2.5 rounded-lg bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg border border-blue-600/20 hover:scale-[1.02]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 1321,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1320,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Download\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1323,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1312,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1279,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1278,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1257,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm\",\n                        title: \"Deletar\",\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            onDelete(chat.id, chat.name);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3.5 h-3.5\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1339,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1338,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1330,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1253,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n        lineNumber: 1195,\n        columnNumber: 5\n    }, this);\n}\n_s1(ChatItem, \"6WBL0LKLhC/v4f94gWhcXYAtQGE=\");\n_c2 = ChatItem;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Sidebar$forwardRef\");\n$RefreshReg$(_c1, \"Sidebar\");\n$RefreshReg$(_c2, \"ChatItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar.tsx\n"));

/***/ })

});