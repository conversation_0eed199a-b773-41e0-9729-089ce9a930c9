'use client';

import { useState, useRef, useEffect } from 'react';
import { Send, Paperclip, X, Wrench, Globe, Monitor, ChevronUp, ChevronDown, FileText } from 'lucide-react';
import attachmentService from '@/lib/services/attachmentService';
import { AttachmentMetadata } from '@/lib/types/chat';

interface Attachment {
  id: string;
  filename: string;
  type: 'image' | 'document';
  file: File;
}

interface InputBarProps {
  message: string;
  setMessage: (message: string) => void;
  onSendMessage: (attachments?: import('@/lib/types/chat').AttachmentMetadata[], webSearchEnabled?: boolean) => void;
  isLoading: boolean;
  selectedModel: string;
  onModelChange: (model: string) => void;
  onScrollToTop?: () => void;
  onScrollToBottom?: () => void;
  isStreaming?: boolean;
  onCancelStreaming?: () => void;
  onOpenModelModal?: () => void;
  onOpenAttachmentsModal?: () => void;
  username?: string;
  chatId?: string;
  activeAttachmentsCount?: number; // Número de anexos ativos no chat
}

const AI_MODELS = [
  { id: 'gpt-4.1-nano', name: 'GPT-4.1 Nano', description: 'Rápido e eficiente' },
  { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: 'Mais poderoso' },
  { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', description: 'Criativo e preciso' },
  { id: 'gemini-pro', name: 'Gemini Pro', description: 'Multimodal' },
];

export default function InputBar({
  message,
  setMessage,
  onSendMessage,
  isLoading,
  selectedModel,
  onModelChange,
  onScrollToTop,
  onScrollToBottom,
  isStreaming = false,
  onCancelStreaming,
  onOpenModelModal,
  onOpenAttachmentsModal,
  username,
  chatId,
  activeAttachmentsCount = 0
}: InputBarProps) {

  const [webSearchEnabled, setWebSearchEnabled] = useState(false);
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [processedAttachments, setProcessedAttachments] = useState<AttachmentMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isToolsExpanded, setIsToolsExpanded] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toolsRef = useRef<HTMLDivElement>(null);
  const adjustHeightTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (adjustHeightTimeoutRef.current) {
        clearTimeout(adjustHeightTimeoutRef.current);
      }
    };
  }, []);

  // Adjust textarea height when message changes
  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  // Close tools dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (toolsRef.current && !toolsRef.current.contains(event.target as Node)) {
        setIsToolsExpanded(false);
      }
    };

    if (isToolsExpanded) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isToolsExpanded]);

  // Escutar eventos de drag-n-drop do ChatArea
  useEffect(() => {
    const handleDragDropAttachments = (event: CustomEvent) => {
      const { attachments } = event.detail;

      // Converter AttachmentMetadata para o formato local Attachment
      const localAttachments: Attachment[] = attachments.map((att: AttachmentMetadata) => ({
        id: att.id,
        filename: att.filename,
        type: att.type === 'image' ? 'image' : 'document',
        file: new File([], att.filename, {
          type: att.type === 'image' ? 'image/jpeg' : 'application/pdf'
        }) // Arquivo placeholder
      }));

      // Adicionar aos anexos locais
      setAttachments(prev => [...prev, ...localAttachments]);

      // Adicionar aos anexos processados
      setProcessedAttachments(prev => [...prev, ...attachments]);

      console.log(`📎 ${attachments.length} anexo(s) adicionado(s) via drag-n-drop`);
    };

    // Adicionar listener para o evento customizado
    window.addEventListener('dragDropAttachments', handleDragDropAttachments as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('dragDropAttachments', handleDragDropAttachments as EventListener);
    };
  }, []);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setMessage(value);

    // Ajustar altura imediatamente para evitar flickering
    requestAnimationFrame(() => {
      adjustTextareaHeight();
    });
  };

  const handleSend = () => {
    const hasMessage = message?.trim();
    const hasAttachments = attachments.length > 0;
    const canSend = (hasMessage || hasAttachments) && !isLoading && !isUploading;

    if (canSend) {
      onSendMessage(processedAttachments, webSearchEnabled);
      // Limpar anexos após envio
      setAttachments([]);
      setProcessedAttachments([]);
      // Fechar ferramentas após envio
      setIsToolsExpanded(false);
    }
  };

  const handleAttachment = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || !username || !chatId) return;

    setIsUploading(true);
    try {
      // Primeiro, adicionar arquivos localmente para preview
      const localAttachments: Attachment[] = [];
      for (const file of Array.from(files)) {
        const attachment: Attachment = {
          id: Date.now().toString() + Math.random().toString(36).substring(2, 9),
          filename: file.name,
          type: file.type.startsWith('image/') ? 'image' : 'document',
          file
        };
        localAttachments.push(attachment);
      }
      setAttachments(prev => [...prev, ...localAttachments]);

      // Fazer upload dos arquivos
      const uploadedAttachments = await attachmentService.uploadMultipleAttachments(
        Array.from(files),
        username,
        chatId
      );

      // Atualizar com metadados dos arquivos processados
      setProcessedAttachments(prev => [
        ...prev,
        ...uploadedAttachments.map(att => att.metadata)
      ]);

    } catch (error) {
      console.error('Erro ao processar arquivos:', error);
      // Remover anexos que falharam
      setAttachments(prev => prev.filter(att =>
        !Array.from(files).some(file => file.name === att.filename)
      ));
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const removeAttachment = (id: string) => {
    // Encontrar o anexo para obter o filename
    const attachment = attachments.find(att => att.id === id);
    if (attachment) {
      // Remover do estado local
      setAttachments(prev => prev.filter(att => att.id !== id));
      // Remover dos anexos processados também
      setProcessedAttachments(prev => prev.filter(att => att.filename !== attachment.filename));
    }
  };

  const handleWebSearch = () => {
    setWebSearchEnabled(!webSearchEnabled);
  };

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // Store current scroll position to prevent jumping
    const scrollTop = textarea.scrollTop;

    // Temporarily set height to auto to get accurate scrollHeight
    textarea.style.height = 'auto';

    // Calculate new height with proper constraints
    const scrollHeight = textarea.scrollHeight;
    const minHeight = 44; // Minimum height in pixels
    const maxHeight = 120; // Maximum height in pixels

    // Set the new height within bounds
    const newHeight = Math.max(minHeight, Math.min(scrollHeight, maxHeight));
    textarea.style.height = newHeight + 'px';

    // Handle overflow and scrolling
    if (scrollHeight > maxHeight) {
      textarea.style.overflowY = 'auto';
      // Restore scroll position if content was scrolled
      if (scrollTop > 0) {
        textarea.scrollTop = scrollTop;
      }
    } else {
      textarea.style.overflowY = 'hidden';
    }
  };

  const isWebSearchEnabled = () => {
    // Web search está disponível para todos os modelos via OpenRouter plugins
    // Apenas ocultar para modelos locais ou específicos que não suportam
    const unsupportedModels = ['local/', 'offline/'];
    return !unsupportedModels.some(prefix => selectedModel.startsWith(prefix));
  };

  const currentModel = AI_MODELS.find(model => model.id === selectedModel);
  const modelName = currentModel ? currentModel.name : selectedModel;

  return (
    <div className="relative">
      <div className="p-3 sm:p-4 lg:p-6 bg-gradient-to-r from-blue-950/98 via-blue-900/98 to-blue-950/98 backdrop-blur-xl relative w-full shadow-2xl shadow-blue-900/60">
        {/* Efeito de brilho sutil */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/8 via-transparent to-cyan-500/8 pointer-events-none"></div>

        {/* Floating glow effect - inner glow */}
        <div className="absolute inset-0 bg-gradient-to-t from-blue-600/15 via-blue-500/5 to-transparent pointer-events-none"></div>
      
      <div className="max-w-4xl mx-auto relative z-10 w-full">
        {/* Attachment Preview */}
        {attachments.length > 0 && (
          <div className="mb-3 sm:mb-4 p-3 sm:p-4 bg-blue-900/30 backdrop-blur-sm rounded-lg sm:rounded-xl border border-blue-600/30">
            <div className="flex flex-wrap gap-2 sm:gap-3">
              {attachments.map((attachment) => (
                <div
                  key={attachment.id}
                  className="flex items-center space-x-2 sm:space-x-3 bg-blue-800/40 backdrop-blur-sm rounded-lg px-2 sm:px-3 py-1.5 sm:py-2 border border-blue-600/20"
                >
                  {attachment.type === 'image' ? (
                    <svg className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  ) : (
                    <svg className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-red-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  )}
                  <span className="text-xs sm:text-sm text-blue-100 truncate max-w-20 sm:max-w-32 font-medium">
                    {attachment.filename}
                  </span>
                  <button
                    onClick={() => removeAttachment(attachment.id)}
                    className="text-blue-300 hover:text-red-400 transition-colors p-0.5 sm:p-1 rounded-full hover:bg-red-500/20 flex-shrink-0"
                  >
                    <svg className="w-3.5 h-3.5 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Selected Model and Attachments Indicators - Normal Position */}
        {(selectedModel || activeAttachmentsCount > 0) && !isToolsExpanded && (
          <div className="mb-1 flex items-center justify-start space-x-2">
            {selectedModel && (
              <div className="bg-blue-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-blue-600/30 shadow-lg transition-all duration-200">
                <span className="text-xs text-blue-300">Modelo: </span>
                <span className="text-xs text-cyan-300 font-medium">{modelName}</span>
              </div>
            )}

            {activeAttachmentsCount > 0 && (
              <div className="bg-green-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-green-600/30 shadow-lg">
                <span className="text-xs text-green-300">Anexos: </span>
                <span className="text-xs text-green-200 font-medium">{activeAttachmentsCount}</span>
              </div>
            )}
          </div>
        )}

        <div className="flex items-end space-x-2 sm:space-x-3 relative">
          {/* Model Indicator - Top Right Position when tools expanded */}
          {selectedModel && isToolsExpanded && (
            <div className="absolute -top-10 right-20 z-[60] bg-blue-900/90 backdrop-blur-sm rounded-lg px-3 py-1 border border-blue-600/30 shadow-lg animate-in slide-in-from-bottom-2 duration-200 min-w-fit whitespace-nowrap">
              <span className="text-xs text-blue-300">Modelo: </span>
              <span className="text-xs text-cyan-300 font-medium">{modelName}</span>
            </div>
          )}

          {/* Floating input container */}
          <div className="flex-1 relative bg-gradient-to-r from-blue-900/80 to-blue-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl border border-blue-600/30 shadow-xl shadow-blue-900/50">
            {/* Neumorphic glow effect */}
            <div className="absolute inset-0 rounded-xl sm:rounded-2xl shadow-[inset_0_1px_0_rgba(59,130,246,0.1),0_0_20px_rgba(59,130,246,0.15)]"></div>

            <div className="relative z-10 p-3 sm:p-4">
              {/* Main input area */}
              <div className="flex items-center space-x-3">
                {/* Tools Button */}
                <div className="relative" ref={toolsRef}>
                  <button
                    onClick={() => setIsToolsExpanded(!isToolsExpanded)}
                    className={`p-2 rounded-lg transition-all duration-200 hover:scale-105 ${
                      isToolsExpanded
                        ? 'bg-cyan-600/30 text-cyan-200 border-cyan-500/50 shadow-lg shadow-cyan-500/20'
                        : 'bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 border-blue-600/20 hover:shadow-blue-500/20'
                    } backdrop-blur-sm border`}
                    title="Ferramentas"
                  >
                    <Wrench className="w-4 h-4" />
                  </button>

                  {/* Tools Dropdown */}
                  {isToolsExpanded && (
                    <div className="absolute bottom-full left-0 mb-2 bg-blue-900/95 backdrop-blur-xl rounded-xl border border-blue-600/30 shadow-2xl shadow-blue-900/60 p-2 min-w-[200px] z-50 animate-in slide-in-from-bottom-2 duration-200">
                      {/* Glow effect */}
                      <div className="absolute inset-0 rounded-xl bg-gradient-to-t from-blue-600/10 via-blue-500/5 to-transparent pointer-events-none"></div>

                      <div className="space-y-1 relative z-10">
                        {/* Model Selection */}
                        <button
                          onClick={() => {
                            onOpenModelModal?.();
                            setIsToolsExpanded(false);
                          }}
                          className="w-full flex items-center space-x-3 p-2.5 rounded-lg bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg border border-blue-600/20 hover:scale-[1.02]"
                        >
                          <Monitor className="w-4 h-4 flex-shrink-0" />
                          <span className="text-sm font-medium">Selecionar Modelo</span>
                        </button>

                        {/* Attachment */}
                        <button
                          onClick={() => {
                            handleAttachment();
                            setIsToolsExpanded(false);
                          }}
                          disabled={isUploading}
                          className="w-full flex items-center space-x-3 p-2.5 rounded-lg bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg border border-blue-600/20 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-[1.02] disabled:hover:scale-100"
                        >
                          {isUploading ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-400 flex-shrink-0"></div>
                          ) : (
                            <Paperclip className="w-4 h-4 flex-shrink-0" />
                          )}
                          <span className="text-sm font-medium">Anexar Arquivo</span>
                        </button>

                        {/* Attachments Modal */}
                        <button
                          onClick={() => {
                            onOpenAttachmentsModal?.();
                            setIsToolsExpanded(false);
                          }}
                          className="w-full flex items-center space-x-3 p-2.5 rounded-lg bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg border border-blue-600/20 hover:scale-[1.02]"
                        >
                          <FileText className="w-4 h-4 flex-shrink-0" />
                          <span className="text-sm font-medium">Anexos</span>
                        </button>

                        {/* Web Search */}
                        {isWebSearchEnabled() && (
                          <button
                            onClick={() => {
                              handleWebSearch();
                              setIsToolsExpanded(false);
                            }}
                            className={`w-full flex items-center space-x-3 p-2.5 rounded-lg transition-all duration-200 hover:shadow-lg border hover:scale-[1.02] ${
                              webSearchEnabled
                                ? 'bg-cyan-600/30 text-cyan-200 border-cyan-500/50 shadow-lg shadow-cyan-500/20'
                                : 'bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 border-blue-600/20'
                            }`}
                          >
                            <Globe className="w-4 h-4 flex-shrink-0" />
                            <span className="text-sm font-medium">
                              Web Search {webSearchEnabled ? '(Ativo)' : ''}
                            </span>
                          </button>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {/* Text Input - Expanded */}
                <div className="flex-1 relative">
                  <textarea
                    ref={textareaRef}
                    value={message}
                    onChange={handleInputChange}
                    onKeyDown={handleKeyPress}
                    className="w-full bg-transparent border-none rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none resize-none text-sm selection:bg-blue-500/30"
                    rows={1}
                    placeholder="Ask anything..."
                    disabled={isLoading || isStreaming}
                    style={{
                      height: '44px',
                      minHeight: '44px',
                      maxHeight: '120px',
                      lineHeight: '1.5',
                      wordWrap: 'break-word',
                      whiteSpace: 'pre-wrap',
                      overflowY: 'hidden',
                      overflowX: 'hidden',
                      scrollbarWidth: 'thin'
                    }}
                  />
                </div>

                {/* Send Button - Auto send on Enter */}
                {(message?.trim() || attachments.length > 0) && !isStreaming && (
                  <button
                    onClick={handleSend}
                    disabled={isLoading || isUploading}
                    className="p-2 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105 disabled:hover:scale-100"
                    title="Enviar (Enter)"
                  >
                    {isLoading ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                    ) : (
                      <Send className="w-4 h-4" />
                    )}
                  </button>
                )}

                {/* Cancel Button when streaming */}
                {isStreaming && (
                  <button
                    onClick={onCancelStreaming}
                    className="p-2 rounded-lg bg-gradient-to-r from-red-600 to-red-500 hover:from-red-500 hover:to-red-400 text-white transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105"
                    title="Parar geração"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Scroll Buttons - Side by side next to input */}
          <div className="flex flex-col space-y-2">
            {/* Scroll to Top Button */}
            {onScrollToTop && (
              <button
                onClick={onScrollToTop}
                className="group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95"
                title="Ir para o topo"
              >
                {/* Glow effect */}
                <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
                <ChevronUp className="relative w-5 h-5 transform group-hover:-translate-y-0.5 transition-transform duration-200" />
              </button>
            )}

            {/* Scroll to Bottom Button */}
            {onScrollToBottom && (
              <button
                onClick={onScrollToBottom}
                className="group relative p-2.5 rounded-xl bg-gradient-to-br from-blue-800/40 to-blue-900/40 hover:from-blue-700/50 hover:to-blue-800/50 backdrop-blur-sm text-blue-300 hover:text-white transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/30 border border-blue-600/20 hover:border-blue-500/40 hover:scale-105 active:scale-95"
                title="Ir para o final"
              >
                {/* Glow effect */}
                <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
                <ChevronDown className="relative w-5 h-5 transform group-hover:translate-y-0.5 transition-transform duration-200" />
              </button>
            )}
          </div>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/png,image/jpeg,image/webp,application/pdf"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>
    </div>
    </div>
  );
}
