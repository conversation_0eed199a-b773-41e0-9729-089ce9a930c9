"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/MarkdownRenderer.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var remark_math__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-math */ \"(app-pages-browser)/./node_modules/remark-math/lib/index.js\");\n/* harmony import */ var rehype_katex__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rehype-katex */ \"(app-pages-browser)/./node_modules/rehype-katex/lib/index.js\");\n/* harmony import */ var rehype_highlight__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rehype-highlight */ \"(app-pages-browser)/./node_modules/rehype-highlight/lib/index.js\");\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(app-pages-browser)/./node_modules/katex/dist/katex.min.css\");\n/* harmony import */ var _styles_markdown_minimal_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/markdown-minimal.css */ \"(app-pages-browser)/./src/styles/markdown-minimal.css\");\n/* harmony import */ var _styles_markdown_latex_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/markdown-latex.css */ \"(app-pages-browser)/./src/styles/markdown-latex.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Importar estilos do KaTeX e Markdown\n\n\n\n// Componente otimizado para renderização de markdown durante streaming\nconst OptimizedMarkdown = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo((param)=>{\n    let { content, isStreaming } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_5__.Markdown, {\n        remarkPlugins: [\n            remark_gfm__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            remark_math__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        ],\n        rehypePlugins: [\n            [\n                rehype_katex__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                {\n                    strict: false,\n                    throwOnError: false,\n                    errorColor: \"#cc0000\",\n                    macros: {\n                        \"\\\\RR\": \"\\\\mathbb{R}\",\n                        \"\\\\NN\": \"\\\\mathbb{N}\",\n                        \"\\\\ZZ\": \"\\\\mathbb{Z}\",\n                        \"\\\\QQ\": \"\\\\mathbb{Q}\",\n                        \"\\\\CC\": \"\\\\mathbb{C}\"\n                    }\n                }\n            ],\n            [\n                rehype_highlight__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                {\n                    detect: true,\n                    ignoreMissing: true\n                }\n            ]\n        ],\n        components: {\n            // Customizar renderização de código\n            code (param) {\n                let { node, inline, className, children, ...props } = param;\n                const match = /language-(\\w+)/.exec(className || \"\");\n                const language = match ? match[1] : \"text\";\n                return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"code-block-container group\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"code-header\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-1.5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full bg-red-400/80\"\n                                                }, void 0, false, void 0, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full bg-yellow-400/80\"\n                                                }, void 0, false, void 0, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full bg-green-400/80\"\n                                                }, void 0, false, void 0, void 0)\n                                            ]\n                                        }, void 0, true, void 0, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-slate-300 capitalize\",\n                                            children: language\n                                        }, void 0, false, void 0, void 0)\n                                    ]\n                                }, void 0, true, void 0, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"copy-button opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                    onClick: ()=>navigator.clipboard.writeText(String(children)),\n                                    title: \"Copiar c\\xf3digo\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                                        }, void 0, false, void 0, void 0)\n                                    }, void 0, false, void 0, void 0)\n                                }, void 0, false, void 0, void 0)\n                            ]\n                        }, void 0, true, void 0, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"code-content\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: className,\n                                ...props,\n                                children: children\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0)\n                    ]\n                }, void 0, true, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                    className: \"inline-code\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de links\n            a (param) {\n                let { children, href, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: href,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"markdown-link\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de tabelas\n            table (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"table-wrapper\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"markdown-table\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            th (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                    className: \"table-header\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            td (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                    className: \"table-cell\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de blockquotes\n            blockquote (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                    className: \"markdown-blockquote\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"quote-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de listas\n            ul (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"markdown-list unordered\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            ol (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                    className: \"markdown-list ordered\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            li (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"list-item\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de títulos\n            h1 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"markdown-heading h1\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            h2 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"markdown-heading h2\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            h3 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"markdown-heading h3\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            h4 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                    className: \"markdown-heading h4\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            h5 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                    className: \"markdown-heading h5\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            h6 (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                    className: \"markdown-heading h6\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"heading-content\",\n                        children: children\n                    }, void 0, false, void 0, void 0)\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de parágrafos\n            p (param) {\n                let { children, ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"markdown-paragraph\",\n                    ...props,\n                    children: children\n                }, void 0, false, void 0, void 0);\n            },\n            // Customizar renderização de separadores\n            hr (param) {\n                let { ...props } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                    className: \"markdown-divider\",\n                    ...props\n                }, void 0, false, void 0, void 0);\n            }\n        },\n        children: content\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n}, (prevProps, nextProps)=>{\n    // Durante streaming, só re-renderizar se o conteúdo mudou significativamente\n    if (nextProps.isStreaming) {\n        // Durante streaming, re-renderizar apenas a cada 100 caracteres para melhor performance\n        const prevLength = prevProps.content.length;\n        const nextLength = nextProps.content.length;\n        const shouldUpdate = nextLength - prevLength >= 100 || nextLength < prevLength;\n        return !shouldUpdate;\n    }\n    // Fora do streaming, comportamento normal\n    return prevProps.content === nextProps.content;\n});\n_c = OptimizedMarkdown;\nconst MarkdownRenderer = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().memo(_c1 = _s((param)=>{\n    let { content, className = \"\", hasWebSearch = false, webSearchAnnotations = [], isStreaming = false } = param;\n    _s();\n    // Função para detectar se o conteúdo contém citações de web search\n    const detectWebSearch = (text)=>{\n        // Detecta links no formato [dominio.com] que são característicos do web search\n        const webSearchPattern = /\\[[\\w.-]+\\.[\\w]+\\]/g;\n        const matches = text.match(webSearchPattern);\n        return matches !== null && matches.length > 0;\n    };\n    // Função para processar o conteúdo (OpenRouter já retorna links formatados corretamente)\n    const processWebSearchLinks = (text)=>{\n        // Como o OpenRouter já retorna os links no formato markdown correto,\n        // não precisamos processar nada. Apenas retornamos o texto original.\n        return text;\n    };\n    // Função para contar e extrair informações sobre as fontes\n    const getWebSearchInfo = (text, annotations)=>{\n        if (annotations.length > 0) {\n            // Usar annotations se disponíveis\n            const uniqueDomains = new Set(annotations.map((annotation)=>{\n                try {\n                    return new URL(annotation.url).hostname.replace(\"www.\", \"\");\n                } catch (e) {\n                    return annotation.url;\n                }\n            }));\n            return {\n                sourceCount: annotations.length,\n                sources: Array.from(uniqueDomains)\n            };\n        }\n        // Fallback: detectar pelos padrões no texto (formato markdown link)\n        const webSearchPattern = /\\[[\\w.-]+\\.[\\w]+\\]\\([^)]+\\)/g;\n        const matches = text.match(webSearchPattern) || [];\n        const sourceSet = new Set(matches.map((match)=>{\n            // Extrair o domínio do formato [dominio.com](url)\n            const domainMatch = match.match(/\\[([\\w.-]+\\.[\\w]+)\\]/);\n            return domainMatch ? domainMatch[1] : match;\n        }));\n        const uniqueSources = Array.from(sourceSet);\n        return {\n            sourceCount: matches.length,\n            sources: uniqueSources\n        };\n    };\n    // Usar useMemo para otimizar processamento durante streaming\n    const { isWebSearchMessage, webSearchInfo, processedContent } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const isWebSearch = hasWebSearch || detectWebSearch(content);\n        const searchInfo = isWebSearch ? getWebSearchInfo(content, webSearchAnnotations) : {\n            sourceCount: 0,\n            sources: []\n        };\n        const processed = isWebSearch ? processWebSearchLinks(content) : content;\n        return {\n            isWebSearchMessage: isWebSearch,\n            webSearchInfo: searchInfo,\n            processedContent: processed\n        };\n    }, [\n        content,\n        hasWebSearch,\n        webSearchAnnotations\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rafthor-markdown \".concat(className, \" \").concat(isStreaming ? \"streaming\" : \"\"),\n        children: [\n            isWebSearchMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"web-search-indicator\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"search-badge\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"search-icon\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M9 12l2 2 4-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Busca na Web\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"source-count\",\n                                children: webSearchInfo.sourceCount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 11\n                    }, undefined),\n                    webSearchInfo.sources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"source-list\",\n                        children: [\n                            webSearchInfo.sources.slice(0, 3).map((source, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"source-tag\",\n                                    children: source\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 17\n                                }, undefined)),\n                            webSearchInfo.sources.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"source-tag more\",\n                                children: [\n                                    \"+\",\n                                    webSearchInfo.sources.length - 3\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                lineNumber: 327,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OptimizedMarkdown, {\n                content: processedContent,\n                isStreaming: isStreaming\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n        lineNumber: 324,\n        columnNumber: 5\n    }, undefined);\n}, \"dFNNXoCuWiDJiwthbe1hRtm0Vi0=\")), \"dFNNXoCuWiDJiwthbe1hRtm0Vi0=\");\n_c2 = MarkdownRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MarkdownRenderer);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"OptimizedMarkdown\");\n$RefreshReg$(_c1, \"MarkdownRenderer$React.memo\");\n$RefreshReg$(_c2, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9NYXJrZG93blJlbmRlcmVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUV1QztBQUNJO0FBQ1I7QUFDRTtBQUNFO0FBQ1E7QUFFL0MsdUNBQXVDO0FBQ0w7QUFDSztBQUNGO0FBbUJyQyx1RUFBdUU7QUFDdkUsTUFBTU8sa0NBQW9CUCxpREFBVSxDQUFDO1FBQUMsRUFBRVMsT0FBTyxFQUFFQyxXQUFXLEVBQTZDO0lBQ3ZHLHFCQUNFLDhEQUFDUixvREFBYUE7UUFDWlMsZUFBZTtZQUFDUixrREFBU0E7WUFBRUMsbURBQVVBO1NBQUM7UUFDdENRLGVBQWU7WUFDYjtnQkFBQ1Asb0RBQVdBO2dCQUFFO29CQUNaUSxRQUFRO29CQUNSQyxjQUFjO29CQUNkQyxZQUFZO29CQUNaQyxRQUFRO3dCQUNOLFFBQVE7d0JBQ1IsUUFBUTt3QkFDUixRQUFRO3dCQUNSLFFBQVE7d0JBQ1IsUUFBUTtvQkFDVjtnQkFDRjthQUFFO1lBQ0Y7Z0JBQUNWLHdEQUFlQTtnQkFBRTtvQkFBRVcsUUFBUTtvQkFBTUMsZUFBZTtnQkFBSzthQUFFO1NBQ3pEO1FBQ0RDLFlBQVk7WUFDVixvQ0FBb0M7WUFDcENDLE1BQUssS0FBb0Q7b0JBQXBELEVBQUVDLElBQUksRUFBRUMsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFFBQVEsRUFBRSxHQUFHQyxPQUFZLEdBQXBEO2dCQUNILE1BQU1DLFFBQVEsaUJBQWlCQyxJQUFJLENBQUNKLGFBQWE7Z0JBQ2pELE1BQU1LLFdBQVdGLFFBQVFBLEtBQUssQ0FBQyxFQUFFLEdBQUc7Z0JBRXBDLE9BQU8sQ0FBQ0osVUFBVUksc0JBQ2hCLDhEQUFDRztvQkFBSU4sV0FBVTs7c0NBQ2IsOERBQUNNOzRCQUFJTixXQUFVOzs4Q0FDYiw4REFBQ007b0NBQUlOLFdBQVU7O3NEQUNiLDhEQUFDTTs0Q0FBSU4sV0FBVTs7OERBQ2IsOERBQUNNO29EQUFJTixXQUFVOzs4REFDZiw4REFBQ007b0RBQUlOLFdBQVU7OzhEQUNmLDhEQUFDTTtvREFBSU4sV0FBVTs7OztzREFFakIsOERBQUNPOzRDQUFLUCxXQUFVO3NEQUNiSzs7Ozs4Q0FHTCw4REFBQ0c7b0NBQ0NSLFdBQVU7b0NBQ1ZTLFNBQVMsSUFBTUMsVUFBVUMsU0FBUyxDQUFDQyxTQUFTLENBQUNDLE9BQU9aO29DQUNwRGEsT0FBTTs4Q0FFTiw0RUFBQ0M7d0NBQUlmLFdBQVU7d0NBQVVnQixNQUFLO3dDQUFPQyxRQUFPO3dDQUFlQyxTQUFRO2tEQUNqRSw0RUFBQ0M7NENBQUtDLGVBQWM7NENBQVFDLGdCQUFlOzRDQUFRQyxhQUFhOzRDQUFHQyxHQUFFOzs7Ozs7c0NBSTNFLDhEQUFDQzs0QkFBSXhCLFdBQVU7c0NBQ2IsNEVBQUNIO2dDQUFLRyxXQUFXQTtnQ0FBWSxHQUFHRSxLQUFLOzBDQUNsQ0Q7Ozs7aUVBS1AsOERBQUNKO29CQUFLRyxXQUFVO29CQUFlLEdBQUdFLEtBQUs7OEJBQ3BDRDs7WUFHUDtZQUVBLG1DQUFtQztZQUNuQ3dCLEdBQUUsS0FBNEI7b0JBQTVCLEVBQUV4QixRQUFRLEVBQUV5QixJQUFJLEVBQUUsR0FBR3hCLE9BQU8sR0FBNUI7Z0JBQ0EscUJBQ0UsOERBQUN1QjtvQkFDQ0MsTUFBTUE7b0JBQ05DLFFBQU87b0JBQ1BDLEtBQUk7b0JBQ0o1QixXQUFVO29CQUNULEdBQUdFLEtBQUs7OEJBRVJEOztZQUdQO1lBRUEscUNBQXFDO1lBQ3JDNEIsT0FBTSxLQUFzQjtvQkFBdEIsRUFBRTVCLFFBQVEsRUFBRSxHQUFHQyxPQUFPLEdBQXRCO2dCQUNKLHFCQUNFLDhEQUFDSTtvQkFBSU4sV0FBVTs4QkFDYiw0RUFBQzZCO3dCQUFNN0IsV0FBVTt3QkFBa0IsR0FBR0UsS0FBSztrQ0FDeENEOzs7WUFJVDtZQUVBNkIsSUFBRyxLQUFzQjtvQkFBdEIsRUFBRTdCLFFBQVEsRUFBRSxHQUFHQyxPQUFPLEdBQXRCO2dCQUNELHFCQUNFLDhEQUFDNEI7b0JBQUc5QixXQUFVO29CQUFnQixHQUFHRSxLQUFLOzhCQUNuQ0Q7O1lBR1A7WUFFQThCLElBQUcsS0FBc0I7b0JBQXRCLEVBQUU5QixRQUFRLEVBQUUsR0FBR0MsT0FBTyxHQUF0QjtnQkFDRCxxQkFDRSw4REFBQzZCO29CQUFHL0IsV0FBVTtvQkFBYyxHQUFHRSxLQUFLOzhCQUNqQ0Q7O1lBR1A7WUFFQSx5Q0FBeUM7WUFDekMrQixZQUFXLEtBQXNCO29CQUF0QixFQUFFL0IsUUFBUSxFQUFFLEdBQUdDLE9BQU8sR0FBdEI7Z0JBQ1QscUJBQ0UsOERBQUM4QjtvQkFBV2hDLFdBQVU7b0JBQXVCLEdBQUdFLEtBQUs7OEJBQ25ELDRFQUFDSTt3QkFBSU4sV0FBVTtrQ0FDWkM7OztZQUlUO1lBRUEsb0NBQW9DO1lBQ3BDZ0MsSUFBRyxLQUFzQjtvQkFBdEIsRUFBRWhDLFFBQVEsRUFBRSxHQUFHQyxPQUFPLEdBQXRCO2dCQUNELHFCQUNFLDhEQUFDK0I7b0JBQUdqQyxXQUFVO29CQUEyQixHQUFHRSxLQUFLOzhCQUM5Q0Q7O1lBR1A7WUFFQWlDLElBQUcsS0FBc0I7b0JBQXRCLEVBQUVqQyxRQUFRLEVBQUUsR0FBR0MsT0FBTyxHQUF0QjtnQkFDRCxxQkFDRSw4REFBQ2dDO29CQUFHbEMsV0FBVTtvQkFBeUIsR0FBR0UsS0FBSzs4QkFDNUNEOztZQUdQO1lBRUFrQyxJQUFHLEtBQXNCO29CQUF0QixFQUFFbEMsUUFBUSxFQUFFLEdBQUdDLE9BQU8sR0FBdEI7Z0JBQ0QscUJBQ0UsOERBQUNpQztvQkFBR25DLFdBQVU7b0JBQWEsR0FBR0UsS0FBSzs4QkFDaENEOztZQUdQO1lBRUEscUNBQXFDO1lBQ3JDbUMsSUFBRyxLQUFzQjtvQkFBdEIsRUFBRW5DLFFBQVEsRUFBRSxHQUFHQyxPQUFPLEdBQXRCO2dCQUNELHFCQUNFLDhEQUFDa0M7b0JBQUdwQyxXQUFVO29CQUF1QixHQUFHRSxLQUFLOzhCQUMzQyw0RUFBQ0s7d0JBQUtQLFdBQVU7a0NBQW1CQzs7O1lBR3pDO1lBRUFvQyxJQUFHLEtBQXNCO29CQUF0QixFQUFFcEMsUUFBUSxFQUFFLEdBQUdDLE9BQU8sR0FBdEI7Z0JBQ0QscUJBQ0UsOERBQUNtQztvQkFBR3JDLFdBQVU7b0JBQXVCLEdBQUdFLEtBQUs7OEJBQzNDLDRFQUFDSzt3QkFBS1AsV0FBVTtrQ0FBbUJDOzs7WUFHekM7WUFFQXFDLElBQUcsS0FBc0I7b0JBQXRCLEVBQUVyQyxRQUFRLEVBQUUsR0FBR0MsT0FBTyxHQUF0QjtnQkFDRCxxQkFDRSw4REFBQ29DO29CQUFHdEMsV0FBVTtvQkFBdUIsR0FBR0UsS0FBSzs4QkFDM0MsNEVBQUNLO3dCQUFLUCxXQUFVO2tDQUFtQkM7OztZQUd6QztZQUVBc0MsSUFBRyxLQUFzQjtvQkFBdEIsRUFBRXRDLFFBQVEsRUFBRSxHQUFHQyxPQUFPLEdBQXRCO2dCQUNELHFCQUNFLDhEQUFDcUM7b0JBQUd2QyxXQUFVO29CQUF1QixHQUFHRSxLQUFLOzhCQUMzQyw0RUFBQ0s7d0JBQUtQLFdBQVU7a0NBQW1CQzs7O1lBR3pDO1lBRUF1QyxJQUFHLEtBQXNCO29CQUF0QixFQUFFdkMsUUFBUSxFQUFFLEdBQUdDLE9BQU8sR0FBdEI7Z0JBQ0QscUJBQ0UsOERBQUNzQztvQkFBR3hDLFdBQVU7b0JBQXVCLEdBQUdFLEtBQUs7OEJBQzNDLDRFQUFDSzt3QkFBS1AsV0FBVTtrQ0FBbUJDOzs7WUFHekM7WUFFQXdDLElBQUcsS0FBc0I7b0JBQXRCLEVBQUV4QyxRQUFRLEVBQUUsR0FBR0MsT0FBTyxHQUF0QjtnQkFDRCxxQkFDRSw4REFBQ3VDO29CQUFHekMsV0FBVTtvQkFBdUIsR0FBR0UsS0FBSzs4QkFDM0MsNEVBQUNLO3dCQUFLUCxXQUFVO2tDQUFtQkM7OztZQUd6QztZQUVBLHdDQUF3QztZQUN4Q3lDLEdBQUUsS0FBc0I7b0JBQXRCLEVBQUV6QyxRQUFRLEVBQUUsR0FBR0MsT0FBTyxHQUF0QjtnQkFDQSxxQkFDRSw4REFBQ3dDO29CQUFFMUMsV0FBVTtvQkFBc0IsR0FBR0UsS0FBSzs4QkFDeENEOztZQUdQO1lBRUEseUNBQXlDO1lBQ3pDMEMsSUFBRyxLQUFZO29CQUFaLEVBQUUsR0FBR3pDLE9BQU8sR0FBWjtnQkFDRCxxQkFDRSw4REFBQ3lDO29CQUFHM0MsV0FBVTtvQkFBb0IsR0FBR0UsS0FBSzs7WUFFOUM7UUFDRjtrQkFFQ2hCOzs7Ozs7QUFHUCxHQUFHLENBQUMwRCxXQUFXQztJQUNiLDZFQUE2RTtJQUM3RSxJQUFJQSxVQUFVMUQsV0FBVyxFQUFFO1FBQ3pCLHdGQUF3RjtRQUN4RixNQUFNMkQsYUFBYUYsVUFBVTFELE9BQU8sQ0FBQzZELE1BQU07UUFDM0MsTUFBTUMsYUFBYUgsVUFBVTNELE9BQU8sQ0FBQzZELE1BQU07UUFDM0MsTUFBTUUsZUFBZUQsYUFBYUYsY0FBYyxPQUFPRSxhQUFhRjtRQUNwRSxPQUFPLENBQUNHO0lBQ1Y7SUFDQSwwQ0FBMEM7SUFDMUMsT0FBT0wsVUFBVTFELE9BQU8sS0FBSzJELFVBQVUzRCxPQUFPO0FBQ2hEO0tBM05NRjtBQTZOTixNQUFNa0UsaUNBQW9EekUsR0FBQUEsaURBQVUsVUFBQztRQUFDLEVBQ3BFUyxPQUFPLEVBQ1BjLFlBQVksRUFBRSxFQUNkbUQsZUFBZSxLQUFLLEVBQ3BCQyx1QkFBdUIsRUFBRSxFQUN6QmpFLGNBQWMsS0FBSyxFQUNwQjs7SUFDQyxtRUFBbUU7SUFDbkUsTUFBTWtFLGtCQUFrQixDQUFDQztRQUN2QiwrRUFBK0U7UUFDL0UsTUFBTUMsbUJBQW1CO1FBQ3pCLE1BQU1DLFVBQVVGLEtBQUtuRCxLQUFLLENBQUNvRDtRQUMzQixPQUFPQyxZQUFZLFFBQVFBLFFBQVFULE1BQU0sR0FBRztJQUM5QztJQUVBLHlGQUF5RjtJQUN6RixNQUFNVSx3QkFBd0IsQ0FBQ0g7UUFDN0IscUVBQXFFO1FBQ3JFLHFFQUFxRTtRQUNyRSxPQUFPQTtJQUNUO0lBRUEsMkRBQTJEO0lBQzNELE1BQU1JLG1CQUFtQixDQUFDSixNQUFjSztRQUN0QyxJQUFJQSxZQUFZWixNQUFNLEdBQUcsR0FBRztZQUMxQixrQ0FBa0M7WUFDbEMsTUFBTWEsZ0JBQWdCLElBQUlDLElBQUlGLFlBQVlHLEdBQUcsQ0FBQ0MsQ0FBQUE7Z0JBQzVDLElBQUk7b0JBQ0YsT0FBTyxJQUFJQyxJQUFJRCxXQUFXRSxHQUFHLEVBQUVDLFFBQVEsQ0FBQ0MsT0FBTyxDQUFDLFFBQVE7Z0JBQzFELEVBQUUsT0FBT0MsR0FBRztvQkFDVixPQUFPTCxXQUFXRSxHQUFHO2dCQUN2QjtZQUNGO1lBRUEsT0FBTztnQkFDTEksYUFBYVYsWUFBWVosTUFBTTtnQkFDL0J1QixTQUFTQyxNQUFNQyxJQUFJLENBQUNaO1lBQ3RCO1FBQ0Y7UUFFQSxvRUFBb0U7UUFDcEUsTUFBTUwsbUJBQW1CO1FBQ3pCLE1BQU1DLFVBQVVGLEtBQUtuRCxLQUFLLENBQUNvRCxxQkFBcUIsRUFBRTtRQUNsRCxNQUFNa0IsWUFBWSxJQUFJWixJQUFJTCxRQUFRTSxHQUFHLENBQUMzRCxDQUFBQTtZQUNwQyxrREFBa0Q7WUFDbEQsTUFBTXVFLGNBQWN2RSxNQUFNQSxLQUFLLENBQUM7WUFDaEMsT0FBT3VFLGNBQWNBLFdBQVcsQ0FBQyxFQUFFLEdBQUd2RTtRQUN4QztRQUNBLE1BQU13RSxnQkFBZ0JKLE1BQU1DLElBQUksQ0FBQ0M7UUFFakMsT0FBTztZQUNMSixhQUFhYixRQUFRVCxNQUFNO1lBQzNCdUIsU0FBU0s7UUFDWDtJQUNGO0lBRUEsNkRBQTZEO0lBQzdELE1BQU0sRUFBRUMsa0JBQWtCLEVBQUVDLGFBQWEsRUFBRUMsZ0JBQWdCLEVBQUUsR0FBR3BHLDhDQUFPQSxDQUFDO1FBQ3RFLE1BQU1xRyxjQUFjNUIsZ0JBQWdCRSxnQkFBZ0JuRTtRQUNwRCxNQUFNOEYsYUFBYUQsY0FBY3JCLGlCQUFpQnhFLFNBQVNrRSx3QkFBd0I7WUFBRWlCLGFBQWE7WUFBR0MsU0FBUyxFQUFFO1FBQUM7UUFDakgsTUFBTVcsWUFBWUYsY0FBY3RCLHNCQUFzQnZFLFdBQVdBO1FBRWpFLE9BQU87WUFDTDBGLG9CQUFvQkc7WUFDcEJGLGVBQWVHO1lBQ2ZGLGtCQUFrQkc7UUFDcEI7SUFDRixHQUFHO1FBQUMvRjtRQUFTaUU7UUFBY0M7S0FBcUI7SUFFaEQscUJBQ0UsOERBQUM5QztRQUFJTixXQUFXLG9CQUFpQ2IsT0FBYmEsV0FBVSxLQUFrQyxPQUEvQmIsY0FBYyxjQUFjOztZQUUxRXlGLG9DQUNDLDhEQUFDdEU7Z0JBQUlOLFdBQVU7O2tDQUNiLDhEQUFDTTt3QkFBSU4sV0FBVTs7MENBQ2IsOERBQUNlO2dDQUFJZixXQUFVO2dDQUFjZ0IsTUFBSztnQ0FBT0MsUUFBTztnQ0FBZUMsU0FBUTs7a0RBQ3JFLDhEQUFDQzt3Q0FBS0MsZUFBYzt3Q0FBUUMsZ0JBQWU7d0NBQVFDLGFBQWE7d0NBQUtDLEdBQUU7Ozs7OztrREFDdkUsOERBQUNKO3dDQUFLQyxlQUFjO3dDQUFRQyxnQkFBZTt3Q0FBUUMsYUFBYTt3Q0FBS0MsR0FBRTs7Ozs7Ozs7Ozs7OzBDQUV6RSw4REFBQ2hCOzBDQUFLOzs7Ozs7MENBQ04sOERBQUNEO2dDQUFJTixXQUFVOzBDQUNaNkUsY0FBY1IsV0FBVzs7Ozs7Ozs7Ozs7O29CQUc3QlEsY0FBY1AsT0FBTyxDQUFDdkIsTUFBTSxHQUFHLG1CQUM5Qiw4REFBQ3pDO3dCQUFJTixXQUFVOzs0QkFDWjZFLGNBQWNQLE9BQU8sQ0FBQ1ksS0FBSyxDQUFDLEdBQUcsR0FBR3BCLEdBQUcsQ0FBQyxDQUFDcUIsUUFBUUMsc0JBQzlDLDhEQUFDN0U7b0NBQWlCUCxXQUFVOzhDQUN6Qm1GO21DQURRQzs7Ozs7NEJBSVpQLGNBQWNQLE9BQU8sQ0FBQ3ZCLE1BQU0sR0FBRyxtQkFDOUIsOERBQUN4QztnQ0FBS1AsV0FBVTs7b0NBQWtCO29DQUM5QjZFLGNBQWNQLE9BQU8sQ0FBQ3ZCLE1BQU0sR0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTN0MsOERBQUMvRDtnQkFDQ0UsU0FBUzRGO2dCQUNUM0YsYUFBYUE7Ozs7Ozs7Ozs7OztBQUlyQjs7QUFFQSwrREFBZStELGdCQUFnQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvTWFya2Rvd25SZW5kZXJlci50c3g/MGE5OSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IFJlYWN0TWFya2Rvd24gZnJvbSAncmVhY3QtbWFya2Rvd24nO1xuaW1wb3J0IHJlbWFya0dmbSBmcm9tICdyZW1hcmstZ2ZtJztcbmltcG9ydCByZW1hcmtNYXRoIGZyb20gJ3JlbWFyay1tYXRoJztcbmltcG9ydCByZWh5cGVLYXRleCBmcm9tICdyZWh5cGUta2F0ZXgnO1xuaW1wb3J0IHJlaHlwZUhpZ2hsaWdodCBmcm9tICdyZWh5cGUtaGlnaGxpZ2h0JztcblxuLy8gSW1wb3J0YXIgZXN0aWxvcyBkbyBLYVRlWCBlIE1hcmtkb3duXG5pbXBvcnQgJ2thdGV4L2Rpc3Qva2F0ZXgubWluLmNzcyc7XG5pbXBvcnQgJ0Avc3R5bGVzL21hcmtkb3duLW1pbmltYWwuY3NzJztcbmltcG9ydCAnQC9zdHlsZXMvbWFya2Rvd24tbGF0ZXguY3NzJztcblxuaW50ZXJmYWNlIFdlYlNlYXJjaEFubm90YXRpb24ge1xuICB0eXBlOiBcInVybF9jaXRhdGlvblwiO1xuICB1cmw6IHN0cmluZztcbiAgdGl0bGU6IHN0cmluZztcbiAgY29udGVudD86IHN0cmluZztcbiAgc3RhcnRfaW5kZXg6IG51bWJlcjtcbiAgZW5kX2luZGV4OiBudW1iZXI7XG59XG5cbmludGVyZmFjZSBNYXJrZG93blJlbmRlcmVyUHJvcHMge1xuICBjb250ZW50OiBzdHJpbmc7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbiAgaGFzV2ViU2VhcmNoPzogYm9vbGVhbjtcbiAgd2ViU2VhcmNoQW5ub3RhdGlvbnM/OiBXZWJTZWFyY2hBbm5vdGF0aW9uW107XG4gIGlzU3RyZWFtaW5nPzogYm9vbGVhbjsgLy8gTm92YSBwcm9wIHBhcmEgaW5kaWNhciBzZSBlc3TDoSBlbSBzdHJlYW1pbmdcbn1cblxuLy8gQ29tcG9uZW50ZSBvdGltaXphZG8gcGFyYSByZW5kZXJpemHDp8OjbyBkZSBtYXJrZG93biBkdXJhbnRlIHN0cmVhbWluZ1xuY29uc3QgT3B0aW1pemVkTWFya2Rvd24gPSBSZWFjdC5tZW1vKCh7IGNvbnRlbnQsIGlzU3RyZWFtaW5nIH06IHsgY29udGVudDogc3RyaW5nOyBpc1N0cmVhbWluZzogYm9vbGVhbiB9KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPFJlYWN0TWFya2Rvd25cbiAgICAgIHJlbWFya1BsdWdpbnM9e1tyZW1hcmtHZm0sIHJlbWFya01hdGhdfVxuICAgICAgcmVoeXBlUGx1Z2lucz17W1xuICAgICAgICBbcmVoeXBlS2F0ZXgsIHtcbiAgICAgICAgICBzdHJpY3Q6IGZhbHNlLFxuICAgICAgICAgIHRocm93T25FcnJvcjogZmFsc2UsXG4gICAgICAgICAgZXJyb3JDb2xvcjogJyNjYzAwMDAnLFxuICAgICAgICAgIG1hY3Jvczoge1xuICAgICAgICAgICAgXCJcXFxcUlJcIjogXCJcXFxcbWF0aGJie1J9XCIsXG4gICAgICAgICAgICBcIlxcXFxOTlwiOiBcIlxcXFxtYXRoYmJ7Tn1cIixcbiAgICAgICAgICAgIFwiXFxcXFpaXCI6IFwiXFxcXG1hdGhiYntafVwiLFxuICAgICAgICAgICAgXCJcXFxcUVFcIjogXCJcXFxcbWF0aGJie1F9XCIsXG4gICAgICAgICAgICBcIlxcXFxDQ1wiOiBcIlxcXFxtYXRoYmJ7Q31cIlxuICAgICAgICAgIH1cbiAgICAgICAgfV0sXG4gICAgICAgIFtyZWh5cGVIaWdobGlnaHQsIHsgZGV0ZWN0OiB0cnVlLCBpZ25vcmVNaXNzaW5nOiB0cnVlIH1dXG4gICAgICBdfVxuICAgICAgY29tcG9uZW50cz17e1xuICAgICAgICAvLyBDdXN0b21pemFyIHJlbmRlcml6YcOnw6NvIGRlIGPDs2RpZ29cbiAgICAgICAgY29kZSh7IG5vZGUsIGlubGluZSwgY2xhc3NOYW1lLCBjaGlsZHJlbiwgLi4ucHJvcHMgfTogYW55KSB7XG4gICAgICAgICAgY29uc3QgbWF0Y2ggPSAvbGFuZ3VhZ2UtKFxcdyspLy5leGVjKGNsYXNzTmFtZSB8fCAnJyk7XG4gICAgICAgICAgY29uc3QgbGFuZ3VhZ2UgPSBtYXRjaCA/IG1hdGNoWzFdIDogJ3RleHQnO1xuXG4gICAgICAgICAgcmV0dXJuICFpbmxpbmUgJiYgbWF0Y2ggPyAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvZGUtYmxvY2stY29udGFpbmVyIGdyb3VwXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29kZS1oZWFkZXJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTEuNVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMyBoLTMgcm91bmRlZC1mdWxsIGJnLXJlZC00MDAvODBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMgaC0zIHJvdW5kZWQtZnVsbCBiZy15ZWxsb3ctNDAwLzgwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zIGgtMyByb3VuZGVkLWZ1bGwgYmctZ3JlZW4tNDAwLzgwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1zbGF0ZS0zMDAgY2FwaXRhbGl6ZVwiPlxuICAgICAgICAgICAgICAgICAgICB7bGFuZ3VhZ2V9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY29weS1idXR0b24gb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQoU3RyaW5nKGNoaWxkcmVuKSl9XG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIkNvcGlhciBjw7NkaWdvXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTggMTZINmEyIDIgMCAwMS0yLTJWNmEyIDIgMCAwMTItMmg4YTIgMiAwIDAxMiAydjJtLTYgMTJoOGEyIDIgMCAwMDItMnYtOGEyIDIgMCAwMC0yLTJoLThhMiAyIDAgMDAtMiAydjhhMiAyIDAgMDAyIDJ6XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHByZSBjbGFzc05hbWU9XCJjb2RlLWNvbnRlbnRcIj5cbiAgICAgICAgICAgICAgICA8Y29kZSBjbGFzc05hbWU9e2NsYXNzTmFtZX0gey4uLnByb3BzfT5cbiAgICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgICA8L2NvZGU+XG4gICAgICAgICAgICAgIDwvcHJlPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxjb2RlIGNsYXNzTmFtZT1cImlubGluZS1jb2RlXCIgey4uLnByb3BzfT5cbiAgICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgICAgPC9jb2RlPlxuICAgICAgICAgICk7XG4gICAgICAgIH0sXG5cbiAgICAgICAgLy8gQ3VzdG9taXphciByZW5kZXJpemHDp8OjbyBkZSBsaW5rc1xuICAgICAgICBhKHsgY2hpbGRyZW4sIGhyZWYsIC4uLnByb3BzIH0pIHtcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgaHJlZj17aHJlZn1cbiAgICAgICAgICAgICAgdGFyZ2V0PVwiX2JsYW5rXCJcbiAgICAgICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1hcmtkb3duLWxpbmtcIlxuICAgICAgICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICApO1xuICAgICAgICB9LFxuXG4gICAgICAgIC8vIEN1c3RvbWl6YXIgcmVuZGVyaXphw6fDo28gZGUgdGFiZWxhc1xuICAgICAgICB0YWJsZSh7IGNoaWxkcmVuLCAuLi5wcm9wcyB9KSB7XG4gICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGFibGUtd3JhcHBlclwiPlxuICAgICAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwibWFya2Rvd24tdGFibGVcIiB7Li4ucHJvcHN9PlxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgPC90YWJsZT5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICk7XG4gICAgICAgIH0sXG5cbiAgICAgICAgdGgoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfSkge1xuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGFibGUtaGVhZGVyXCIgey4uLnByb3BzfT5cbiAgICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICApO1xuICAgICAgICB9LFxuXG4gICAgICAgIHRkKHsgY2hpbGRyZW4sIC4uLnByb3BzIH0pIHtcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInRhYmxlLWNlbGxcIiB7Li4ucHJvcHN9PlxuICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICk7XG4gICAgICAgIH0sXG5cbiAgICAgICAgLy8gQ3VzdG9taXphciByZW5kZXJpemHDp8OjbyBkZSBibG9ja3F1b3Rlc1xuICAgICAgICBibG9ja3F1b3RlKHsgY2hpbGRyZW4sIC4uLnByb3BzIH0pIHtcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPGJsb2NrcXVvdGUgY2xhc3NOYW1lPVwibWFya2Rvd24tYmxvY2txdW90ZVwiIHsuLi5wcm9wc30+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicXVvdGUtY29udGVudFwiPlxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Jsb2NrcXVvdGU+XG4gICAgICAgICAgKTtcbiAgICAgICAgfSxcblxuICAgICAgICAvLyBDdXN0b21pemFyIHJlbmRlcml6YcOnw6NvIGRlIGxpc3Rhc1xuICAgICAgICB1bCh7IGNoaWxkcmVuLCAuLi5wcm9wcyB9KSB7XG4gICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJtYXJrZG93bi1saXN0IHVub3JkZXJlZFwiIHsuLi5wcm9wc30+XG4gICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgKTtcbiAgICAgICAgfSxcblxuICAgICAgICBvbCh7IGNoaWxkcmVuLCAuLi5wcm9wcyB9KSB7XG4gICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIDxvbCBjbGFzc05hbWU9XCJtYXJrZG93bi1saXN0IG9yZGVyZWRcIiB7Li4ucHJvcHN9PlxuICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICA8L29sPlxuICAgICAgICAgICk7XG4gICAgICAgIH0sXG5cbiAgICAgICAgbGkoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfSkge1xuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwibGlzdC1pdGVtXCIgey4uLnByb3BzfT5cbiAgICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICApO1xuICAgICAgICB9LFxuXG4gICAgICAgIC8vIEN1c3RvbWl6YXIgcmVuZGVyaXphw6fDo28gZGUgdMOtdHVsb3NcbiAgICAgICAgaDEoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfSkge1xuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwibWFya2Rvd24taGVhZGluZyBoMVwiIHsuLi5wcm9wc30+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImhlYWRpbmctY29udGVudFwiPntjaGlsZHJlbn08L3NwYW4+XG4gICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICk7XG4gICAgICAgIH0sXG5cbiAgICAgICAgaDIoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfSkge1xuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwibWFya2Rvd24taGVhZGluZyBoMlwiIHsuLi5wcm9wc30+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImhlYWRpbmctY29udGVudFwiPntjaGlsZHJlbn08L3NwYW4+XG4gICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICk7XG4gICAgICAgIH0sXG5cbiAgICAgICAgaDMoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfSkge1xuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwibWFya2Rvd24taGVhZGluZyBoM1wiIHsuLi5wcm9wc30+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImhlYWRpbmctY29udGVudFwiPntjaGlsZHJlbn08L3NwYW4+XG4gICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICk7XG4gICAgICAgIH0sXG5cbiAgICAgICAgaDQoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfSkge1xuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwibWFya2Rvd24taGVhZGluZyBoNFwiIHsuLi5wcm9wc30+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImhlYWRpbmctY29udGVudFwiPntjaGlsZHJlbn08L3NwYW4+XG4gICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICk7XG4gICAgICAgIH0sXG5cbiAgICAgICAgaDUoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfSkge1xuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwibWFya2Rvd24taGVhZGluZyBoNVwiIHsuLi5wcm9wc30+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImhlYWRpbmctY29udGVudFwiPntjaGlsZHJlbn08L3NwYW4+XG4gICAgICAgICAgICA8L2g1PlxuICAgICAgICAgICk7XG4gICAgICAgIH0sXG5cbiAgICAgICAgaDYoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfSkge1xuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8aDYgY2xhc3NOYW1lPVwibWFya2Rvd24taGVhZGluZyBoNlwiIHsuLi5wcm9wc30+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImhlYWRpbmctY29udGVudFwiPntjaGlsZHJlbn08L3NwYW4+XG4gICAgICAgICAgICA8L2g2PlxuICAgICAgICAgICk7XG4gICAgICAgIH0sXG5cbiAgICAgICAgLy8gQ3VzdG9taXphciByZW5kZXJpemHDp8OjbyBkZSBwYXLDoWdyYWZvc1xuICAgICAgICBwKHsgY2hpbGRyZW4sIC4uLnByb3BzIH0pIHtcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibWFya2Rvd24tcGFyYWdyYXBoXCIgey4uLnByb3BzfT5cbiAgICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICk7XG4gICAgICAgIH0sXG5cbiAgICAgICAgLy8gQ3VzdG9taXphciByZW5kZXJpemHDp8OjbyBkZSBzZXBhcmFkb3Jlc1xuICAgICAgICBocih7IC4uLnByb3BzIH0pIHtcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPGhyIGNsYXNzTmFtZT1cIm1hcmtkb3duLWRpdmlkZXJcIiB7Li4ucHJvcHN9IC8+XG4gICAgICAgICAgKTtcbiAgICAgICAgfVxuICAgICAgfX1cbiAgICA+XG4gICAgICB7Y29udGVudH1cbiAgICA8L1JlYWN0TWFya2Rvd24+XG4gICk7XG59LCAocHJldlByb3BzLCBuZXh0UHJvcHMpID0+IHtcbiAgLy8gRHVyYW50ZSBzdHJlYW1pbmcsIHPDsyByZS1yZW5kZXJpemFyIHNlIG8gY29udGXDumRvIG11ZG91IHNpZ25pZmljYXRpdmFtZW50ZVxuICBpZiAobmV4dFByb3BzLmlzU3RyZWFtaW5nKSB7XG4gICAgLy8gRHVyYW50ZSBzdHJlYW1pbmcsIHJlLXJlbmRlcml6YXIgYXBlbmFzIGEgY2FkYSAxMDAgY2FyYWN0ZXJlcyBwYXJhIG1lbGhvciBwZXJmb3JtYW5jZVxuICAgIGNvbnN0IHByZXZMZW5ndGggPSBwcmV2UHJvcHMuY29udGVudC5sZW5ndGg7XG4gICAgY29uc3QgbmV4dExlbmd0aCA9IG5leHRQcm9wcy5jb250ZW50Lmxlbmd0aDtcbiAgICBjb25zdCBzaG91bGRVcGRhdGUgPSBuZXh0TGVuZ3RoIC0gcHJldkxlbmd0aCA+PSAxMDAgfHwgbmV4dExlbmd0aCA8IHByZXZMZW5ndGg7XG4gICAgcmV0dXJuICFzaG91bGRVcGRhdGU7XG4gIH1cbiAgLy8gRm9yYSBkbyBzdHJlYW1pbmcsIGNvbXBvcnRhbWVudG8gbm9ybWFsXG4gIHJldHVybiBwcmV2UHJvcHMuY29udGVudCA9PT0gbmV4dFByb3BzLmNvbnRlbnQ7XG59KTtcblxuY29uc3QgTWFya2Rvd25SZW5kZXJlcjogUmVhY3QuRkM8TWFya2Rvd25SZW5kZXJlclByb3BzPiA9IFJlYWN0Lm1lbW8oKHtcbiAgY29udGVudCxcbiAgY2xhc3NOYW1lID0gJycsXG4gIGhhc1dlYlNlYXJjaCA9IGZhbHNlLFxuICB3ZWJTZWFyY2hBbm5vdGF0aW9ucyA9IFtdLFxuICBpc1N0cmVhbWluZyA9IGZhbHNlXG59KSA9PiB7XG4gIC8vIEZ1bsOnw6NvIHBhcmEgZGV0ZWN0YXIgc2UgbyBjb250ZcO6ZG8gY29udMOpbSBjaXRhw6fDtWVzIGRlIHdlYiBzZWFyY2hcbiAgY29uc3QgZGV0ZWN0V2ViU2VhcmNoID0gKHRleHQ6IHN0cmluZyk6IGJvb2xlYW4gPT4ge1xuICAgIC8vIERldGVjdGEgbGlua3Mgbm8gZm9ybWF0byBbZG9taW5pby5jb21dIHF1ZSBzw6NvIGNhcmFjdGVyw61zdGljb3MgZG8gd2ViIHNlYXJjaFxuICAgIGNvbnN0IHdlYlNlYXJjaFBhdHRlcm4gPSAvXFxbW1xcdy4tXStcXC5bXFx3XStcXF0vZztcbiAgICBjb25zdCBtYXRjaGVzID0gdGV4dC5tYXRjaCh3ZWJTZWFyY2hQYXR0ZXJuKTtcbiAgICByZXR1cm4gbWF0Y2hlcyAhPT0gbnVsbCAmJiBtYXRjaGVzLmxlbmd0aCA+IDA7XG4gIH07XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBwcm9jZXNzYXIgbyBjb250ZcO6ZG8gKE9wZW5Sb3V0ZXIgasOhIHJldG9ybmEgbGlua3MgZm9ybWF0YWRvcyBjb3JyZXRhbWVudGUpXG4gIGNvbnN0IHByb2Nlc3NXZWJTZWFyY2hMaW5rcyA9ICh0ZXh0OiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xuICAgIC8vIENvbW8gbyBPcGVuUm91dGVyIGrDoSByZXRvcm5hIG9zIGxpbmtzIG5vIGZvcm1hdG8gbWFya2Rvd24gY29ycmV0byxcbiAgICAvLyBuw6NvIHByZWNpc2Ftb3MgcHJvY2Vzc2FyIG5hZGEuIEFwZW5hcyByZXRvcm5hbW9zIG8gdGV4dG8gb3JpZ2luYWwuXG4gICAgcmV0dXJuIHRleHQ7XG4gIH07XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBjb250YXIgZSBleHRyYWlyIGluZm9ybWHDp8O1ZXMgc29icmUgYXMgZm9udGVzXG4gIGNvbnN0IGdldFdlYlNlYXJjaEluZm8gPSAodGV4dDogc3RyaW5nLCBhbm5vdGF0aW9uczogV2ViU2VhcmNoQW5ub3RhdGlvbltdKTogeyBzb3VyY2VDb3VudDogbnVtYmVyOyBzb3VyY2VzOiBzdHJpbmdbXSB9ID0+IHtcbiAgICBpZiAoYW5ub3RhdGlvbnMubGVuZ3RoID4gMCkge1xuICAgICAgLy8gVXNhciBhbm5vdGF0aW9ucyBzZSBkaXNwb27DrXZlaXNcbiAgICAgIGNvbnN0IHVuaXF1ZURvbWFpbnMgPSBuZXcgU2V0KGFubm90YXRpb25zLm1hcChhbm5vdGF0aW9uID0+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICByZXR1cm4gbmV3IFVSTChhbm5vdGF0aW9uLnVybCkuaG9zdG5hbWUucmVwbGFjZSgnd3d3LicsICcnKTtcbiAgICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAgIHJldHVybiBhbm5vdGF0aW9uLnVybDtcbiAgICAgICAgfVxuICAgICAgfSkpO1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBzb3VyY2VDb3VudDogYW5ub3RhdGlvbnMubGVuZ3RoLFxuICAgICAgICBzb3VyY2VzOiBBcnJheS5mcm9tKHVuaXF1ZURvbWFpbnMpXG4gICAgICB9O1xuICAgIH1cblxuICAgIC8vIEZhbGxiYWNrOiBkZXRlY3RhciBwZWxvcyBwYWRyw7VlcyBubyB0ZXh0byAoZm9ybWF0byBtYXJrZG93biBsaW5rKVxuICAgIGNvbnN0IHdlYlNlYXJjaFBhdHRlcm4gPSAvXFxbW1xcdy4tXStcXC5bXFx3XStcXF1cXChbXildK1xcKS9nO1xuICAgIGNvbnN0IG1hdGNoZXMgPSB0ZXh0Lm1hdGNoKHdlYlNlYXJjaFBhdHRlcm4pIHx8IFtdO1xuICAgIGNvbnN0IHNvdXJjZVNldCA9IG5ldyBTZXQobWF0Y2hlcy5tYXAobWF0Y2ggPT4ge1xuICAgICAgLy8gRXh0cmFpciBvIGRvbcOtbmlvIGRvIGZvcm1hdG8gW2RvbWluaW8uY29tXSh1cmwpXG4gICAgICBjb25zdCBkb21haW5NYXRjaCA9IG1hdGNoLm1hdGNoKC9cXFsoW1xcdy4tXStcXC5bXFx3XSspXFxdLyk7XG4gICAgICByZXR1cm4gZG9tYWluTWF0Y2ggPyBkb21haW5NYXRjaFsxXSA6IG1hdGNoO1xuICAgIH0pKTtcbiAgICBjb25zdCB1bmlxdWVTb3VyY2VzID0gQXJyYXkuZnJvbShzb3VyY2VTZXQpO1xuXG4gICAgcmV0dXJuIHtcbiAgICAgIHNvdXJjZUNvdW50OiBtYXRjaGVzLmxlbmd0aCxcbiAgICAgIHNvdXJjZXM6IHVuaXF1ZVNvdXJjZXNcbiAgICB9O1xuICB9O1xuXG4gIC8vIFVzYXIgdXNlTWVtbyBwYXJhIG90aW1pemFyIHByb2Nlc3NhbWVudG8gZHVyYW50ZSBzdHJlYW1pbmdcbiAgY29uc3QgeyBpc1dlYlNlYXJjaE1lc3NhZ2UsIHdlYlNlYXJjaEluZm8sIHByb2Nlc3NlZENvbnRlbnQgfSA9IHVzZU1lbW8oKCkgPT4ge1xuICAgIGNvbnN0IGlzV2ViU2VhcmNoID0gaGFzV2ViU2VhcmNoIHx8IGRldGVjdFdlYlNlYXJjaChjb250ZW50KTtcbiAgICBjb25zdCBzZWFyY2hJbmZvID0gaXNXZWJTZWFyY2ggPyBnZXRXZWJTZWFyY2hJbmZvKGNvbnRlbnQsIHdlYlNlYXJjaEFubm90YXRpb25zKSA6IHsgc291cmNlQ291bnQ6IDAsIHNvdXJjZXM6IFtdIH07XG4gICAgY29uc3QgcHJvY2Vzc2VkID0gaXNXZWJTZWFyY2ggPyBwcm9jZXNzV2ViU2VhcmNoTGlua3MoY29udGVudCkgOiBjb250ZW50O1xuXG4gICAgcmV0dXJuIHtcbiAgICAgIGlzV2ViU2VhcmNoTWVzc2FnZTogaXNXZWJTZWFyY2gsXG4gICAgICB3ZWJTZWFyY2hJbmZvOiBzZWFyY2hJbmZvLFxuICAgICAgcHJvY2Vzc2VkQ29udGVudDogcHJvY2Vzc2VkXG4gICAgfTtcbiAgfSwgW2NvbnRlbnQsIGhhc1dlYlNlYXJjaCwgd2ViU2VhcmNoQW5ub3RhdGlvbnNdKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgcmFmdGhvci1tYXJrZG93biAke2NsYXNzTmFtZX0gJHtpc1N0cmVhbWluZyA/ICdzdHJlYW1pbmcnIDogJyd9YH0+XG4gICAgICB7LyogU2XDp8OjbyBkZSBXZWIgU2VhcmNoIG1pbmltYWxpc3RhICovfVxuICAgICAge2lzV2ViU2VhcmNoTWVzc2FnZSAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwid2ViLXNlYXJjaC1pbmRpY2F0b3JcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNlYXJjaC1iYWRnZVwiPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJzZWFyY2gtaWNvblwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezEuNX0gZD1cIk0yMSAxMmE5IDkgMCAxMS0xOCAwIDkgOSAwIDAxMTggMHpcIiAvPlxuICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezEuNX0gZD1cIk05IDEybDIgMiA0LTRcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICA8c3Bhbj5CdXNjYSBuYSBXZWI8L3NwYW4+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNvdXJjZS1jb3VudFwiPlxuICAgICAgICAgICAgICB7d2ViU2VhcmNoSW5mby5zb3VyY2VDb3VudH1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIHt3ZWJTZWFyY2hJbmZvLnNvdXJjZXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNvdXJjZS1saXN0XCI+XG4gICAgICAgICAgICAgIHt3ZWJTZWFyY2hJbmZvLnNvdXJjZXMuc2xpY2UoMCwgMykubWFwKChzb3VyY2UsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPHNwYW4ga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwic291cmNlLXRhZ1wiPlxuICAgICAgICAgICAgICAgICAge3NvdXJjZX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICB7d2ViU2VhcmNoSW5mby5zb3VyY2VzLmxlbmd0aCA+IDMgJiYgKFxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNvdXJjZS10YWcgbW9yZVwiPlxuICAgICAgICAgICAgICAgICAgK3t3ZWJTZWFyY2hJbmZvLnNvdXJjZXMubGVuZ3RoIC0gM31cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBDb250ZcO6ZG8gcHJpbmNpcGFsICovfVxuICAgICAgPE9wdGltaXplZE1hcmtkb3duXG4gICAgICAgIGNvbnRlbnQ9e3Byb2Nlc3NlZENvbnRlbnR9XG4gICAgICAgIGlzU3RyZWFtaW5nPXtpc1N0cmVhbWluZ31cbiAgICAgIC8+XG4gICAgPC9kaXY+XG4gICk7XG59KTtcblxuZXhwb3J0IGRlZmF1bHQgTWFya2Rvd25SZW5kZXJlcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZU1lbW8iLCJSZWFjdE1hcmtkb3duIiwicmVtYXJrR2ZtIiwicmVtYXJrTWF0aCIsInJlaHlwZUthdGV4IiwicmVoeXBlSGlnaGxpZ2h0IiwiT3B0aW1pemVkTWFya2Rvd24iLCJtZW1vIiwiY29udGVudCIsImlzU3RyZWFtaW5nIiwicmVtYXJrUGx1Z2lucyIsInJlaHlwZVBsdWdpbnMiLCJzdHJpY3QiLCJ0aHJvd09uRXJyb3IiLCJlcnJvckNvbG9yIiwibWFjcm9zIiwiZGV0ZWN0IiwiaWdub3JlTWlzc2luZyIsImNvbXBvbmVudHMiLCJjb2RlIiwibm9kZSIsImlubGluZSIsImNsYXNzTmFtZSIsImNoaWxkcmVuIiwicHJvcHMiLCJtYXRjaCIsImV4ZWMiLCJsYW5ndWFnZSIsImRpdiIsInNwYW4iLCJidXR0b24iLCJvbkNsaWNrIiwibmF2aWdhdG9yIiwiY2xpcGJvYXJkIiwid3JpdGVUZXh0IiwiU3RyaW5nIiwidGl0bGUiLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJwcmUiLCJhIiwiaHJlZiIsInRhcmdldCIsInJlbCIsInRhYmxlIiwidGgiLCJ0ZCIsImJsb2NrcXVvdGUiLCJ1bCIsIm9sIiwibGkiLCJoMSIsImgyIiwiaDMiLCJoNCIsImg1IiwiaDYiLCJwIiwiaHIiLCJwcmV2UHJvcHMiLCJuZXh0UHJvcHMiLCJwcmV2TGVuZ3RoIiwibGVuZ3RoIiwibmV4dExlbmd0aCIsInNob3VsZFVwZGF0ZSIsIk1hcmtkb3duUmVuZGVyZXIiLCJoYXNXZWJTZWFyY2giLCJ3ZWJTZWFyY2hBbm5vdGF0aW9ucyIsImRldGVjdFdlYlNlYXJjaCIsInRleHQiLCJ3ZWJTZWFyY2hQYXR0ZXJuIiwibWF0Y2hlcyIsInByb2Nlc3NXZWJTZWFyY2hMaW5rcyIsImdldFdlYlNlYXJjaEluZm8iLCJhbm5vdGF0aW9ucyIsInVuaXF1ZURvbWFpbnMiLCJTZXQiLCJtYXAiLCJhbm5vdGF0aW9uIiwiVVJMIiwidXJsIiwiaG9zdG5hbWUiLCJyZXBsYWNlIiwiZSIsInNvdXJjZUNvdW50Iiwic291cmNlcyIsIkFycmF5IiwiZnJvbSIsInNvdXJjZVNldCIsImRvbWFpbk1hdGNoIiwidW5pcXVlU291cmNlcyIsImlzV2ViU2VhcmNoTWVzc2FnZSIsIndlYlNlYXJjaEluZm8iLCJwcm9jZXNzZWRDb250ZW50IiwiaXNXZWJTZWFyY2giLCJzZWFyY2hJbmZvIiwicHJvY2Vzc2VkIiwic2xpY2UiLCJzb3VyY2UiLCJpbmRleCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx\n"));

/***/ })

});