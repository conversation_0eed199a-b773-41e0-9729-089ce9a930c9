"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-katex";
exports.ids = ["vendor-chunks/rehype-katex"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-katex/lib/index.js":
/*!************************************************!*\
  !*** ./node_modules/rehype-katex/lib/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeKatex)\n/* harmony export */ });\n/* harmony import */ var hast_util_from_html_isomorphic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-from-html-isomorphic */ \"(ssr)/./node_modules/hast-util-from-html-isomorphic/lib/index.js\");\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var katex__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! katex */ \"(ssr)/./node_modules/katex/dist/katex.mjs\");\n/* harmony import */ var unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit-parents */ \"(ssr)/./node_modules/unist-util-visit-parents/lib/index.js\");\n/**\n * @import {ElementContent, Root} from 'hast'\n * @import {KatexOptions} from 'katex'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @typedef {Omit<KatexOptions, 'displayMode' | 'throwOnError'>} Options\n */\n\n\n\n\n\n\n/** @type {Readonly<Options>} */\nconst emptyOptions = {}\n/** @type {ReadonlyArray<unknown>} */\nconst emptyClasses = []\n\n/**\n * Render elements with a `language-math` (or `math-display`, `math-inline`)\n * class with KaTeX.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns\n *   Transform.\n */\nfunction rehypeKatex(options) {\n  const settings = options || emptyOptions\n\n  /**\n   * Transform.\n   *\n   * @param {Root} tree\n   *   Tree.\n   * @param {VFile} file\n   *   File.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  return function (tree, file) {\n    ;(0,unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__.visitParents)(tree, 'element', function (element, parents) {\n      const classes = Array.isArray(element.properties.className)\n        ? element.properties.className\n        : emptyClasses\n      // This class can be generated from markdown with ` ```math `.\n      const languageMath = classes.includes('language-math')\n      // This class is used by `remark-math` for flow math (block, `$$\\nmath\\n$$`).\n      const mathDisplay = classes.includes('math-display')\n      // This class is used by `remark-math` for text math (inline, `$math$`).\n      const mathInline = classes.includes('math-inline')\n      let displayMode = mathDisplay\n\n      // Any class is fine.\n      if (!languageMath && !mathDisplay && !mathInline) {\n        return\n      }\n\n      let parent = parents[parents.length - 1]\n      let scope = element\n\n      // If this was generated with ` ```math `, replace the `<pre>` and use\n      // display.\n      if (\n        element.tagName === 'code' &&\n        languageMath &&\n        parent &&\n        parent.type === 'element' &&\n        parent.tagName === 'pre'\n      ) {\n        scope = parent\n        parent = parents[parents.length - 2]\n        displayMode = true\n      }\n\n      /* c8 ignore next -- verbose to test. */\n      if (!parent) return\n\n      const value = (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_2__.toText)(scope, {whitespace: 'pre'})\n\n      /** @type {Array<ElementContent> | string | undefined} */\n      let result\n\n      try {\n        result = katex__WEBPACK_IMPORTED_MODULE_0__[\"default\"].renderToString(value, {\n          ...settings,\n          displayMode,\n          throwOnError: true\n        })\n      } catch (error) {\n        const cause = /** @type {Error} */ (error)\n        const ruleId = cause.name.toLowerCase()\n\n        file.message('Could not render math with KaTeX', {\n          ancestors: [...parents, element],\n          cause,\n          place: element.position,\n          ruleId,\n          source: 'rehype-katex'\n        })\n\n        // KaTeX *should* handle `ParseError` itself, but not others.\n        // it doesn’t always:\n        // <https://github.com/remarkjs/react-markdown/issues/853>\n        try {\n          result = katex__WEBPACK_IMPORTED_MODULE_0__[\"default\"].renderToString(value, {\n            ...settings,\n            displayMode,\n            strict: 'ignore',\n            throwOnError: false\n          })\n        } catch {\n          // Generate similar markup if this is an other error.\n          // See: <https://github.com/KaTeX/KaTeX/blob/5dc7af0/docs/error.md>.\n          result = [\n            {\n              type: 'element',\n              tagName: 'span',\n              properties: {\n                className: ['katex-error'],\n                style: 'color:' + (settings.errorColor || '#cc0000'),\n                title: String(error)\n              },\n              children: [{type: 'text', value}]\n            }\n          ]\n        }\n      }\n\n      if (typeof result === 'string') {\n        const root = (0,hast_util_from_html_isomorphic__WEBPACK_IMPORTED_MODULE_3__.fromHtmlIsomorphic)(result, {fragment: true})\n        // Cast as we don’t expect `doctypes` in KaTeX result.\n        result = /** @type {Array<ElementContent>} */ (root.children)\n      }\n\n      const index = parent.children.indexOf(scope)\n      parent.children.splice(index, 1, ...result)\n      return unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__.SKIP\n    })\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-katex/lib/index.js\n");

/***/ })

};
;