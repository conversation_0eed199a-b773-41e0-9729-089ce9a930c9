"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useAdvancedSearch.ts":
/*!****************************************!*\
  !*** ./src/hooks/useAdvancedSearch.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAdvancedSearch: function() { return /* binding */ useAdvancedSearch; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useAdvancedSearch(models) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { debounceMs = 300, enableSuggestions = true, cacheResults = true, fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n    const [searchTerm, setSearchTermState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [hasSearched, setHasSearched] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [debounceTimer, setDebounceTimer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Cache para resultados de busca\n    const [searchCache] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Map());\n    // Função de busca principal\n    const performSearch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (term)=>{\n        if (!term.trim()) {\n            setSearchResults([]);\n            setHasSearched(false);\n            return;\n        }\n        setIsSearching(true);\n        try {\n            // Verificar cache se habilitado\n            if (cacheResults && searchCache.has(term)) {\n                setSearchResults(searchCache.get(term));\n                setHasSearched(true);\n                setIsSearching(false);\n                return;\n            }\n            // Realizar busca\n            const results = searchModels(models, term, {\n                fuzzyThreshold,\n                maxResults,\n                boostFavorites\n            });\n            // Salvar no cache se habilitado\n            if (cacheResults) {\n                searchCache.set(term, results);\n            }\n            setSearchResults(results);\n            setHasSearched(true);\n        } catch (error) {\n            console.error(\"Error performing search:\", error);\n            setSearchResults([]);\n        } finally{\n            setIsSearching(false);\n        }\n    }, [\n        models,\n        fuzzyThreshold,\n        maxResults,\n        boostFavorites,\n        cacheResults,\n        searchCache\n    ]);\n    // Função para definir termo de busca com debounce\n    const setSearchTerm = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((term)=>{\n        setSearchTermState(term);\n        // Limpar timer anterior\n        if (debounceTimer) {\n            clearTimeout(debounceTimer);\n        }\n        // Configurar novo timer\n        const timer = setTimeout(()=>{\n            performSearch(term);\n        }, debounceMs);\n        setDebounceTimer(timer);\n    }, [\n        debounceTimer,\n        debounceMs,\n        performSearch\n    ]);\n    // Sistema de sugestões removido junto com analytics\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setSuggestions([]);\n    }, [\n        searchTerm,\n        enableSuggestions\n    ]);\n    // Limpar busca\n    const clearSearch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setSearchTermState(\"\");\n        setSearchResults([]);\n        setHasSearched(false);\n        setSuggestions([]);\n        if (debounceTimer) {\n            clearTimeout(debounceTimer);\n            setDebounceTimer(null);\n        }\n    }, [\n        debounceTimer\n    ]);\n    // Limpar timer ao desmontar\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            if (debounceTimer) {\n                clearTimeout(debounceTimer);\n            }\n        };\n    }, [\n        debounceTimer\n    ]);\n    return {\n        searchTerm,\n        setSearchTerm,\n        searchResults,\n        suggestions,\n        isSearching,\n        hasSearched,\n        clearSearch,\n        trackModelSelection\n    };\n}\n// Função de busca com fuzzy matching\nfunction searchModels(models, searchTerm) {\n    let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n    const { fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n    if (!searchTerm.trim()) {\n        return [];\n    }\n    const term = searchTerm.toLowerCase();\n    const results = [];\n    for (const model of models){\n        let score = 0;\n        const matchedFields = [];\n        let highlightedName = model.name;\n        let highlightedDescription = model.description || \"\";\n        // Busca no nome (peso maior)\n        if (model.name.toLowerCase().includes(term)) {\n            score += 10;\n            matchedFields.push(\"name\");\n            highlightedName = highlightText(model.name, term);\n        }\n        // Busca no ID (peso médio)\n        if (model.id.toLowerCase().includes(term)) {\n            score += 7;\n            matchedFields.push(\"id\");\n        }\n        // Busca na descrição (peso menor)\n        if (model.description && model.description.toLowerCase().includes(term)) {\n            score += 3;\n            matchedFields.push(\"description\");\n            highlightedDescription = highlightText(model.description, term);\n        }\n        // Boost para favoritos\n        if (boostFavorites && model.isFavorite) {\n            score *= 1.5;\n        }\n        // Boost para modelos gratuitos se buscar por \"free\" ou \"grátis\"\n        if ((term.includes(\"free\") || term.includes(\"gr\\xe1tis\")) && isModelFree(model)) {\n            score += 5;\n        }\n        // Boost para modelos caros se buscar por \"expensive\" ou \"caro\"\n        if ((term.includes(\"expensive\") || term.includes(\"caro\")) && getTotalPrice(model) > 0.00002) {\n            score += 5;\n        }\n        if (score > 0) {\n            results.push({\n                model,\n                score,\n                matchedFields,\n                highlightedName,\n                highlightedDescription\n            });\n        }\n    }\n    // Ordenar por score e limitar resultados\n    return results.sort((a, b)=>b.score - a.score).slice(0, maxResults);\n}\n// Função auxiliar para destacar texto\nfunction highlightText(text, term) {\n    const regex = new RegExp(\"(\".concat(term, \")\"), \"gi\");\n    return text.replace(regex, '<mark class=\"bg-yellow-300 text-black px-1 rounded\">$1</mark>');\n}\n// Função auxiliar para verificar se modelo é gratuito\nfunction isModelFree(model) {\n    const promptPrice = parseFloat(model.pricing.prompt);\n    const completionPrice = parseFloat(model.pricing.completion);\n    return promptPrice === 0 && completionPrice === 0;\n}\n// Função auxiliar para obter preço total\nfunction getTotalPrice(model) {\n    const promptPrice = parseFloat(model.pricing.prompt);\n    const completionPrice = parseFloat(model.pricing.completion);\n    return promptPrice + completionPrice;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\n"));

/***/ })

});