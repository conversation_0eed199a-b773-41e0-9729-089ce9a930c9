"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/Sidebar.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _CreateChatModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CreateChatModal */ \"(app-pages-browser)/./src/components/dashboard/CreateChatModal.tsx\");\n/* harmony import */ var _CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CreateFolderModal */ \"(app-pages-browser)/./src/components/dashboard/CreateFolderModal.tsx\");\n/* harmony import */ var _ConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ConfirmDeleteModal */ \"(app-pages-browser)/./src/components/dashboard/ConfirmDeleteModal.tsx\");\n/* harmony import */ var _PasswordProtectedModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./PasswordProtectedModal */ \"(app-pages-browser)/./src/components/dashboard/PasswordProtectedModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst Sidebar = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { userData, isOpen, isCollapsed = false, onToggle, onSettingsOpen, onChatSelect, currentChat, onUpdateOpenRouterBalance, showCloseButton = true, onDownloadModal, onAttachmentsModal, onStatisticsModal } = param;\n    _s();\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unorganizedChats, setUnorganizedChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openRouterBalance, setOpenRouterBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        balance: 0,\n        isLoading: true,\n        error: undefined\n    });\n    const [createChatModalOpen, setCreateChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editChatModalOpen, setEditChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingChat, setEditingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [createFolderModalOpen, setCreateFolderModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editFolderModalOpen, setEditFolderModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingFolder, setEditingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [draggedChat, setDraggedChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverFolder, setDragOverFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredFolder, setHoveredFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animatingChat, setAnimatingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [configDropdownOpen, setConfigDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estados para modais de confirmação\n    const [deleteConfirmModal, setDeleteConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        type: \"chat\",\n        id: \"\",\n        name: \"\"\n    });\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para modal de senha\n    const [passwordModal, setPasswordModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        chatId: \"\",\n        chatName: \"\",\n        action: \"access\"\n    });\n    // Ref para controlar requisições em andamento\n    const fetchingBalanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Ref para o dropdown de configurações\n    const configDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fetchOpenRouterBalance = async ()=>{\n        // Evitar múltiplas requisições simultâneas\n        if (fetchingBalanceRef.current) {\n            console.log(\"Requisi\\xe7\\xe3o de saldo j\\xe1 em andamento, ignorando...\");\n            return;\n        }\n        try {\n            fetchingBalanceRef.current = true;\n            console.log(\"Iniciando busca do saldo do OpenRouter...\");\n            setOpenRouterBalance((prev)=>({\n                    ...prev,\n                    isLoading: true,\n                    error: undefined\n                }));\n            let openRouterApiKey = \"\";\n            // Primeiro, tentar buscar na nova estrutura (endpoints)\n            try {\n                const endpointsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"endpoints\");\n                const endpointsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(endpointsRef);\n                endpointsSnapshot.forEach((doc)=>{\n                    const data = doc.data();\n                    if (data.isActive && data.url && data.url.includes(\"openrouter.ai\")) {\n                        openRouterApiKey = data.apiKey;\n                    }\n                });\n            } catch (error) {\n                console.log(\"Nova estrutura n\\xe3o encontrada, tentando estrutura antiga...\");\n            }\n            // Se não encontrou na nova estrutura, buscar na estrutura antiga\n            if (!openRouterApiKey) {\n                try {\n                    const { doc: docImport, getDoc } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n                    const configRef = docImport(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"configuracoes\", \"settings\");\n                    const configDoc = await getDoc(configRef);\n                    if (configDoc.exists()) {\n                        const config = configDoc.data();\n                        if (config.endpoints) {\n                            // Buscar endpoint ativo do OpenRouter na estrutura antiga\n                            Object.values(config.endpoints).forEach((endpoint)=>{\n                                if (endpoint.ativo && endpoint.url && endpoint.url.includes(\"openrouter.ai\")) {\n                                    openRouterApiKey = endpoint.apiKey;\n                                }\n                            });\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"Estrutura antiga tamb\\xe9m n\\xe3o encontrada\");\n                }\n            }\n            if (!openRouterApiKey) {\n                setOpenRouterBalance((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: \"Nenhuma API key do OpenRouter configurada\"\n                    }));\n                return;\n            }\n            console.log(\"API Key encontrada, buscando saldo...\");\n            // Fazer requisição para buscar créditos\n            const response = await fetch(\"/api/openrouter/credits\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    apiKey: openRouterApiKey\n                })\n            });\n            const data = await response.json();\n            console.log(\"Resposta da API:\", data);\n            if (data.success) {\n                setOpenRouterBalance({\n                    balance: data.balance,\n                    isLoading: false,\n                    error: undefined\n                });\n                console.log(\"Saldo carregado com sucesso:\", data.balance);\n            } else {\n                setOpenRouterBalance((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: data.error || \"Erro ao buscar saldo\"\n                    }));\n                console.log(\"Erro ao buscar saldo:\", data.error);\n            }\n        } catch (error) {\n            console.error(\"Erro ao buscar saldo do OpenRouter:\", error);\n            setOpenRouterBalance((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: \"Erro ao conectar com OpenRouter\"\n                }));\n        } finally{\n            fetchingBalanceRef.current = false;\n        }\n    };\n    const loadChats = async ()=>{\n        try {\n            // Carregar chats\n            const chatsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\");\n            const chatsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(chatsRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)(\"lastUpdatedAt\", \"desc\"));\n            const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(chatsQuery);\n            const chats = [];\n            chatsSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                chats.push({\n                    id: doc.id,\n                    name: data.name || \"Conversa sem nome\",\n                    lastMessage: data.ultimaMensagem || \"Nenhuma mensagem ainda\",\n                    lastMessageTime: data.ultimaMensagemEm || data.createdAt,\n                    folder: data.folderId,\n                    password: data.password\n                });\n            });\n            // Carregar pastas\n            const foldersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\");\n            const foldersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(foldersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)(\"createdAt\", \"asc\"));\n            const foldersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(foldersQuery);\n            const loadedFolders = [];\n            foldersSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                const folderChats = chats.filter((chat)=>chat.folder === doc.id);\n                loadedFolders.push({\n                    id: doc.id,\n                    name: data.name,\n                    description: data.description,\n                    color: data.color || \"#3B82F6\",\n                    isExpanded: data.expandedByDefault !== false,\n                    chats: folderChats\n                });\n            });\n            // Chats sem pasta\n            const unorganized = chats.filter((chat)=>!chat.folder);\n            setFolders(loadedFolders);\n            setUnorganizedChats(unorganized);\n        } catch (error) {\n            console.error(\"Erro ao carregar chats e pastas:\", error);\n        }\n    };\n    // Ref para controlar se já foi carregado\n    const balanceLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (userData.username) {\n            loadChats();\n            // Carregar saldo apenas se ainda não foi carregado\n            if (!balanceLoadedRef.current) {\n                console.log(\"Carregando saldo do OpenRouter pela primeira vez...\");\n                fetchOpenRouterBalance();\n                balanceLoadedRef.current = true;\n            }\n        }\n    }, [\n        userData.username\n    ]);\n    // Função para mover chat para o topo com animação\n    const moveToTop = async (chatId)=>{\n        try {\n            // Animar o chat\n            setAnimatingChat(chatId);\n            // Atualizar o timestamp no Firestore para mover para o topo\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", chatId), {\n                lastUpdatedAt: new Date().toISOString()\n            });\n            // Recarregar chats após um pequeno delay para mostrar a animação\n            setTimeout(()=>{\n                loadChats();\n                setAnimatingChat(null);\n            }, 300);\n        } catch (error) {\n            console.error(\"Erro ao mover chat para o topo:\", error);\n            setAnimatingChat(null);\n        }\n    };\n    // Fechar dropdown de configurações apenas com clique real (não mouse leave)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (configDropdownRef.current && !configDropdownRef.current.contains(event.target)) {\n                setConfigDropdownOpen(null);\n            }\n        };\n        if (configDropdownOpen) {\n            // Usar um pequeno delay para evitar fechamento imediato\n            const timeoutId = setTimeout(()=>{\n                document.addEventListener(\"mousedown\", handleClickOutside);\n            }, 100);\n            return ()=>{\n                clearTimeout(timeoutId);\n                document.removeEventListener(\"mousedown\", handleClickOutside);\n            };\n        }\n    }, [\n        configDropdownOpen\n    ]);\n    // Expor as funções para o componente pai\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            reloadChats: loadChats,\n            updateOpenRouterBalance: fetchOpenRouterBalance,\n            moveToTop: moveToTop\n        }));\n    const handleNewChat = ()=>{\n        setCreateChatModalOpen(true);\n    };\n    const handleNewFolder = ()=>{\n        setCreateFolderModalOpen(true);\n    };\n    const handleFolderCreated = (folderId)=>{\n        console.log(\"Pasta criada:\", folderId);\n        loadChats(); // Recarregar para mostrar a nova pasta\n    };\n    const handleFolderUpdated = (folderId)=>{\n        console.log(\"Pasta atualizada:\", folderId);\n        loadChats(); // Recarregar para mostrar as alterações\n        setEditFolderModalOpen(false);\n        setEditingFolder(null);\n    };\n    const handleEditFolder = async (folderId)=>{\n        try {\n            var _folderDoc_docs_find;\n            // Buscar dados da pasta no Firestore\n            const folderDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\")));\n            const folderData = (_folderDoc_docs_find = folderDoc.docs.find((doc)=>doc.id === folderId)) === null || _folderDoc_docs_find === void 0 ? void 0 : _folderDoc_docs_find.data();\n            if (folderData) {\n                setEditingFolder({\n                    id: folderId,\n                    name: folderData.name,\n                    description: folderData.description,\n                    color: folderData.color || \"#3B82F6\",\n                    expandedByDefault: folderData.expandedByDefault !== false\n                });\n                setEditFolderModalOpen(true);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar dados da pasta:\", error);\n            alert(\"Erro ao carregar dados da pasta.\");\n        }\n    };\n    const handleDeleteFolder = (folderId, folderName)=>{\n        setDeleteConfirmModal({\n            isOpen: true,\n            type: \"folder\",\n            id: folderId,\n            name: folderName\n        });\n    };\n    const handleDeleteChat = (chatId, chatName)=>{\n        setDeleteConfirmModal({\n            isOpen: true,\n            type: \"chat\",\n            id: chatId,\n            name: chatName\n        });\n    };\n    // Função para deletar todos os anexos de um chat\n    const deleteChatAttachments = async (chatId)=>{\n        try {\n            const attachmentsRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(userData.username, \"/conversas/\").concat(chatId, \"/anexos\"));\n            const attachmentsList = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.listAll)(attachmentsRef);\n            // Deletar todos os arquivos de anexos\n            const deletePromises = attachmentsList.items.map((item)=>(0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.deleteObject)(item));\n            await Promise.all(deletePromises);\n            console.log(\"\".concat(attachmentsList.items.length, \" anexos deletados do chat \").concat(chatId));\n        } catch (error) {\n            console.log(\"Erro ao deletar anexos ou pasta de anexos n\\xe3o encontrada:\", error);\n        }\n    };\n    const confirmDelete = async ()=>{\n        setIsDeleting(true);\n        try {\n            if (deleteConfirmModal.type === \"folder\") {\n                var _folders_find;\n                // Deletar pasta\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\", deleteConfirmModal.id));\n                // Mover todos os chats da pasta para \"sem pasta\"\n                const chatsInFolder = ((_folders_find = folders.find((f)=>f.id === deleteConfirmModal.id)) === null || _folders_find === void 0 ? void 0 : _folders_find.chats) || [];\n                for (const chat of chatsInFolder){\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", chat.id), {\n                        folderId: null,\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n                console.log(\"Pasta deletada:\", deleteConfirmModal.id);\n            } else {\n                // Deletar chat\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", deleteConfirmModal.id));\n                // Deletar arquivo chat.json do Storage\n                try {\n                    const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(userData.username, \"/conversas/\").concat(deleteConfirmModal.id, \"/chat.json\"));\n                    await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.deleteObject)(chatJsonRef);\n                } catch (storageError) {\n                    console.log(\"Arquivo chat.json no storage n\\xe3o encontrado:\", storageError);\n                }\n                // Deletar todos os anexos do chat\n                await deleteChatAttachments(deleteConfirmModal.id);\n                // Se era o chat ativo, limpar seleção\n                if (currentChat === deleteConfirmModal.id) {\n                    onChatSelect(\"\");\n                }\n                console.log(\"Chat deletado:\", deleteConfirmModal.id);\n            }\n            // Recarregar dados\n            loadChats();\n            // Fechar modal\n            setDeleteConfirmModal({\n                isOpen: false,\n                type: \"chat\",\n                id: \"\",\n                name: \"\"\n            });\n        } catch (error) {\n            console.error(\"Erro ao deletar:\", error);\n            alert(\"Erro ao deletar. Tente novamente.\");\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // Funções de drag and drop\n    const handleDragStart = (chatId)=>{\n        setDraggedChat(chatId);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedChat(null);\n        setDragOverFolder(null);\n    };\n    const handleDragOver = (e, folderId)=>{\n        e.preventDefault();\n        setDragOverFolder(folderId);\n    };\n    const handleDragLeave = ()=>{\n        setDragOverFolder(null);\n    };\n    const handleDrop = async (e, folderId)=>{\n        e.preventDefault();\n        if (!draggedChat) return;\n        try {\n            // Atualizar o chat no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", draggedChat), {\n                folderId: folderId,\n                updatedAt: new Date().toISOString()\n            });\n            console.log(\"Chat \".concat(draggedChat, \" movido para pasta \").concat(folderId || \"sem pasta\"));\n            // Recarregar chats para refletir a mudança\n            loadChats();\n        } catch (error) {\n            console.error(\"Erro ao mover chat:\", error);\n        } finally{\n            setDraggedChat(null);\n            setDragOverFolder(null);\n        }\n    };\n    const handleChatCreated = (chatId)=>{\n        console.log(\"Chat criado:\", chatId);\n        // Recarregar a lista de chats\n        loadChats();\n        onChatSelect(chatId);\n    };\n    const handleEditChat = (chat)=>{\n        setEditingChat(chat);\n        setEditChatModalOpen(true);\n    };\n    const handleChatUpdated = (chatId)=>{\n        console.log(\"Chat atualizado:\", chatId);\n        // Recarregar a lista de chats\n        loadChats();\n        setEditChatModalOpen(false);\n        setEditingChat(null);\n    };\n    const handleHomeClick = ()=>{\n        onChatSelect(null); // Limpar chat atual para ir para área inicial\n    };\n    const toggleFolder = (folderId)=>{\n        setFolders((prev)=>prev.map((folder)=>folder.id === folderId ? {\n                    ...folder,\n                    isExpanded: !folder.isExpanded\n                } : folder));\n    };\n    const formatTime = (timeString)=>{\n        const date = new Date(timeString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return date.toLocaleTimeString(\"pt-BR\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffInHours < 168) {\n            return date.toLocaleDateString(\"pt-BR\", {\n                weekday: \"short\"\n            });\n        } else {\n            return date.toLocaleDateString(\"pt-BR\", {\n                day: \"2-digit\",\n                month: \"2-digit\"\n            });\n        }\n    };\n    const getFolderHexColor = (colorName)=>{\n        const colorMap = {\n            \"blue\": \"#3B82F6\",\n            \"green\": \"#10B981\",\n            \"yellow\": \"#F59E0B\",\n            \"red\": \"#EF4444\",\n            \"purple\": \"#8B5CF6\",\n            \"cyan\": \"#06B6D4\",\n            \"lime\": \"#84CC16\",\n            \"orange\": \"#F97316\",\n            \"pink\": \"#EC4899\",\n            \"gray\": \"#6B7280\"\n        };\n        return colorMap[colorName] || colorName;\n    };\n    // Funções para chat protegido por senha\n    const checkChatPassword = async (chatId, inputPassword)=>{\n        try {\n            var _chatDoc_docs_find;\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\")));\n            const chatData = (_chatDoc_docs_find = chatDoc.docs.find((doc)=>doc.id === chatId)) === null || _chatDoc_docs_find === void 0 ? void 0 : _chatDoc_docs_find.data();\n            if (chatData && chatData.password) {\n                return chatData.password === inputPassword;\n            }\n            return true; // Se não tem senha, permite acesso\n        } catch (error) {\n            console.error(\"Erro ao verificar senha:\", error);\n            return false;\n        }\n    };\n    const handleProtectedAction = (chatId, chatName, action)=>{\n        const chat = [\n            ...unorganizedChats,\n            ...folders.flatMap((f)=>f.chats)\n        ].find((c)=>c.id === chatId);\n        if (chat === null || chat === void 0 ? void 0 : chat.password) {\n            // Chat protegido, abrir modal de senha\n            setPasswordModal({\n                isOpen: true,\n                chatId,\n                chatName,\n                action\n            });\n        } else {\n            // Chat não protegido, executar ação diretamente\n            executeAction(chatId, chatName, action);\n        }\n    };\n    const executeAction = (chatId, chatName, action)=>{\n        switch(action){\n            case \"access\":\n                onChatSelect(chatId);\n                break;\n            case \"edit\":\n                const chat = [\n                    ...unorganizedChats,\n                    ...folders.flatMap((f)=>f.chats)\n                ].find((c)=>c.id === chatId);\n                if (chat) {\n                    handleEditChat(chat);\n                }\n                break;\n            case \"delete\":\n                handleDeleteChat(chatId, chatName);\n                break;\n        }\n    };\n    const handlePasswordSuccess = ()=>{\n        executeAction(passwordModal.chatId, passwordModal.chatName, passwordModal.action);\n        setPasswordModal({\n            isOpen: false,\n            chatId: \"\",\n            chatName: \"\",\n            action: \"access\"\n        });\n    };\n    const formatBalance = (balance)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        }).format(balance);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n        fixed top-0 left-0 h-full w-80 bg-gradient-to-b from-blue-950/95 via-blue-900/95 to-blue-950/95\\n        backdrop-blur-xl border-r border-blue-700/30 z-50 transform transition-all duration-300 shadow-2xl\\n        \".concat(isOpen ? \"translate-x-0\" : \"-translate-x-full\", \"\\n        \").concat(isCollapsed ? \"lg:-translate-x-full\" : \"lg:translate-x-0\", \"\\n      \"),\n                children: [\n                    showCloseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onToggle,\n                        className: \"absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-blue-600 hover:bg-blue-500 rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-xl border-2 border-white/20 z-10 group\",\n                        title: \"Fechar sidebar\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-white transition-transform duration-300 group-hover:rotate-180\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M15 19l-7-7 7-7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 690,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 681,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-semibold text-lg\",\n                                                children: userData.username.charAt(0).toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 706,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-semibold text-lg\",\n                                                    children: userData.username\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                openRouterBalance.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 border border-blue-300 border-t-transparent rounded-full animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-blue-200 text-sm\",\n                                                            children: \"Carregando...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 715,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 713,\n                                                    columnNumber: 19\n                                                }, undefined) : openRouterBalance.error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-400 text-sm\",\n                                                    title: openRouterBalance.error,\n                                                    children: \"$0.00\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-200 text-sm\",\n                                                    children: [\n                                                        \"$\",\n                                                        openRouterBalance.balance.toFixed(4)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 722,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onSettingsOpen,\n                                            className: \"text-blue-200 hover:text-white transition-colors p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 732,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 703,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-2 border-b border-white/5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleHomeClick,\n                                        className: \"group relative w-full bg-gradient-to-r from-blue-600/90 to-cyan-600/90 hover:from-blue-500 hover:to-cyan-500 text-white rounded-xl transition-all duration-300 overflow-hidden shadow-md shadow-blue-900/15 hover:shadow-lg hover:shadow-blue-800/25 border border-blue-400/20 hover:border-cyan-400/40 backdrop-blur-sm hover:-translate-y-0.5 active:translate-y-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center justify-center py-2.5 px-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 rounded-lg bg-white/15 flex items-center justify-center group-hover:bg-white/25 group-hover:scale-110 transition-all duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    strokeWidth: 2.5,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 760,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 759,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-semibold text-white/95 group-hover:text-white transition-colors duration-300\",\n                                                                children: \"\\xc1rea Inicial\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 756,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute right-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-1 group-hover:translate-x-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-white/80\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2.5,\n                                                                d: \"M9 5l7 7-7 7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 772,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-1.5 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleNewChat,\n                                                className: \"group relative bg-gradient-to-br from-blue-600/80 to-cyan-600/80 hover:from-blue-500/90 hover:to-cyan-500/90 text-white rounded-lg transition-all duration-300 overflow-hidden shadow-sm shadow-blue-900/10 hover:shadow-md hover:shadow-blue-800/20 border border-blue-400/15 hover:border-cyan-400/30 backdrop-blur-sm hover:-translate-y-0.5 active:translate-y-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex flex-col items-center justify-center py-2.5 px-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-md bg-white/15 flex items-center justify-center group-hover:bg-white/25 group-hover:scale-110 transition-all duration-300 mb-1.5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    strokeWidth: 2.5,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        d: \"M12 4v16m8-8H4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 796,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 795,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 793,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-white/90 group-hover:text-white transition-colors duration-300 text-center leading-tight\",\n                                                                children: [\n                                                                    \"Nova\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 800,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    \"Conversa\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 799,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 780,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleNewFolder,\n                                                className: \"group relative bg-gradient-to-br from-blue-600/80 to-cyan-600/80 hover:from-blue-500/90 hover:to-cyan-500/90 text-white rounded-lg transition-all duration-300 overflow-hidden shadow-sm shadow-blue-900/10 hover:shadow-md hover:shadow-blue-800/20 border border-blue-400/15 hover:border-cyan-400/30 backdrop-blur-sm hover:-translate-y-0.5 active:translate-y-0\",\n                                                title: \"Criar Nova Pasta\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 815,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex flex-col items-center justify-center py-2.5 px-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-7 h-7 rounded-md bg-white/15 flex items-center justify-center group-hover:bg-white/25 group-hover:scale-110 transition-all duration-300 mb-1.5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    strokeWidth: 2.5,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 822,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 819,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-white/90 group-hover:text-white transition-colors duration-300 text-center leading-tight\",\n                                                                children: [\n                                                                    \"Nova\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 827,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    \"Pasta\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 826,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 818,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 779,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 741,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-2 border-b border-white/10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-blue-300 text-sm font-bold uppercase tracking-wider flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 840,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Conversas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 841,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 839,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 838,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 837,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-2 py-2\",\n                                        children: folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-2\",\n                                                onDragOver: (e)=>handleDragOver(e, folder.id),\n                                                onDragLeave: handleDragLeave,\n                                                onDrop: (e)=>handleDrop(e, folder.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"group relative rounded-lg transition-all duration-300 cursor-pointer \".concat(hoveredFolder === folder.id ? \"bg-blue-800/40\" : \"hover:bg-blue-800/30\", \" \").concat(dragOverFolder === folder.id ? \"bg-blue-500/30 ring-2 ring-blue-400/50\" : \"\"),\n                                                        onMouseEnter: ()=>setHoveredFolder(folder.id),\n                                                        onMouseLeave: ()=>setHoveredFolder(null),\n                                                        onClick: ()=>toggleFolder(folder.id),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-shrink-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4 text-blue-300 transition-transform duration-200 \".concat(folder.isExpanded ? \"rotate-90\" : \"\"),\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M9 5l7 7-7 7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 877,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 869,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 868,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-6 h-6 rounded flex items-center justify-center flex-shrink-0\",\n                                                                            style: {\n                                                                                backgroundColor: getFolderHexColor(folder.color) + \"40\"\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4\",\n                                                                                style: {\n                                                                                    color: getFolderHexColor(folder.color)\n                                                                                },\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 892,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 886,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 882,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"text-base font-semibold text-blue-100 truncate\",\n                                                                                            children: folder.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 899,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-xs text-blue-300/70 bg-blue-900/50 px-2 py-0.5 rounded-full\",\n                                                                                            children: folder.chats.length\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 902,\n                                                                                            columnNumber: 29\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 898,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                folder.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-blue-300/60 truncate mt-0.5\",\n                                                                                    children: folder.description\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 907,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 897,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 866,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 transition-all duration-300 \".concat(hoveredFolder === folder.id ? \"opacity-100 translate-x-0\" : \"opacity-0 translate-x-2\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200\",\n                                                                            title: \"Editar pasta\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                handleEditFolder(folder.id);\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-3.5 h-3.5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        strokeWidth: 2,\n                                                                                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 927,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        strokeWidth: 2,\n                                                                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 928,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 926,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 918,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200\",\n                                                                            title: \"Deletar pasta\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                handleDeleteFolder(folder.id, folder.name);\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-3.5 h-3.5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 940,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 939,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 931,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 915,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 865,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 857,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    folder.isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-6 mt-2 space-y-1\",\n                                                        children: folder.chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatItem, {\n                                                                chat: chat,\n                                                                isActive: currentChat === chat.id,\n                                                                onClick: ()=>handleProtectedAction(chat.id, chat.name, \"access\"),\n                                                                onEdit: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"edit\"),\n                                                                onDelete: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"delete\"),\n                                                                onDragStart: handleDragStart,\n                                                                onDragEnd: handleDragEnd,\n                                                                isDragging: draggedChat === chat.id,\n                                                                isAnimating: animatingChat === chat.id,\n                                                                configDropdownOpen: configDropdownOpen,\n                                                                setConfigDropdownOpen: setConfigDropdownOpen,\n                                                                onDownloadModal: onDownloadModal,\n                                                                onAttachmentsModal: onAttachmentsModal,\n                                                                onStatisticsModal: onStatisticsModal\n                                                            }, chat.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 951,\n                                                                columnNumber: 25\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 949,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, folder.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 847,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-2 py-2\",\n                                        onDragOver: (e)=>handleDragOver(e, null),\n                                        onDragLeave: handleDragLeave,\n                                        onDrop: (e)=>handleDrop(e, null),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-3 py-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-blue-300 text-xs font-bold uppercase tracking-wider transition-all duration-200 flex items-center space-x-2 \".concat(dragOverFolder === null && draggedChat ? \"text-blue-400\" : \"\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 986,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Sem Pasta \",\n                                                                dragOverFolder === null && draggedChat ? \"(Solte aqui para remover da pasta)\" : \"\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 987,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 983,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 982,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1 min-h-[60px] transition-all duration-200 \".concat(dragOverFolder === null && draggedChat ? \"bg-blue-500/10 rounded-lg p-2 border-2 border-dashed border-blue-400/50\" : \"\"),\n                                                children: unorganizedChats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-white/30\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 997,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 996,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 995,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-white/40 text-xs\",\n                                                            children: \"Nenhuma conversa sem pasta\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 1001,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 994,\n                                                    columnNumber: 19\n                                                }, undefined) : unorganizedChats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatItem, {\n                                                        chat: chat,\n                                                        isActive: currentChat === chat.id,\n                                                        onClick: ()=>handleProtectedAction(chat.id, chat.name, \"access\"),\n                                                        onEdit: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"edit\"),\n                                                        onDelete: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"delete\"),\n                                                        onDragStart: handleDragStart,\n                                                        onDragEnd: handleDragEnd,\n                                                        isDragging: draggedChat === chat.id,\n                                                        isAnimating: animatingChat === chat.id,\n                                                        configDropdownOpen: configDropdownOpen,\n                                                        setConfigDropdownOpen: setConfigDropdownOpen,\n                                                        onDownloadModal: onDownloadModal,\n                                                        onAttachmentsModal: onAttachmentsModal,\n                                                        onStatisticsModal: onStatisticsModal\n                                                    }, chat.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 1005,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 990,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 976,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    folders.length === 0 && unorganizedChats.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white/30 mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-12 h-12 mx-auto\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 1032,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1031,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 1030,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/50 text-sm\",\n                                                children: \"Nenhuma conversa ainda\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 1036,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/30 text-xs mt-1\",\n                                                children: 'Clique em \"Nova Conversa\" para come\\xe7ar'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 1029,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 835,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden p-4 border-t border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggle,\n                                    className: \"w-full bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg  transition-all duration-200 flex items-center justify-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 1050,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1049,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Fechar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1052,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1044,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1043,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 701,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 673,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateChatModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: createChatModalOpen,\n                onClose: ()=>setCreateChatModalOpen(false),\n                username: userData.username,\n                onChatCreated: handleChatCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1059,\n                columnNumber: 7\n            }, undefined),\n            editingChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateChatModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: editChatModalOpen,\n                onClose: ()=>{\n                    setEditChatModalOpen(false);\n                    setEditingChat(null);\n                },\n                username: userData.username,\n                onChatCreated: handleChatUpdated,\n                editingChat: editingChat\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1068,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: createFolderModalOpen,\n                onClose: ()=>setCreateFolderModalOpen(false),\n                username: userData.username,\n                onFolderCreated: handleFolderCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1081,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: editFolderModalOpen,\n                onClose: ()=>{\n                    setEditFolderModalOpen(false);\n                    setEditingFolder(null);\n                },\n                username: userData.username,\n                onFolderCreated: handleFolderUpdated,\n                editingFolder: editingFolder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1089,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: deleteConfirmModal.isOpen,\n                onClose: ()=>setDeleteConfirmModal({\n                        isOpen: false,\n                        type: \"chat\",\n                        id: \"\",\n                        name: \"\"\n                    }),\n                onConfirm: confirmDelete,\n                title: deleteConfirmModal.type === \"folder\" ? \"Deletar Pasta\" : \"Deletar Conversa\",\n                message: deleteConfirmModal.type === \"folder\" ? 'Tem certeza que deseja deletar esta pasta? Todas as conversas dentro dela ser\\xe3o movidas para \"Sem Pasta\".' : \"Tem certeza que deseja deletar esta conversa? Todas as mensagens ser\\xe3o perdidas permanentemente.\",\n                itemName: deleteConfirmModal.name,\n                isLoading: isDeleting\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1101,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PasswordProtectedModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: passwordModal.isOpen,\n                onClose: ()=>setPasswordModal({\n                        isOpen: false,\n                        chatId: \"\",\n                        chatName: \"\",\n                        action: \"access\"\n                    }),\n                onSuccess: handlePasswordSuccess,\n                chatName: passwordModal.chatName,\n                onPasswordSubmit: (password)=>checkChatPassword(passwordModal.chatId, password)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1116,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n}, \"QR32rGUAYI3qrnFAzPZA85tmHfQ=\")), \"QR32rGUAYI3qrnFAzPZA85tmHfQ=\");\n_c1 = Sidebar;\nSidebar.displayName = \"Sidebar\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (Sidebar);\nfunction ChatItem(param) {\n    let { chat, isActive, onClick, onEdit, onDelete, onDragStart, onDragEnd, isDragging, isAnimating = false, configDropdownOpen, setConfigDropdownOpen, onDownloadModal, onAttachmentsModal, onStatisticsModal } = param;\n    _s1();\n    // Ref específica para este item de chat\n    const itemConfigDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Fechar dropdown deste item quando clicar fora\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (itemConfigDropdownRef.current && !itemConfigDropdownRef.current.contains(event.target)) {\n                if (configDropdownOpen === chat.id) {\n                    setConfigDropdownOpen(null);\n                }\n            }\n        };\n        if (configDropdownOpen === chat.id) {\n            // Usar um pequeno delay para evitar fechamento imediato\n            const timeoutId = setTimeout(()=>{\n                document.addEventListener(\"mousedown\", handleClickOutside);\n            }, 100);\n            return ()=>{\n                clearTimeout(timeoutId);\n                document.removeEventListener(\"mousedown\", handleClickOutside);\n            };\n        }\n    }, [\n        configDropdownOpen,\n        chat.id,\n        setConfigDropdownOpen\n    ]);\n    const formatTime = (timeString)=>{\n        const date = new Date(timeString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return date.toLocaleTimeString(\"pt-BR\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffInHours < 168) {\n            return \"h\\xe1 \".concat(Math.floor(diffInHours / 24), \" dias\");\n        } else {\n            return date.toLocaleDateString(\"pt-BR\", {\n                day: \"2-digit\",\n                month: \"2-digit\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: true,\n        onDragStart: ()=>onDragStart(chat.id),\n        onDragEnd: onDragEnd,\n        className: \"group relative rounded-xl transition-all duration-300 cursor-move \".concat(isActive ? \"bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border border-blue-500/30 shadow-lg\" : \"hover:bg-blue-800/30 border border-transparent\", \" \").concat(isDragging ? \"opacity-50 scale-95\" : \"\", \" \").concat(isAnimating ? \"animate-pulse bg-blue-500/20 scale-105\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onClick,\n                className: \"w-full text-left p-3 flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative \".concat(isActive ? \"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30\" : \"bg-blue-700/50 group-hover:bg-blue-600/70\"),\n                        children: [\n                            chat.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-2 h-2 text-white\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 3,\n                                        d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 1233,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1232,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1231,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 text-white\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                strokeWidth: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1239,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1238,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1224,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-hidden flex-1 min-w-0 transition-all duration-300 group-hover:pr-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"truncate text-sm font-semibold \".concat(isActive ? \"text-white\" : \"text-blue-100 group-hover:text-white\"),\n                                    children: chat.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1245,\n                                    columnNumber: 15\n                                }, this),\n                                chat.lastMessageTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs flex-shrink-0 ml-2 \".concat(isActive ? \"text-blue-300/60\" : \"text-blue-400/50 group-hover:text-blue-300/70\"),\n                                    children: formatTime(chat.lastMessageTime)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1253,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1244,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1243,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1220,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 transition-all duration-300 \".concat(configDropdownOpen === chat.id ? \"opacity-100\" : \"opacity-0 group-hover:opacity-100\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        ref: itemConfigDropdownRef,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-1.5 rounded-lg transition-all duration-200 backdrop-blur-sm \".concat(configDropdownOpen === chat.id ? \"bg-cyan-600/90 text-cyan-200\" : \"bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white\"),\n                                title: \"Configura\\xe7\\xf5es\",\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    setConfigDropdownOpen(configDropdownOpen === chat.id ? null : chat.id);\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-3.5 h-3.5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1284,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1285,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1283,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1271,\n                                columnNumber: 11\n                            }, this),\n                            configDropdownOpen === chat.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-full right-0 mb-2 bg-blue-900/95 backdrop-blur-xl rounded-xl border border-blue-600/30 shadow-2xl shadow-blue-900/60 p-2 min-w-[180px] z-50 animate-in slide-in-from-bottom-2 duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                onEdit(chat.id, chat.name);\n                                                setConfigDropdownOpen(null);\n                                            },\n                                            className: \"w-full flex items-center space-x-3 p-2.5 rounded-lg bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg border border-blue-600/20 hover:scale-[1.02]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 1303,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 1304,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1302,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Configura\\xe7\\xe3o\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1306,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1294,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                onStatisticsModal === null || onStatisticsModal === void 0 ? void 0 : onStatisticsModal(chat.id);\n                                                setConfigDropdownOpen(null);\n                                            },\n                                            className: \"w-full flex items-center space-x-3 p-2.5 rounded-lg bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg border border-blue-600/20 hover:scale-[1.02]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 1319,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1318,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Estat\\xedsticas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1321,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1310,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                onDownloadModal === null || onDownloadModal === void 0 ? void 0 : onDownloadModal(chat.id);\n                                                setConfigDropdownOpen(null);\n                                            },\n                                            className: \"w-full flex items-center space-x-3 p-2.5 rounded-lg bg-blue-800/40 hover:bg-blue-700/50 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg border border-blue-600/20 hover:scale-[1.02]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 1334,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1333,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Download\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 1336,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1325,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1292,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1291,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1270,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm\",\n                        title: \"Deletar\",\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            onDelete(chat.id, chat.name);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3.5 h-3.5\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1352,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1351,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1343,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1266,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n        lineNumber: 1208,\n        columnNumber: 5\n    }, this);\n}\n_s1(ChatItem, \"6WBL0LKLhC/v4f94gWhcXYAtQGE=\");\n_c2 = ChatItem;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Sidebar$forwardRef\");\n$RefreshReg$(_c1, \"Sidebar\");\n$RefreshReg$(_c2, \"ChatItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar.tsx\n"));

/***/ })

});