import { useState, useEffect, useCallback, useMemo } from 'react';
import { ChatMessage } from '@/lib/types/chat';
import { 
  ChatSession, 
  ChatSessionsMetadata, 
  SessionLoadingState, 
  SessionNavigation,
  ChatContext
} from '@/lib/types/chatSessions';
import { chatSessionsService } from '@/lib/services/chatSessionsService';
import { useAppearanceStyles } from './useAppearanceStyles';

interface UseChatSessionsProps {
  chatId: string;
  messages: ChatMessage[];
  enabled?: boolean;
}

interface UseChatSessionsReturn {
  // Estado das sessões
  sessions: ChatSession[];
  currentSession: ChatSession | null;
  metadata: ChatSessionsMetadata | null;
  
  // Estado de carregamento
  loadingState: SessionLoadingState;
  
  // Navegação
  navigation: SessionNavigation | null;
  
  // Contexto completo (para IA)
  fullContext: ChatContext | null;
  
  // Funções de controle
  navigateToSession: (sessionId: string) => Promise<void>;
  navigateToPrevious: () => Promise<void>;
  navigateToNext: () => Promise<void>;
  loadPreviousSession: () => Promise<void>;
  refreshSessions: () => Promise<void>;
  
  // Utilitários
  isSessionsEnabled: boolean;
  totalSessions: number;
  currentSessionIndex: number;
}

/**
 * Hook para gerenciar sessões de chat com carregamento progressivo
 * IMPORTANTE: Sessões são apenas para visualização, IA sempre usa todas as mensagens
 */
export function useChatSessions({ 
  chatId, 
  messages, 
  enabled = true 
}: UseChatSessionsProps): UseChatSessionsReturn {
  const { settings } = useAppearanceStyles();
  
  // Estados
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [metadata, setMetadata] = useState<ChatSessionsMetadata | null>(null);
  const [navigation, setNavigation] = useState<SessionNavigation | null>(null);
  const [fullContext, setFullContext] = useState<ChatContext | null>(null);
  const [loadingState, setLoadingState] = useState<SessionLoadingState>({
    isLoading: false,
    progress: 0
  });

  // Função para dividir mensagens em sessões
  const divideIntoSessions = useCallback(async () => {
    if (!enabled || messages.length === 0) {
      setSessions([]);
      setCurrentSession(null);
      setMetadata(null);
      setNavigation(null);
      setFullContext(null);
      return;
    }

    setLoadingState(prev => ({ ...prev, isLoading: true, progress: 0 }));

    try {
      // Divide mensagens em sessões
      const result = chatSessionsService.divideIntoSessions(
        messages,
        settings.palavrasPorSessao,
        chatId
      );

      setSessions(result.sessions);
      setMetadata(result.metadata);

      // Define sessão ativa como atual
      const activeSession = result.sessions.find(s => s.isActive) || result.sessions[result.sessions.length - 1];
      setCurrentSession(activeSession || null);

      // Atualiza navegação
      if (activeSession) {
        const nav = await chatSessionsService.navigateToSession(activeSession.id);
        setNavigation(nav);
      }

      // Obtém contexto completo
      const context = await chatSessionsService.getFullChatContext(chatId);
      setFullContext(context);

      setLoadingState(prev => ({ ...prev, progress: 100 }));
    } catch (error) {
      console.error('Erro ao dividir mensagens em sessões:', error);
      setLoadingState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Erro desconhecido' 
      }));
    } finally {
      setLoadingState(prev => ({ ...prev, isLoading: false }));
    }
  }, [chatId, messages, settings.palavrasPorSessao, enabled]);

  // Função para navegar para uma sessão específica
  const navigateToSession = useCallback(async (sessionId: string) => {
    setLoadingState(prev => ({ ...prev, isLoading: true, loadingSessionId: sessionId }));

    try {
      const session = await chatSessionsService.loadSessionForView(chatId, sessionId);
      const nav = await chatSessionsService.navigateToSession(sessionId);
      
      setCurrentSession(session);
      setNavigation(nav);
    } catch (error) {
      console.error('Erro ao navegar para sessão:', error);
      setLoadingState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Erro ao carregar sessão' 
      }));
    } finally {
      setLoadingState(prev => ({ ...prev, isLoading: false, loadingSessionId: undefined }));
    }
  }, [chatId]);

  // Função para navegar para sessão anterior
  const navigateToPrevious = useCallback(async () => {
    if (!navigation?.hasPrevious || !metadata) return;
    
    const currentIndex = navigation.currentIndex;
    const previousSessionId = metadata.sessionIds[currentIndex - 1];
    
    if (previousSessionId) {
      await navigateToSession(previousSessionId);
    }
  }, [navigation, metadata, navigateToSession]);

  // Função para navegar para próxima sessão
  const navigateToNext = useCallback(async () => {
    if (!navigation?.hasNext || !metadata) return;
    
    const currentIndex = navigation.currentIndex;
    const nextSessionId = metadata.sessionIds[currentIndex + 1];
    
    if (nextSessionId) {
      await navigateToSession(nextSessionId);
    }
  }, [navigation, metadata, navigateToSession]);

  // Função para carregar sessão anterior (carregamento progressivo)
  const loadPreviousSession = useCallback(async () => {
    if (!navigation?.hasPrevious || !metadata) return;
    
    const currentIndex = navigation.currentIndex;
    const previousSessionId = metadata.sessionIds[currentIndex - 1];
    
    if (previousSessionId) {
      setLoadingState(prev => ({ ...prev, isLoading: true, progress: 0 }));
      
      try {
        await chatSessionsService.loadSessionForView(chatId, previousSessionId);
        setLoadingState(prev => ({ ...prev, progress: 100 }));
      } catch (error) {
        console.error('Erro ao carregar sessão anterior:', error);
        setLoadingState(prev => ({ 
          ...prev, 
          error: error instanceof Error ? error.message : 'Erro ao carregar sessão anterior' 
        }));
      } finally {
        setLoadingState(prev => ({ ...prev, isLoading: false }));
      }
    }
  }, [chatId, navigation, metadata]);

  // Função para atualizar sessões
  const refreshSessions = useCallback(async () => {
    await divideIntoSessions();
  }, [divideIntoSessions]);

  // Efeito para dividir mensagens quando mudarem
  useEffect(() => {
    divideIntoSessions();
  }, [divideIntoSessions]);

  // Valores computados
  const isSessionsEnabled = enabled && sessions.length > 1;
  const totalSessions = metadata?.totalSessions || 0;
  const currentSessionIndex = navigation?.currentIndex || 0;

  return {
    // Estado das sessões
    sessions,
    currentSession,
    metadata,
    
    // Estado de carregamento
    loadingState,
    
    // Navegação
    navigation,
    
    // Contexto completo (para IA)
    fullContext,
    
    // Funções de controle
    navigateToSession,
    navigateToPrevious,
    navigateToNext,
    loadPreviousSession,
    refreshSessions,
    
    // Utilitários
    isSessionsEnabled,
    totalSessions,
    currentSessionIndex
  };
}
