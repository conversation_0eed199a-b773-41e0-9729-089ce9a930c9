"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useAdvancedSearch.ts":
/*!****************************************!*\
  !*** ./src/hooks/useAdvancedSearch.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAdvancedSearch: function() { return /* binding */ useAdvancedSearch; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useAdvancedSearch(models) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { debounceMs = 300, enableSuggestions = true, cacheResults = true, fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n    const [searchTerm, setSearchTermState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [hasSearched, setHasSearched] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [debounceTimer, setDebounceTimer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Cache para resultados de busca\n    const [searchCache] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Map());\n    // Função de busca principal\n    const performSearch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (term)=>{\n        if (!term.trim()) {\n            setSearchResults([]);\n            setHasSearched(false);\n            return;\n        }\n        setIsSearching(true);\n        try {\n            // Verificar cache se habilitado\n            if (cacheResults && searchCache.has(term)) {\n                setSearchResults(searchCache.get(term));\n                setHasSearched(true);\n                setIsSearching(false);\n                return;\n            }\n            // Realizar busca\n            const results = searchModels(models, term, {\n                fuzzyThreshold,\n                maxResults,\n                boostFavorites\n            });\n            // Salvar no cache se habilitado\n            if (cacheResults) {\n                searchCache.set(term, results);\n            }\n            setSearchResults(results);\n            setHasSearched(true);\n        } catch (error) {\n            console.error(\"Error performing search:\", error);\n            setSearchResults([]);\n        } finally{\n            setIsSearching(false);\n        }\n    }, [\n        models,\n        fuzzyThreshold,\n        maxResults,\n        boostFavorites,\n        cacheResults,\n        searchCache\n    ]);\n    // Função para definir termo de busca com debounce\n    const setSearchTerm = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((term)=>{\n        setSearchTermState(term);\n        // Limpar timer anterior\n        if (debounceTimer) {\n            clearTimeout(debounceTimer);\n        }\n        // Configurar novo timer\n        const timer = setTimeout(()=>{\n            performSearch(term);\n        }, debounceMs);\n        setDebounceTimer(timer);\n    }, [\n        debounceTimer,\n        debounceMs,\n        performSearch\n    ]);\n    // Carregar sugestões quando o termo muda\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (enableSuggestions && searchTerm.length > 0 && userId) {\n            searchAnalyticsService.getSearchSuggestions(userId, searchTerm, 5).then(setSuggestions).catch((error)=>{\n                console.error(\"Error loading suggestions:\", error);\n                setSuggestions([]);\n            });\n        } else {\n            setSuggestions([]);\n        }\n    }, [\n        searchTerm,\n        enableSuggestions,\n        userId\n    ]);\n    // Limpar busca\n    const clearSearch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setSearchTermState(\"\");\n        setSearchResults([]);\n        setHasSearched(false);\n        setSuggestions([]);\n        if (debounceTimer) {\n            clearTimeout(debounceTimer);\n            setDebounceTimer(null);\n        }\n    }, [\n        debounceTimer\n    ]);\n    // Função vazia para manter compatibilidade\n    const trackModelSelection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (modelId)=>{\n    // Sistema de analytics removido\n    }, []);\n    // Limpar timer ao desmontar\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            if (debounceTimer) {\n                clearTimeout(debounceTimer);\n            }\n        };\n    }, [\n        debounceTimer\n    ]);\n    return {\n        searchTerm,\n        setSearchTerm,\n        searchResults,\n        suggestions,\n        isSearching,\n        hasSearched,\n        clearSearch,\n        trackModelSelection\n    };\n}\n// Função de busca com fuzzy matching\nfunction searchModels(models, searchTerm) {\n    let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n    const { fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n    if (!searchTerm.trim()) {\n        return [];\n    }\n    const term = searchTerm.toLowerCase();\n    const results = [];\n    for (const model of models){\n        let score = 0;\n        const matchedFields = [];\n        let highlightedName = model.name;\n        let highlightedDescription = model.description || \"\";\n        // Busca no nome (peso maior)\n        if (model.name.toLowerCase().includes(term)) {\n            score += 10;\n            matchedFields.push(\"name\");\n            highlightedName = highlightText(model.name, term);\n        }\n        // Busca no ID (peso médio)\n        if (model.id.toLowerCase().includes(term)) {\n            score += 7;\n            matchedFields.push(\"id\");\n        }\n        // Busca na descrição (peso menor)\n        if (model.description && model.description.toLowerCase().includes(term)) {\n            score += 3;\n            matchedFields.push(\"description\");\n            highlightedDescription = highlightText(model.description, term);\n        }\n        // Boost para favoritos\n        if (boostFavorites && model.isFavorite) {\n            score *= 1.5;\n        }\n        // Boost para modelos gratuitos se buscar por \"free\" ou \"grátis\"\n        if ((term.includes(\"free\") || term.includes(\"gr\\xe1tis\")) && isModelFree(model)) {\n            score += 5;\n        }\n        // Boost para modelos caros se buscar por \"expensive\" ou \"caro\"\n        if ((term.includes(\"expensive\") || term.includes(\"caro\")) && getTotalPrice(model) > 0.00002) {\n            score += 5;\n        }\n        if (score > 0) {\n            results.push({\n                model,\n                score,\n                matchedFields,\n                highlightedName,\n                highlightedDescription\n            });\n        }\n    }\n    // Ordenar por score e limitar resultados\n    return results.sort((a, b)=>b.score - a.score).slice(0, maxResults);\n}\n// Função auxiliar para destacar texto\nfunction highlightText(text, term) {\n    const regex = new RegExp(\"(\".concat(term, \")\"), \"gi\");\n    return text.replace(regex, '<mark class=\"bg-yellow-300 text-black px-1 rounded\">$1</mark>');\n}\n// Função auxiliar para verificar se modelo é gratuito\nfunction isModelFree(model) {\n    const promptPrice = parseFloat(model.pricing.prompt);\n    const completionPrice = parseFloat(model.pricing.completion);\n    return promptPrice === 0 && completionPrice === 0;\n}\n// Função auxiliar para obter preço total\nfunction getTotalPrice(model) {\n    const promptPrice = parseFloat(model.pricing.prompt);\n    const completionPrice = parseFloat(model.pricing.completion);\n    return promptPrice + completionPrice;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\n"));

/***/ })

});