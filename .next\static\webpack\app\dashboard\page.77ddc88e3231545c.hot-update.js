"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/aiService.ts":
/*!***************************************!*\
  !*** ./src/lib/services/aiService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiService: function() { return /* binding */ aiService; }\n/* harmony export */ });\n/* harmony import */ var _chatSessionsService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chatSessionsService */ \"(app-pages-browser)/./src/lib/services/chatSessionsService.ts\");\n// Serviço para integração com Firebase Functions de IA\n\nclass AIService {\n    /**\n   * Envia mensagem para a IA com streaming\n   */ async sendMessage(config, onChunk, onComplete, onError) {\n        try {\n            // Criar novo AbortController para esta requisição\n            this.abortController = new AbortController();\n            // Preparar dados da requisição\n            const requestData = {\n                username: config.username,\n                chatId: config.chatId,\n                message: config.message,\n                model: config.model,\n                attachments: config.attachments || [],\n                isRegeneration: config.isRegeneration || false,\n                webSearchEnabled: config.webSearchEnabled || false,\n                userMessageId: config.userMessageId\n            };\n            console.log(\"=== DEBUG: AI SERVICE REQUEST DATA ===\");\n            console.log(\"Request data:\", JSON.stringify(requestData, null, 2));\n            console.log(\"Attachments length:\", requestData.attachments.length);\n            if (requestData.attachments.length > 0) {\n                console.log(\"First attachment:\", JSON.stringify(requestData.attachments[0], null, 2));\n            }\n            // Enviar requisição\n            const response = await fetch(this.functionUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestData),\n                signal: this.abortController.signal\n            });\n            console.log(\"\\uD83C\\uDF10 URL:\", response.url);\n            console.groupEnd();\n            if (!response.ok) {\n                const errorData = await response.json();\n                console.group(\"❌ AI SERVICE - ERRO NA RESPOSTA\");\n                console.error(\"Status:\", response.status);\n                console.error(\"Status Text:\", response.statusText);\n                console.error(\"Error Data:\", errorData);\n                console.groupEnd();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            if (!response.body) {\n                throw new Error(\"Response body is not available\");\n            }\n            // Processar stream\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let fullResponse = \"\";\n            // Processar stream\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    fullResponse += chunk;\n                    // Chamar callback para cada chunk\n                    onChunk(chunk);\n                }\n                // Chamar callback de conclusão\n                onComplete(fullResponse);\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    return;\n                }\n                onError(error.message);\n            } else {\n                onError(\"Erro desconhecido na comunica\\xe7\\xe3o com a IA\");\n            }\n        } finally{\n            this.abortController = null;\n        }\n    }\n    /**\n   * Cancela a requisição em andamento\n   */ cancelRequest() {\n        if (this.abortController) {\n            this.abortController.abort();\n            this.abortController = null;\n        }\n    }\n    /**\n   * Verifica se há uma requisição em andamento\n   */ isRequestInProgress() {\n        return this.abortController !== null;\n    }\n    /**\n   * Carrega mensagens de um chat usando a API route\n   */ async loadChatMessages(username, chatId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId), {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao carregar chat: \".concat(response.statusText));\n            }\n            const chatData = await response.json();\n            return chatData.messages || [];\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            return [];\n        }\n    }\n    /**\n   * Obtém contexto completo do chat para envio à IA\n   * IMPORTANTE: Sempre usa TODAS as mensagens, independente das sessões\n   */ async getFullChatContextForAI(chatId) {\n        try {\n            // Tenta obter contexto do sistema de sessões primeiro\n            const context = await _chatSessionsService__WEBPACK_IMPORTED_MODULE_0__.chatSessionsService.getFullChatContext(chatId);\n            if (context && context.allMessages.length > 0) {\n                // Converte mensagens do formato interno para formato da IA\n                return this.convertToAIFormat(context.allMessages);\n            }\n            // Fallback: se não há sessões, retorna array vazio\n            // O contexto será construído normalmente pelo carregamento direto\n            return [];\n        } catch (error) {\n            console.warn(\"Sistema de sess\\xf5es n\\xe3o dispon\\xedvel, usando carregamento direto:\", error);\n            return [];\n        }\n    }\n    /**\n   * Converte mensagens do formato interno para o formato da IA\n   */ convertToAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    }\n    /**\n   * Converte mensagens do formato da IA para o formato interno\n   */ convertFromAIFormat(messages) {\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                sender: msg.role === \"user\" ? \"user\" : \"ai\",\n                timestamp: msg.timestamp,\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    }\n    /**\n   * Gera ID único para mensagens\n   */ generateMessageId() {\n        return Date.now().toString(36) + Math.random().toString(36).substring(2);\n    }\n    /**\n   * Valida configuração antes de enviar\n   */ validateConfig(config) {\n        var _config_username, _config_chatId, _config_message;\n        if (!((_config_username = config.username) === null || _config_username === void 0 ? void 0 : _config_username.trim())) {\n            throw new Error(\"Username \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_chatId = config.chatId) === null || _config_chatId === void 0 ? void 0 : _config_chatId.trim())) {\n            throw new Error(\"Chat ID \\xe9 obrigat\\xf3rio\");\n        }\n        if (!((_config_message = config.message) === null || _config_message === void 0 ? void 0 : _config_message.trim())) {\n            throw new Error(\"Mensagem \\xe9 obrigat\\xf3ria\");\n        }\n    // Removido limite de caracteres para mensagens\n    }\n    /**\n   * Envia mensagem com validação\n   */ async sendMessageSafe(config, onChunk, onComplete, onError) {\n        try {\n            this.validateConfig(config);\n            await this.sendMessage(config, onChunk, onComplete, onError);\n        } catch (error) {\n            if (error instanceof Error) {\n                onError(error.message);\n            } else {\n                onError(\"Erro de valida\\xe7\\xe3o\");\n            }\n        }\n    }\n    /**\n   * Deleta uma mensagem do chat no Firebase Storage\n   */ async deleteMessage(username, chatId, messageId) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao deletar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            return false;\n        }\n    }\n    /**\n   * Atualiza uma mensagem no chat no Firebase Storage\n   */ async updateMessage(username, chatId, messageId, newContent) {\n        try {\n            const response = await fetch(\"/api/chat/\".concat(username, \"/\").concat(chatId, \"/message/\").concat(messageId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    content: newContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Erro ao atualizar mensagem: \".concat(response.statusText));\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar mensagem:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.functionUrl = \"https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI\";\n        this.abortController = null;\n    }\n}\n// Exportar instância singleton\nconst aiService = new AIService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (aiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc2VydmljZXMvYWlTZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsdURBQXVEO0FBQ0s7QUE0QjVELE1BQU1DO0lBSUo7O0dBRUMsR0FDRCxNQUFNQyxZQUNKQyxNQUF1QixFQUN2QkMsT0FBZ0MsRUFDaENDLFVBQTBDLEVBQzFDQyxPQUFnQyxFQUNqQjtRQUNmLElBQUk7WUFDRixrREFBa0Q7WUFDbEQsSUFBSSxDQUFDQyxlQUFlLEdBQUcsSUFBSUM7WUFFM0IsK0JBQStCO1lBQy9CLE1BQU1DLGNBQWM7Z0JBQ2xCQyxVQUFVUCxPQUFPTyxRQUFRO2dCQUN6QkMsUUFBUVIsT0FBT1EsTUFBTTtnQkFDckJDLFNBQVNULE9BQU9TLE9BQU87Z0JBQ3ZCQyxPQUFPVixPQUFPVSxLQUFLO2dCQUNuQkMsYUFBYVgsT0FBT1csV0FBVyxJQUFJLEVBQUU7Z0JBQ3JDQyxnQkFBZ0JaLE9BQU9ZLGNBQWMsSUFBSTtnQkFDekNDLGtCQUFrQmIsT0FBT2EsZ0JBQWdCLElBQUk7Z0JBQzdDQyxlQUFlZCxPQUFPYyxhQUFhO1lBQ3JDO1lBRUFDLFFBQVFDLEdBQUcsQ0FBQztZQUNaRCxRQUFRQyxHQUFHLENBQUMsaUJBQWlCQyxLQUFLQyxTQUFTLENBQUNaLGFBQWEsTUFBTTtZQUMvRFMsUUFBUUMsR0FBRyxDQUFDLHVCQUF1QlYsWUFBWUssV0FBVyxDQUFDUSxNQUFNO1lBQ2pFLElBQUliLFlBQVlLLFdBQVcsQ0FBQ1EsTUFBTSxHQUFHLEdBQUc7Z0JBQ3RDSixRQUFRQyxHQUFHLENBQUMscUJBQXFCQyxLQUFLQyxTQUFTLENBQUNaLFlBQVlLLFdBQVcsQ0FBQyxFQUFFLEVBQUUsTUFBTTtZQUNwRjtZQUVBLG9CQUFvQjtZQU1wQixNQUFNUyxXQUFXLE1BQU1DLE1BQU0sSUFBSSxDQUFDQyxXQUFXLEVBQUU7Z0JBQzdDQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1SLEtBQUtDLFNBQVMsQ0FBQ1o7Z0JBQ3JCb0IsUUFBUSxJQUFJLENBQUN0QixlQUFlLENBQUNzQixNQUFNO1lBQ3JDO1lBR0FYLFFBQVFDLEdBQUcsQ0FBQyxxQkFBV0ksU0FBU08sR0FBRztZQUNuQ1osUUFBUWEsUUFBUTtZQUVoQixJQUFJLENBQUNSLFNBQVNTLEVBQUUsRUFBRTtnQkFDaEIsTUFBTUMsWUFBWSxNQUFNVixTQUFTVyxJQUFJO2dCQUNyQ2hCLFFBQVFpQixLQUFLLENBQUM7Z0JBQ2RqQixRQUFRa0IsS0FBSyxDQUFDLFdBQVdiLFNBQVNjLE1BQU07Z0JBQ3hDbkIsUUFBUWtCLEtBQUssQ0FBQyxnQkFBZ0JiLFNBQVNlLFVBQVU7Z0JBQ2pEcEIsUUFBUWtCLEtBQUssQ0FBQyxlQUFlSDtnQkFDN0JmLFFBQVFhLFFBQVE7Z0JBQ2hCLE1BQU0sSUFBSVEsTUFBTU4sVUFBVUcsS0FBSyxJQUFJLFFBQTRCYixPQUFwQkEsU0FBU2MsTUFBTSxFQUFDLE1BQXdCLE9BQXBCZCxTQUFTZSxVQUFVO1lBQ3BGO1lBRUEsSUFBSSxDQUFDZixTQUFTSyxJQUFJLEVBQUU7Z0JBQ2xCLE1BQU0sSUFBSVcsTUFBTTtZQUNsQjtZQUVBLG1CQUFtQjtZQUNuQixNQUFNQyxTQUFTakIsU0FBU0ssSUFBSSxDQUFDYSxTQUFTO1lBQ3RDLE1BQU1DLFVBQVUsSUFBSUM7WUFDcEIsSUFBSUMsZUFBZTtZQUVuQixtQkFBbUI7WUFFbkIsSUFBSTtnQkFDRixNQUFPLEtBQU07b0JBQ1gsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1OLE9BQU9PLElBQUk7b0JBRXpDLElBQUlGLE1BQU07d0JBQ1I7b0JBQ0Y7b0JBRUEsTUFBTUcsUUFBUU4sUUFBUU8sTUFBTSxDQUFDSCxPQUFPO3dCQUFFSSxRQUFRO29CQUFLO29CQUVuRE4sZ0JBQWdCSTtvQkFFaEIsa0NBQWtDO29CQUNsQzVDLFFBQVE0QztnQkFDVjtnQkFFQSwrQkFBK0I7Z0JBQy9CM0MsV0FBV3VDO1lBRWIsU0FBVTtnQkFDUkosT0FBT1csV0FBVztZQUNwQjtRQUVGLEVBQUUsT0FBT2YsT0FBTztZQUNkLElBQUlBLGlCQUFpQkcsT0FBTztnQkFDMUIsSUFBSUgsTUFBTWdCLElBQUksS0FBSyxjQUFjO29CQUMvQjtnQkFDRjtnQkFDQTlDLFFBQVE4QixNQUFNeEIsT0FBTztZQUN2QixPQUFPO2dCQUNMTixRQUFRO1lBQ1Y7UUFDRixTQUFVO1lBQ1IsSUFBSSxDQUFDQyxlQUFlLEdBQUc7UUFDekI7SUFDRjtJQUVBOztHQUVDLEdBQ0Q4QyxnQkFBc0I7UUFDcEIsSUFBSSxJQUFJLENBQUM5QyxlQUFlLEVBQUU7WUFDeEIsSUFBSSxDQUFDQSxlQUFlLENBQUMrQyxLQUFLO1lBQzFCLElBQUksQ0FBQy9DLGVBQWUsR0FBRztRQUN6QjtJQUNGO0lBRUE7O0dBRUMsR0FDRGdELHNCQUErQjtRQUM3QixPQUFPLElBQUksQ0FBQ2hELGVBQWUsS0FBSztJQUNsQztJQUVBOztHQUVDLEdBQ0QsTUFBTWlELGlCQUFpQjlDLFFBQWdCLEVBQUVDLE1BQWMsRUFBMEI7UUFDL0UsSUFBSTtZQUNGLE1BQU1ZLFdBQVcsTUFBTUMsTUFBTSxhQUF5QmIsT0FBWkQsVUFBUyxLQUFVLE9BQVBDLFNBQVU7Z0JBQzlEZSxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7WUFDRjtZQUVBLElBQUksQ0FBQ0osU0FBU1MsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlPLE1BQU0sMEJBQThDLE9BQXBCaEIsU0FBU2UsVUFBVTtZQUMvRDtZQUVBLE1BQU1tQixXQUFXLE1BQU1sQyxTQUFTVyxJQUFJO1lBQ3BDLE9BQU91QixTQUFTQyxRQUFRLElBQUksRUFBRTtRQUVoQyxFQUFFLE9BQU90QixPQUFPO1lBQ2RsQixRQUFRa0IsS0FBSyxDQUFDLHVDQUF1Q0E7WUFDckQsT0FBTyxFQUFFO1FBQ1g7SUFDRjtJQUVBOzs7R0FHQyxHQUNELE1BQU11Qix3QkFBd0JoRCxNQUFjLEVBQTBCO1FBQ3BFLElBQUk7WUFDRixzREFBc0Q7WUFDdEQsTUFBTWlELFVBQVUsTUFBTTVELHFFQUFtQkEsQ0FBQzZELGtCQUFrQixDQUFDbEQ7WUFDN0QsSUFBSWlELFdBQVdBLFFBQVFFLFdBQVcsQ0FBQ3hDLE1BQU0sR0FBRyxHQUFHO2dCQUM3QywyREFBMkQ7Z0JBQzNELE9BQU8sSUFBSSxDQUFDeUMsaUJBQWlCLENBQUNILFFBQVFFLFdBQVc7WUFDbkQ7WUFFQSxtREFBbUQ7WUFDbkQsa0VBQWtFO1lBQ2xFLE9BQU8sRUFBRTtRQUNYLEVBQUUsT0FBTzFCLE9BQU87WUFDZGxCLFFBQVE4QyxJQUFJLENBQUMsMkVBQWtFNUI7WUFDL0UsT0FBTyxFQUFFO1FBQ1g7SUFDRjtJQUVBOztHQUVDLEdBQ0QyQixrQkFBa0JMLFFBQWUsRUFBaUI7UUFDaEQsT0FBT0EsU0FBU08sR0FBRyxDQUFDQyxDQUFBQSxNQUFRO2dCQUMxQkMsSUFBSUQsSUFBSUMsRUFBRTtnQkFDVkMsU0FBU0YsSUFBSUUsT0FBTztnQkFDcEJDLE1BQU1ILElBQUlJLE1BQU0sS0FBSyxTQUFTLFNBQVM7Z0JBQ3ZDQyxXQUFXTCxJQUFJSyxTQUFTO2dCQUN4QkMsWUFBWU4sSUFBSU0sVUFBVSxJQUFJO2dCQUM5QjFELGFBQWFvRCxJQUFJcEQsV0FBVyxJQUFJLEVBQUU7WUFDcEM7SUFDRjtJQUVBOztHQUVDLEdBQ0QyRCxvQkFBb0JmLFFBQXVCLEVBQVM7UUFDbEQsT0FBT0EsU0FBU08sR0FBRyxDQUFDQyxDQUFBQSxNQUFRO2dCQUMxQkMsSUFBSUQsSUFBSUMsRUFBRTtnQkFDVkMsU0FBU0YsSUFBSUUsT0FBTztnQkFDcEJFLFFBQVFKLElBQUlHLElBQUksS0FBSyxTQUFTLFNBQVM7Z0JBQ3ZDRSxXQUFXTCxJQUFJSyxTQUFTO2dCQUN4QkMsWUFBWU4sSUFBSU0sVUFBVSxJQUFJO2dCQUM5QjFELGFBQWFvRCxJQUFJcEQsV0FBVyxJQUFJLEVBQUU7WUFDcEM7SUFDRjtJQUVBOztHQUVDLEdBQ0Q0RCxvQkFBNEI7UUFDMUIsT0FBT0MsS0FBS0MsR0FBRyxHQUFHQyxRQUFRLENBQUMsTUFBTUMsS0FBS0MsTUFBTSxHQUFHRixRQUFRLENBQUMsSUFBSUcsU0FBUyxDQUFDO0lBQ3hFO0lBRUE7O0dBRUMsR0FDRCxlQUF1QjdFLE1BQXVCLEVBQVE7WUFDL0NBLGtCQUdBQSxnQkFHQUE7UUFOTCxJQUFJLEdBQUNBLG1CQUFBQSxPQUFPTyxRQUFRLGNBQWZQLHVDQUFBQSxpQkFBaUIrRSxJQUFJLEtBQUk7WUFDNUIsTUFBTSxJQUFJM0MsTUFBTTtRQUNsQjtRQUNBLElBQUksR0FBQ3BDLGlCQUFBQSxPQUFPUSxNQUFNLGNBQWJSLHFDQUFBQSxlQUFlK0UsSUFBSSxLQUFJO1lBQzFCLE1BQU0sSUFBSTNDLE1BQU07UUFDbEI7UUFDQSxJQUFJLEdBQUNwQyxrQkFBQUEsT0FBT1MsT0FBTyxjQUFkVCxzQ0FBQUEsZ0JBQWdCK0UsSUFBSSxLQUFJO1lBQzNCLE1BQU0sSUFBSTNDLE1BQU07UUFDbEI7SUFDQSwrQ0FBK0M7SUFDakQ7SUFFQTs7R0FFQyxHQUNELE1BQU00QyxnQkFDSmhGLE1BQXVCLEVBQ3ZCQyxPQUFnQyxFQUNoQ0MsVUFBMEMsRUFDMUNDLE9BQWdDLEVBQ2pCO1FBQ2YsSUFBSTtZQUNGLElBQUksQ0FBQzJFLGNBQWMsQ0FBQzlFO1lBQ3BCLE1BQU0sSUFBSSxDQUFDRCxXQUFXLENBQUNDLFFBQVFDLFNBQVNDLFlBQVlDO1FBQ3RELEVBQUUsT0FBTzhCLE9BQU87WUFDZCxJQUFJQSxpQkFBaUJHLE9BQU87Z0JBQzFCakMsUUFBUThCLE1BQU14QixPQUFPO1lBQ3ZCLE9BQU87Z0JBQ0xOLFFBQVE7WUFDVjtRQUNGO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU04RSxjQUFjMUUsUUFBZ0IsRUFBRUMsTUFBYyxFQUFFMEUsU0FBaUIsRUFBb0I7UUFDekYsSUFBSTtZQUNGLE1BQU05RCxXQUFXLE1BQU1DLE1BQU0sYUFBeUJiLE9BQVpELFVBQVMsS0FBcUIyRSxPQUFsQjFFLFFBQU8sYUFBcUIsT0FBVjBFLFlBQWE7Z0JBQ25GM0QsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7WUFFQSxJQUFJLENBQUNKLFNBQVNTLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJTyxNQUFNLDZCQUFpRCxPQUFwQmhCLFNBQVNlLFVBQVU7WUFDbEU7WUFFQSxPQUFPO1FBQ1QsRUFBRSxPQUFPRixPQUFPO1lBQ2RsQixRQUFRa0IsS0FBSyxDQUFDLDZCQUE2QkE7WUFDM0MsT0FBTztRQUNUO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1rRCxjQUFjNUUsUUFBZ0IsRUFBRUMsTUFBYyxFQUFFMEUsU0FBaUIsRUFBRUUsVUFBa0IsRUFBb0I7UUFDN0csSUFBSTtZQUNGLE1BQU1oRSxXQUFXLE1BQU1DLE1BQU0sYUFBeUJiLE9BQVpELFVBQVMsS0FBcUIyRSxPQUFsQjFFLFFBQU8sYUFBcUIsT0FBVjBFLFlBQWE7Z0JBQ25GM0QsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNUixLQUFLQyxTQUFTLENBQUM7b0JBQUUrQyxTQUFTbUI7Z0JBQVc7WUFDN0M7WUFFQSxJQUFJLENBQUNoRSxTQUFTUyxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSU8sTUFBTSwrQkFBbUQsT0FBcEJoQixTQUFTZSxVQUFVO1lBQ3BFO1lBRUEsT0FBTztRQUNULEVBQUUsT0FBT0YsT0FBTztZQUNkbEIsUUFBUWtCLEtBQUssQ0FBQywrQkFBK0JBO1lBQzdDLE9BQU87UUFDVDtJQUNGOzthQXJTaUJYLGNBQWM7YUFDdkJsQixrQkFBMEM7O0FBcVNwRDtBQUVBLCtCQUErQjtBQUN4QixNQUFNaUYsWUFBWSxJQUFJdkYsWUFBWTtBQUN6QywrREFBZXVGLFNBQVNBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2xpYi9zZXJ2aWNlcy9haVNlcnZpY2UudHM/NjU4ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBTZXJ2acOnbyBwYXJhIGludGVncmHDp8OjbyBjb20gRmlyZWJhc2UgRnVuY3Rpb25zIGRlIElBXG5pbXBvcnQgeyBjaGF0U2Vzc2lvbnNTZXJ2aWNlIH0gZnJvbSAnLi9jaGF0U2Vzc2lvbnNTZXJ2aWNlJztcblxuZXhwb3J0IGludGVyZmFjZSBDaGF0TWVzc2FnZSB7XG4gIGlkOiBzdHJpbmc7XG4gIGNvbnRlbnQ6IHN0cmluZztcbiAgcm9sZTogJ3VzZXInIHwgJ2Fzc2lzdGFudCc7XG4gIHRpbWVzdGFtcDogc3RyaW5nO1xuICBpc0Zhdm9yaXRlPzogYm9vbGVhbjtcbiAgYXR0YWNobWVudHM/OiBpbXBvcnQoJ0AvbGliL3R5cGVzL2NoYXQnKS5BdHRhY2htZW50TWV0YWRhdGFbXTtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBTdHJlYW1SZXNwb25zZSB7XG4gIGNvbnRlbnQ6IHN0cmluZztcbiAgaXNDb21wbGV0ZTogYm9vbGVhbjtcbiAgZXJyb3I/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQUlTZXJ2aWNlQ29uZmlnIHtcbiAgdXNlcm5hbWU6IHN0cmluZztcbiAgY2hhdElkOiBzdHJpbmc7XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgbW9kZWw/OiBzdHJpbmc7XG4gIGF0dGFjaG1lbnRzPzogaW1wb3J0KCdAL2xpYi90eXBlcy9jaGF0JykuQXR0YWNobWVudE1ldGFkYXRhW107XG4gIGlzUmVnZW5lcmF0aW9uPzogYm9vbGVhbjtcbiAgd2ViU2VhcmNoRW5hYmxlZD86IGJvb2xlYW47XG4gIHVzZXJNZXNzYWdlSWQ/OiBzdHJpbmc7XG59XG5cbmNsYXNzIEFJU2VydmljZSB7XG4gIHByaXZhdGUgcmVhZG9ubHkgZnVuY3Rpb25VcmwgPSAnaHR0cHM6Ly91cy1jZW50cmFsMS1yYWZ0aG9yLTAwMDEuY2xvdWRmdW5jdGlvbnMubmV0L2NoYXRXaXRoQUknO1xuICBwcml2YXRlIGFib3J0Q29udHJvbGxlcjogQWJvcnRDb250cm9sbGVyIHwgbnVsbCA9IG51bGw7XG5cbiAgLyoqXG4gICAqIEVudmlhIG1lbnNhZ2VtIHBhcmEgYSBJQSBjb20gc3RyZWFtaW5nXG4gICAqL1xuICBhc3luYyBzZW5kTWVzc2FnZShcbiAgICBjb25maWc6IEFJU2VydmljZUNvbmZpZyxcbiAgICBvbkNodW5rOiAoY2h1bms6IHN0cmluZykgPT4gdm9pZCxcbiAgICBvbkNvbXBsZXRlOiAoZnVsbFJlc3BvbnNlOiBzdHJpbmcpID0+IHZvaWQsXG4gICAgb25FcnJvcjogKGVycm9yOiBzdHJpbmcpID0+IHZvaWRcbiAgKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIENyaWFyIG5vdm8gQWJvcnRDb250cm9sbGVyIHBhcmEgZXN0YSByZXF1aXNpw6fDo29cbiAgICAgIHRoaXMuYWJvcnRDb250cm9sbGVyID0gbmV3IEFib3J0Q29udHJvbGxlcigpO1xuXG4gICAgICAvLyBQcmVwYXJhciBkYWRvcyBkYSByZXF1aXNpw6fDo29cbiAgICAgIGNvbnN0IHJlcXVlc3REYXRhID0ge1xuICAgICAgICB1c2VybmFtZTogY29uZmlnLnVzZXJuYW1lLFxuICAgICAgICBjaGF0SWQ6IGNvbmZpZy5jaGF0SWQsXG4gICAgICAgIG1lc3NhZ2U6IGNvbmZpZy5tZXNzYWdlLFxuICAgICAgICBtb2RlbDogY29uZmlnLm1vZGVsLFxuICAgICAgICBhdHRhY2htZW50czogY29uZmlnLmF0dGFjaG1lbnRzIHx8IFtdLFxuICAgICAgICBpc1JlZ2VuZXJhdGlvbjogY29uZmlnLmlzUmVnZW5lcmF0aW9uIHx8IGZhbHNlLFxuICAgICAgICB3ZWJTZWFyY2hFbmFibGVkOiBjb25maWcud2ViU2VhcmNoRW5hYmxlZCB8fCBmYWxzZSxcbiAgICAgICAgdXNlck1lc3NhZ2VJZDogY29uZmlnLnVzZXJNZXNzYWdlSWQsXG4gICAgICB9O1xuXG4gICAgICBjb25zb2xlLmxvZygnPT09IERFQlVHOiBBSSBTRVJWSUNFIFJFUVVFU1QgREFUQSA9PT0nKTtcbiAgICAgIGNvbnNvbGUubG9nKCdSZXF1ZXN0IGRhdGE6JywgSlNPTi5zdHJpbmdpZnkocmVxdWVzdERhdGEsIG51bGwsIDIpKTtcbiAgICAgIGNvbnNvbGUubG9nKCdBdHRhY2htZW50cyBsZW5ndGg6JywgcmVxdWVzdERhdGEuYXR0YWNobWVudHMubGVuZ3RoKTtcbiAgICAgIGlmIChyZXF1ZXN0RGF0YS5hdHRhY2htZW50cy5sZW5ndGggPiAwKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdGaXJzdCBhdHRhY2htZW50OicsIEpTT04uc3RyaW5naWZ5KHJlcXVlc3REYXRhLmF0dGFjaG1lbnRzWzBdLCBudWxsLCAyKSk7XG4gICAgICB9XG5cbiAgICAgIC8vIEVudmlhciByZXF1aXNpw6fDo29cblxuXG5cblxuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHRoaXMuZnVuY3Rpb25VcmwsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShyZXF1ZXN0RGF0YSksXG4gICAgICAgIHNpZ25hbDogdGhpcy5hYm9ydENvbnRyb2xsZXIuc2lnbmFsLFxuICAgICAgfSk7XG5cblxuICAgICAgY29uc29sZS5sb2coJ/CfjJAgVVJMOicsIHJlc3BvbnNlLnVybCk7XG4gICAgICBjb25zb2xlLmdyb3VwRW5kKCk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBjb25zb2xlLmdyb3VwKCfinYwgQUkgU0VSVklDRSAtIEVSUk8gTkEgUkVTUE9TVEEnKTtcbiAgICAgICAgY29uc29sZS5lcnJvcignU3RhdHVzOicsIHJlc3BvbnNlLnN0YXR1cyk7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1N0YXR1cyBUZXh0OicsIHJlc3BvbnNlLnN0YXR1c1RleHQpO1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBEYXRhOicsIGVycm9yRGF0YSk7XG4gICAgICAgIGNvbnNvbGUuZ3JvdXBFbmQoKTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5lcnJvciB8fCBgSFRUUCAke3Jlc3BvbnNlLnN0YXR1c306ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcbiAgICAgIH1cblxuICAgICAgaWYgKCFyZXNwb25zZS5ib2R5KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignUmVzcG9uc2UgYm9keSBpcyBub3QgYXZhaWxhYmxlJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIFByb2Nlc3NhciBzdHJlYW1cbiAgICAgIGNvbnN0IHJlYWRlciA9IHJlc3BvbnNlLmJvZHkuZ2V0UmVhZGVyKCk7XG4gICAgICBjb25zdCBkZWNvZGVyID0gbmV3IFRleHREZWNvZGVyKCk7XG4gICAgICBsZXQgZnVsbFJlc3BvbnNlID0gJyc7XG5cbiAgICAgIC8vIFByb2Nlc3NhciBzdHJlYW1cblxuICAgICAgdHJ5IHtcbiAgICAgICAgd2hpbGUgKHRydWUpIHtcbiAgICAgICAgICBjb25zdCB7IGRvbmUsIHZhbHVlIH0gPSBhd2FpdCByZWFkZXIucmVhZCgpO1xuXG4gICAgICAgICAgaWYgKGRvbmUpIHtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGNvbnN0IGNodW5rID0gZGVjb2Rlci5kZWNvZGUodmFsdWUsIHsgc3RyZWFtOiB0cnVlIH0pO1xuXG4gICAgICAgICAgZnVsbFJlc3BvbnNlICs9IGNodW5rO1xuXG4gICAgICAgICAgLy8gQ2hhbWFyIGNhbGxiYWNrIHBhcmEgY2FkYSBjaHVua1xuICAgICAgICAgIG9uQ2h1bmsoY2h1bmspO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gQ2hhbWFyIGNhbGxiYWNrIGRlIGNvbmNsdXPDo29cbiAgICAgICAgb25Db21wbGV0ZShmdWxsUmVzcG9uc2UpO1xuXG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICByZWFkZXIucmVsZWFzZUxvY2soKTtcbiAgICAgIH1cblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgICBpZiAoZXJyb3IubmFtZSA9PT0gJ0Fib3J0RXJyb3InKSB7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIG9uRXJyb3IoZXJyb3IubWVzc2FnZSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBvbkVycm9yKCdFcnJvIGRlc2NvbmhlY2lkbyBuYSBjb211bmljYcOnw6NvIGNvbSBhIElBJyk7XG4gICAgICB9XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHRoaXMuYWJvcnRDb250cm9sbGVyID0gbnVsbDtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQ2FuY2VsYSBhIHJlcXVpc2nDp8OjbyBlbSBhbmRhbWVudG9cbiAgICovXG4gIGNhbmNlbFJlcXVlc3QoKTogdm9pZCB7XG4gICAgaWYgKHRoaXMuYWJvcnRDb250cm9sbGVyKSB7XG4gICAgICB0aGlzLmFib3J0Q29udHJvbGxlci5hYm9ydCgpO1xuICAgICAgdGhpcy5hYm9ydENvbnRyb2xsZXIgPSBudWxsO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBWZXJpZmljYSBzZSBow6EgdW1hIHJlcXVpc2nDp8OjbyBlbSBhbmRhbWVudG9cbiAgICovXG4gIGlzUmVxdWVzdEluUHJvZ3Jlc3MoKTogYm9vbGVhbiB7XG4gICAgcmV0dXJuIHRoaXMuYWJvcnRDb250cm9sbGVyICE9PSBudWxsO1xuICB9XG5cbiAgLyoqXG4gICAqIENhcnJlZ2EgbWVuc2FnZW5zIGRlIHVtIGNoYXQgdXNhbmRvIGEgQVBJIHJvdXRlXG4gICAqL1xuICBhc3luYyBsb2FkQ2hhdE1lc3NhZ2VzKHVzZXJuYW1lOiBzdHJpbmcsIGNoYXRJZDogc3RyaW5nKTogUHJvbWlzZTxDaGF0TWVzc2FnZVtdPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvY2hhdC8ke3VzZXJuYW1lfS8ke2NoYXRJZH1gLCB7XG4gICAgICAgIG1ldGhvZDogJ0dFVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBFcnJvIGFvIGNhcnJlZ2FyIGNoYXQ6ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgY2hhdERhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICByZXR1cm4gY2hhdERhdGEubWVzc2FnZXMgfHwgW107XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBjYXJyZWdhciBtZW5zYWdlbnMgZG8gY2hhdDonLCBlcnJvcik7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIE9idMOpbSBjb250ZXh0byBjb21wbGV0byBkbyBjaGF0IHBhcmEgZW52aW8gw6AgSUFcbiAgICogSU1QT1JUQU5URTogU2VtcHJlIHVzYSBUT0RBUyBhcyBtZW5zYWdlbnMsIGluZGVwZW5kZW50ZSBkYXMgc2Vzc8O1ZXNcbiAgICovXG4gIGFzeW5jIGdldEZ1bGxDaGF0Q29udGV4dEZvckFJKGNoYXRJZDogc3RyaW5nKTogUHJvbWlzZTxDaGF0TWVzc2FnZVtdPiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIFRlbnRhIG9idGVyIGNvbnRleHRvIGRvIHNpc3RlbWEgZGUgc2Vzc8O1ZXMgcHJpbWVpcm9cbiAgICAgIGNvbnN0IGNvbnRleHQgPSBhd2FpdCBjaGF0U2Vzc2lvbnNTZXJ2aWNlLmdldEZ1bGxDaGF0Q29udGV4dChjaGF0SWQpO1xuICAgICAgaWYgKGNvbnRleHQgJiYgY29udGV4dC5hbGxNZXNzYWdlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIC8vIENvbnZlcnRlIG1lbnNhZ2VucyBkbyBmb3JtYXRvIGludGVybm8gcGFyYSBmb3JtYXRvIGRhIElBXG4gICAgICAgIHJldHVybiB0aGlzLmNvbnZlcnRUb0FJRm9ybWF0KGNvbnRleHQuYWxsTWVzc2FnZXMpO1xuICAgICAgfVxuXG4gICAgICAvLyBGYWxsYmFjazogc2UgbsOjbyBow6Egc2Vzc8O1ZXMsIHJldG9ybmEgYXJyYXkgdmF6aW9cbiAgICAgIC8vIE8gY29udGV4dG8gc2Vyw6EgY29uc3RydcOtZG8gbm9ybWFsbWVudGUgcGVsbyBjYXJyZWdhbWVudG8gZGlyZXRvXG4gICAgICByZXR1cm4gW107XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUud2FybignU2lzdGVtYSBkZSBzZXNzw7VlcyBuw6NvIGRpc3BvbsOtdmVsLCB1c2FuZG8gY2FycmVnYW1lbnRvIGRpcmV0bzonLCBlcnJvcik7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIENvbnZlcnRlIG1lbnNhZ2VucyBkbyBmb3JtYXRvIGludGVybm8gcGFyYSBvIGZvcm1hdG8gZGEgSUFcbiAgICovXG4gIGNvbnZlcnRUb0FJRm9ybWF0KG1lc3NhZ2VzOiBhbnlbXSk6IENoYXRNZXNzYWdlW10ge1xuICAgIHJldHVybiBtZXNzYWdlcy5tYXAobXNnID0+ICh7XG4gICAgICBpZDogbXNnLmlkLFxuICAgICAgY29udGVudDogbXNnLmNvbnRlbnQsXG4gICAgICByb2xlOiBtc2cuc2VuZGVyID09PSAndXNlcicgPyAndXNlcicgOiAnYXNzaXN0YW50JyxcbiAgICAgIHRpbWVzdGFtcDogbXNnLnRpbWVzdGFtcCxcbiAgICAgIGlzRmF2b3JpdGU6IG1zZy5pc0Zhdm9yaXRlIHx8IGZhbHNlLFxuICAgICAgYXR0YWNobWVudHM6IG1zZy5hdHRhY2htZW50cyB8fCBbXSwgLy8g4pyFIElOQ0xVSVIgQU5FWE9TIVxuICAgIH0pKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBDb252ZXJ0ZSBtZW5zYWdlbnMgZG8gZm9ybWF0byBkYSBJQSBwYXJhIG8gZm9ybWF0byBpbnRlcm5vXG4gICAqL1xuICBjb252ZXJ0RnJvbUFJRm9ybWF0KG1lc3NhZ2VzOiBDaGF0TWVzc2FnZVtdKTogYW55W10ge1xuICAgIHJldHVybiBtZXNzYWdlcy5tYXAobXNnID0+ICh7XG4gICAgICBpZDogbXNnLmlkLFxuICAgICAgY29udGVudDogbXNnLmNvbnRlbnQsXG4gICAgICBzZW5kZXI6IG1zZy5yb2xlID09PSAndXNlcicgPyAndXNlcicgOiAnYWknLFxuICAgICAgdGltZXN0YW1wOiBtc2cudGltZXN0YW1wLFxuICAgICAgaXNGYXZvcml0ZTogbXNnLmlzRmF2b3JpdGUgfHwgZmFsc2UsXG4gICAgICBhdHRhY2htZW50czogbXNnLmF0dGFjaG1lbnRzIHx8IFtdLCAvLyDinIUgSU5DTFVJUiBBTkVYT1MhXG4gICAgfSkpO1xuICB9XG5cbiAgLyoqXG4gICAqIEdlcmEgSUQgw7puaWNvIHBhcmEgbWVuc2FnZW5zXG4gICAqL1xuICBnZW5lcmF0ZU1lc3NhZ2VJZCgpOiBzdHJpbmcge1xuICAgIHJldHVybiBEYXRlLm5vdygpLnRvU3RyaW5nKDM2KSArIE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBWYWxpZGEgY29uZmlndXJhw6fDo28gYW50ZXMgZGUgZW52aWFyXG4gICAqL1xuICBwcml2YXRlIHZhbGlkYXRlQ29uZmlnKGNvbmZpZzogQUlTZXJ2aWNlQ29uZmlnKTogdm9pZCB7XG4gICAgaWYgKCFjb25maWcudXNlcm5hbWU/LnRyaW0oKSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdVc2VybmFtZSDDqSBvYnJpZ2F0w7NyaW8nKTtcbiAgICB9XG4gICAgaWYgKCFjb25maWcuY2hhdElkPy50cmltKCkpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignQ2hhdCBJRCDDqSBvYnJpZ2F0w7NyaW8nKTtcbiAgICB9XG4gICAgaWYgKCFjb25maWcubWVzc2FnZT8udHJpbSgpKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ01lbnNhZ2VtIMOpIG9icmlnYXTDs3JpYScpO1xuICAgIH1cbiAgICAvLyBSZW1vdmlkbyBsaW1pdGUgZGUgY2FyYWN0ZXJlcyBwYXJhIG1lbnNhZ2Vuc1xuICB9XG5cbiAgLyoqXG4gICAqIEVudmlhIG1lbnNhZ2VtIGNvbSB2YWxpZGHDp8Ojb1xuICAgKi9cbiAgYXN5bmMgc2VuZE1lc3NhZ2VTYWZlKFxuICAgIGNvbmZpZzogQUlTZXJ2aWNlQ29uZmlnLFxuICAgIG9uQ2h1bms6IChjaHVuazogc3RyaW5nKSA9PiB2b2lkLFxuICAgIG9uQ29tcGxldGU6IChmdWxsUmVzcG9uc2U6IHN0cmluZykgPT4gdm9pZCxcbiAgICBvbkVycm9yOiAoZXJyb3I6IHN0cmluZykgPT4gdm9pZFxuICApOiBQcm9taXNlPHZvaWQ+IHtcbiAgICB0cnkge1xuICAgICAgdGhpcy52YWxpZGF0ZUNvbmZpZyhjb25maWcpO1xuICAgICAgYXdhaXQgdGhpcy5zZW5kTWVzc2FnZShjb25maWcsIG9uQ2h1bmssIG9uQ29tcGxldGUsIG9uRXJyb3IpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgICBvbkVycm9yKGVycm9yLm1lc3NhZ2UpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgb25FcnJvcignRXJybyBkZSB2YWxpZGHDp8OjbycpO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBEZWxldGEgdW1hIG1lbnNhZ2VtIGRvIGNoYXQgbm8gRmlyZWJhc2UgU3RvcmFnZVxuICAgKi9cbiAgYXN5bmMgZGVsZXRlTWVzc2FnZSh1c2VybmFtZTogc3RyaW5nLCBjaGF0SWQ6IHN0cmluZywgbWVzc2FnZUlkOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9jaGF0LyR7dXNlcm5hbWV9LyR7Y2hhdElkfS9tZXNzYWdlLyR7bWVzc2FnZUlkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEVycm8gYW8gZGVsZXRhciBtZW5zYWdlbTogJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBkZWxldGFyIG1lbnNhZ2VtOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQXR1YWxpemEgdW1hIG1lbnNhZ2VtIG5vIGNoYXQgbm8gRmlyZWJhc2UgU3RvcmFnZVxuICAgKi9cbiAgYXN5bmMgdXBkYXRlTWVzc2FnZSh1c2VybmFtZTogc3RyaW5nLCBjaGF0SWQ6IHN0cmluZywgbWVzc2FnZUlkOiBzdHJpbmcsIG5ld0NvbnRlbnQ6IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL2NoYXQvJHt1c2VybmFtZX0vJHtjaGF0SWR9L21lc3NhZ2UvJHttZXNzYWdlSWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdQVVQnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBjb250ZW50OiBuZXdDb250ZW50IH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBFcnJvIGFvIGF0dWFsaXphciBtZW5zYWdlbTogJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBhdHVhbGl6YXIgbWVuc2FnZW06JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfVxufVxuXG4vLyBFeHBvcnRhciBpbnN0w6JuY2lhIHNpbmdsZXRvblxuZXhwb3J0IGNvbnN0IGFpU2VydmljZSA9IG5ldyBBSVNlcnZpY2UoKTtcbmV4cG9ydCBkZWZhdWx0IGFpU2VydmljZTtcbiJdLCJuYW1lcyI6WyJjaGF0U2Vzc2lvbnNTZXJ2aWNlIiwiQUlTZXJ2aWNlIiwic2VuZE1lc3NhZ2UiLCJjb25maWciLCJvbkNodW5rIiwib25Db21wbGV0ZSIsIm9uRXJyb3IiLCJhYm9ydENvbnRyb2xsZXIiLCJBYm9ydENvbnRyb2xsZXIiLCJyZXF1ZXN0RGF0YSIsInVzZXJuYW1lIiwiY2hhdElkIiwibWVzc2FnZSIsIm1vZGVsIiwiYXR0YWNobWVudHMiLCJpc1JlZ2VuZXJhdGlvbiIsIndlYlNlYXJjaEVuYWJsZWQiLCJ1c2VyTWVzc2FnZUlkIiwiY29uc29sZSIsImxvZyIsIkpTT04iLCJzdHJpbmdpZnkiLCJsZW5ndGgiLCJyZXNwb25zZSIsImZldGNoIiwiZnVuY3Rpb25VcmwiLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsInNpZ25hbCIsInVybCIsImdyb3VwRW5kIiwib2siLCJlcnJvckRhdGEiLCJqc29uIiwiZ3JvdXAiLCJlcnJvciIsInN0YXR1cyIsInN0YXR1c1RleHQiLCJFcnJvciIsInJlYWRlciIsImdldFJlYWRlciIsImRlY29kZXIiLCJUZXh0RGVjb2RlciIsImZ1bGxSZXNwb25zZSIsImRvbmUiLCJ2YWx1ZSIsInJlYWQiLCJjaHVuayIsImRlY29kZSIsInN0cmVhbSIsInJlbGVhc2VMb2NrIiwibmFtZSIsImNhbmNlbFJlcXVlc3QiLCJhYm9ydCIsImlzUmVxdWVzdEluUHJvZ3Jlc3MiLCJsb2FkQ2hhdE1lc3NhZ2VzIiwiY2hhdERhdGEiLCJtZXNzYWdlcyIsImdldEZ1bGxDaGF0Q29udGV4dEZvckFJIiwiY29udGV4dCIsImdldEZ1bGxDaGF0Q29udGV4dCIsImFsbE1lc3NhZ2VzIiwiY29udmVydFRvQUlGb3JtYXQiLCJ3YXJuIiwibWFwIiwibXNnIiwiaWQiLCJjb250ZW50Iiwicm9sZSIsInNlbmRlciIsInRpbWVzdGFtcCIsImlzRmF2b3JpdGUiLCJjb252ZXJ0RnJvbUFJRm9ybWF0IiwiZ2VuZXJhdGVNZXNzYWdlSWQiLCJEYXRlIiwibm93IiwidG9TdHJpbmciLCJNYXRoIiwicmFuZG9tIiwic3Vic3RyaW5nIiwidmFsaWRhdGVDb25maWciLCJ0cmltIiwic2VuZE1lc3NhZ2VTYWZlIiwiZGVsZXRlTWVzc2FnZSIsIm1lc3NhZ2VJZCIsInVwZGF0ZU1lc3NhZ2UiLCJuZXdDb250ZW50IiwiYWlTZXJ2aWNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/aiService.ts\n"));

/***/ })

});