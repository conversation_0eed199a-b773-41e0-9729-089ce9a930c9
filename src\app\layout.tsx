import type { <PERSON>ada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { AuthProvider } from '@/contexts/AuthContext'
import { AppearanceProvider } from '@/contexts/AppearanceContext'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Rafthor - AI Chatbot Platform',
  description: 'Uma plataforma de chatbot com múltiplas IAs',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="pt-BR">
      <body className={inter.className}>
        <AuthProvider>
          <AppearanceProvider>
            {children}
          </AppearanceProvider>
        </AuthProvider>
      </body>
    </html>
  )
}
