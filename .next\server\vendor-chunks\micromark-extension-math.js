"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-math";
exports.ids = ["vendor-chunks/micromark-extension-math"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-math/dev/lib/math-flow.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark-extension-math/dev/lib/math-flow.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mathFlow: () => (/* binding */ mathFlow)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {Construct, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst mathFlow = {\n  tokenize: tokenizeMathFenced,\n  concrete: true,\n  name: 'mathFlow'\n}\n\n/** @type {Construct} */\nconst nonLazyContinuation = {\n  tokenize: tokenizeNonLazyContinuation,\n  partial: true\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeMathFenced(effects, ok, nok) {\n  const self = this\n  const tail = self.events[self.events.length - 1]\n  const initialSize =\n    tail && tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix\n      ? tail[2].sliceSerialize(tail[1], true).length\n      : 0\n  let sizeOpen = 0\n\n  return start\n\n  /**\n   * Start of math.\n   *\n   * ```markdown\n   * > | $$\n   *     ^\n   *   | \\frac{1}{2}\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dollarSign, 'expected `$`')\n    effects.enter('mathFlow')\n    effects.enter('mathFlowFence')\n    effects.enter('mathFlowFenceSequence')\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening fence sequence.\n   *\n   * ```markdown\n   * > | $$\n   *      ^\n   *   | \\frac{1}{2}\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dollarSign) {\n      effects.consume(code)\n      sizeOpen++\n      return sequenceOpen\n    }\n\n    if (sizeOpen < 2) {\n      return nok(code)\n    }\n\n    effects.exit('mathFlowFenceSequence')\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, metaBefore, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)\n  }\n\n  /**\n   * In opening fence, before meta.\n   *\n   * ```markdown\n   * > | $$asciimath\n   *       ^\n   *   | x < y\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n\n  function metaBefore(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      return metaAfter(code)\n    }\n\n    effects.enter('mathFlowFenceMeta')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.chunkString, {contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeString})\n    return meta(code)\n  }\n\n  /**\n   * In meta.\n   *\n   * ```markdown\n   * > | $$asciimath\n   *        ^\n   *   | x < y\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function meta(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.chunkString)\n      effects.exit('mathFlowFenceMeta')\n      return metaAfter(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dollarSign) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return meta\n  }\n\n  /**\n   * After meta.\n   *\n   * ```markdown\n   * > | $$\n   *       ^\n   *   | \\frac{1}{2}\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function metaAfter(code) {\n    // Guaranteed to be eol/eof.\n    effects.exit('mathFlowFence')\n\n    if (self.interrupt) {\n      return ok(code)\n    }\n\n    return effects.attempt(\n      nonLazyContinuation,\n      beforeNonLazyContinuation,\n      after\n    )(code)\n  }\n\n  /**\n   * After eol/eof in math, at a non-lazy closing fence or content.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *     ^\n   * > | $$\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeNonLazyContinuation(code) {\n    return effects.attempt(\n      {tokenize: tokenizeClosingFence, partial: true},\n      after,\n      contentStart\n    )(code)\n  }\n\n  /**\n   * Before math content, definitely not before a closing fence.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *     ^\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function contentStart(code) {\n    return (\n      initialSize\n        ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n            effects,\n            beforeContentChunk,\n            micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix,\n            initialSize + 1\n          )\n        : beforeContentChunk\n    )(code)\n  }\n\n  /**\n   * Before math content, after optional prefix.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *     ^\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeContentChunk(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof) {\n      return after(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      return effects.attempt(\n        nonLazyContinuation,\n        beforeNonLazyContinuation,\n        after\n      )(code)\n    }\n\n    effects.enter('mathFlowValue')\n    return contentChunk(code)\n  }\n\n  /**\n   * In math content.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *      ^\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function contentChunk(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit('mathFlowValue')\n      return beforeContentChunk(code)\n    }\n\n    effects.consume(code)\n    return contentChunk\n  }\n\n  /**\n   * After math (ha!).\n   *\n   * ```markdown\n   *   | $$\n   *   | \\frac{1}{2}\n   * > | $$\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    effects.exit('mathFlow')\n    return ok(code)\n  }\n\n  /** @type {Tokenizer} */\n  function tokenizeClosingFence(effects, ok, nok) {\n    let size = 0\n\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.parser.constructs.disable.null, 'expected `disable.null`')\n    /**\n     * Before closing fence, at optional whitespace.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *     ^\n     * ```\n     */\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n      effects,\n      beforeSequenceClose,\n      micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix,\n      self.parser.constructs.disable.null.includes('codeIndented')\n        ? undefined\n        : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize\n    )\n\n    /**\n     * In closing fence, after optional whitespace, at sequence.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function beforeSequenceClose(code) {\n      effects.enter('mathFlowFence')\n      effects.enter('mathFlowFenceSequence')\n      return sequenceClose(code)\n    }\n\n    /**\n     * In closing fence sequence.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *      ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceClose(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dollarSign) {\n        size++\n        effects.consume(code)\n        return sequenceClose\n      }\n\n      if (size < sizeOpen) {\n        return nok(code)\n      }\n\n      effects.exit('mathFlowFenceSequence')\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, afterSequenceClose, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)\n    }\n\n    /**\n     * After closing fence sequence, after optional whitespace.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *       ^\n     * ```\n     *\n     * @type {State}\n     */\n    function afterSequenceClose(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n        effects.exit('mathFlowFence')\n        return ok(code)\n      }\n\n      return nok(code)\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuation(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    if (code === null) {\n      return ok(code)\n    }\n\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected eol')\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n    return lineStart\n  }\n\n  /** @type {State} */\n  function lineStart(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-math/dev/lib/math-flow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-math/dev/lib/math-text.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark-extension-math/dev/lib/math-text.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mathText: () => (/* binding */ mathText)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/**\n * @import {Options} from 'micromark-extension-math'\n * @import {Construct, Previous, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n// To do: next major: clean spaces in HTML compiler.\n// This has to be coordinated together with `mdast-util-math`.\n\n\n\n\n\n/**\n * @param {Options | null | undefined} [options={}]\n *   Configuration (default: `{}`).\n * @returns {Construct}\n *   Construct.\n */\nfunction mathText(options) {\n  const options_ = options || {}\n  let single = options_.singleDollarTextMath\n\n  if (single === null || single === undefined) {\n    single = true\n  }\n\n  return {\n    tokenize: tokenizeMathText,\n    resolve: resolveMathText,\n    previous,\n    name: 'mathText'\n  }\n\n  /**\n   * @this {TokenizeContext}\n   * @type {Tokenizer}\n   */\n  function tokenizeMathText(effects, ok, nok) {\n    const self = this\n    let sizeOpen = 0\n    /** @type {number} */\n    let size\n    /** @type {Token} */\n    let token\n\n    return start\n\n    /**\n     * Start of math (text).\n     *\n     * ```markdown\n     * > | $a$\n     *     ^\n     * > | \\$a$\n     *      ^\n     * ```\n     *\n     * @type {State}\n     */\n    function start(code) {\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign, 'expected `$`')\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(previous.call(self, self.previous), 'expected correct previous')\n      effects.enter('mathText')\n      effects.enter('mathTextSequence')\n      return sequenceOpen(code)\n    }\n\n    /**\n     * In opening sequence.\n     *\n     * ```markdown\n     * > | $a$\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n\n    function sequenceOpen(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign) {\n        effects.consume(code)\n        sizeOpen++\n        return sequenceOpen\n      }\n\n      // Not enough markers in the sequence.\n      if (sizeOpen < 2 && !single) {\n        return nok(code)\n      }\n\n      effects.exit('mathTextSequence')\n      return between(code)\n    }\n\n    /**\n     * Between something and something else.\n     *\n     * ```markdown\n     * > | $a$\n     *      ^^\n     * ```\n     *\n     * @type {State}\n     */\n    function between(code) {\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n        return nok(code)\n      }\n\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign) {\n        token = effects.enter('mathTextSequence')\n        size = 0\n        return sequenceClose(code)\n      }\n\n      // Tabs don’t work, and virtual spaces don’t make sense.\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.space) {\n        effects.enter('space')\n        effects.consume(code)\n        effects.exit('space')\n        return between\n      }\n\n      if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n        effects.consume(code)\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding)\n        return between\n      }\n\n      // Data.\n      effects.enter('mathTextData')\n      return data(code)\n    }\n\n    /**\n     * In data.\n     *\n     * ```markdown\n     * > | $a$\n     *      ^\n     * ```\n     *\n     * @type {State}\n     */\n    function data(code) {\n      if (\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.space ||\n        code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign ||\n        (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)\n      ) {\n        effects.exit('mathTextData')\n        return between(code)\n      }\n\n      effects.consume(code)\n      return data\n    }\n\n    /**\n     * In closing sequence.\n     *\n     * ```markdown\n     * > | `a`\n     *       ^\n     * ```\n     *\n     * @type {State}\n     */\n\n    function sequenceClose(code) {\n      // More.\n      if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign) {\n        effects.consume(code)\n        size++\n        return sequenceClose\n      }\n\n      // Done!\n      if (size === sizeOpen) {\n        effects.exit('mathTextSequence')\n        effects.exit('mathText')\n        return ok(code)\n      }\n\n      // More or less accents: mark as data.\n      token.type = 'mathTextData'\n      return data(code)\n    }\n  }\n}\n\n/** @type {Resolver} */\nfunction resolveMathText(events) {\n  let tailExitIndex = events.length - 4\n  let headEnterIndex = 3\n  /** @type {number} */\n  let index\n  /** @type {number | undefined} */\n  let enter\n\n  // If we start and end with an EOL or a space.\n  if (\n    (events[headEnterIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding ||\n      events[headEnterIndex][1].type === 'space') &&\n    (events[tailExitIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding ||\n      events[tailExitIndex][1].type === 'space')\n  ) {\n    index = headEnterIndex\n\n    // And we have data.\n    while (++index < tailExitIndex) {\n      if (events[index][1].type === 'mathTextData') {\n        // Then we have padding.\n        events[tailExitIndex][1].type = 'mathTextPadding'\n        events[headEnterIndex][1].type = 'mathTextPadding'\n        headEnterIndex += 2\n        tailExitIndex -= 2\n        break\n      }\n    }\n  }\n\n  // Merge adjacent spaces and data.\n  index = headEnterIndex - 1\n  tailExitIndex++\n\n  while (++index <= tailExitIndex) {\n    if (enter === undefined) {\n      if (\n        index !== tailExitIndex &&\n        events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding\n      ) {\n        enter = index\n      }\n    } else if (\n      index === tailExitIndex ||\n      events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding\n    ) {\n      events[enter][1].type = 'mathTextData'\n\n      if (index !== enter + 2) {\n        events[enter][1].end = events[index - 1][1].end\n        events.splice(enter + 2, index - enter - 2)\n        tailExitIndex -= index - enter - 2\n        index = enter + 2\n      }\n\n      enter = undefined\n    }\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Previous}\n */\nfunction previous(code) {\n  // If there is a previous code, there will always be a tail.\n  return (\n    code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign ||\n    this.events[this.events.length - 1][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.characterEscape\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-math/dev/lib/math-text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-math/dev/lib/syntax.js":
/*!*****************************************************************!*\
  !*** ./node_modules/micromark-extension-math/dev/lib/syntax.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   math: () => (/* binding */ math)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _math_flow_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math-flow.js */ \"(ssr)/./node_modules/micromark-extension-math/dev/lib/math-flow.js\");\n/* harmony import */ var _math_text_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./math-text.js */ \"(ssr)/./node_modules/micromark-extension-math/dev/lib/math-text.js\");\n/**\n * @import {Options} from 'micromark-extension-math'\n * @import {Extension} from 'micromark-util-types'\n */\n\n\n\n\n\n/**\n * Create an extension for `micromark` to enable math syntax.\n *\n * @param {Options | null | undefined} [options={}]\n *   Configuration (default: `{}`).\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions`, to\n *   enable math syntax.\n */\nfunction math(options) {\n  return {\n    flow: {[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dollarSign]: _math_flow_js__WEBPACK_IMPORTED_MODULE_1__.mathFlow},\n    text: {[micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dollarSign]: (0,_math_text_js__WEBPACK_IMPORTED_MODULE_2__.mathText)(options)}\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1tYXRoL2Rldi9saWIvc3ludGF4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNBLFlBQVksU0FBUztBQUNyQixZQUFZLFdBQVc7QUFDdkI7O0FBRTJDO0FBQ0o7QUFDQTs7QUFFdkM7QUFDQTtBQUNBO0FBQ0EsV0FBVyw0QkFBNEIsV0FBVztBQUNsRCxnQ0FBZ0M7QUFDaEMsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSxXQUFXLENBQUMsd0RBQUssY0FBYyxtREFBUSxDQUFDO0FBQ3hDLFdBQVcsQ0FBQyx3REFBSyxjQUFjLHVEQUFRO0FBQ3ZDO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yYWZ0aG9yLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay1leHRlbnNpb24tbWF0aC9kZXYvbGliL3N5bnRheC5qcz84YzVjIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9uc30gZnJvbSAnbWljcm9tYXJrLWV4dGVuc2lvbi1tYXRoJ1xuICogQGltcG9ydCB7RXh0ZW5zaW9ufSBmcm9tICdtaWNyb21hcmstdXRpbC10eXBlcydcbiAqL1xuXG5pbXBvcnQge2NvZGVzfSBmcm9tICdtaWNyb21hcmstdXRpbC1zeW1ib2wnXG5pbXBvcnQge21hdGhGbG93fSBmcm9tICcuL21hdGgtZmxvdy5qcydcbmltcG9ydCB7bWF0aFRleHR9IGZyb20gJy4vbWF0aC10ZXh0LmpzJ1xuXG4vKipcbiAqIENyZWF0ZSBhbiBleHRlbnNpb24gZm9yIGBtaWNyb21hcmtgIHRvIGVuYWJsZSBtYXRoIHN5bnRheC5cbiAqXG4gKiBAcGFyYW0ge09wdGlvbnMgfCBudWxsIHwgdW5kZWZpbmVkfSBbb3B0aW9ucz17fV1cbiAqICAgQ29uZmlndXJhdGlvbiAoZGVmYXVsdDogYHt9YCkuXG4gKiBAcmV0dXJucyB7RXh0ZW5zaW9ufVxuICogICBFeHRlbnNpb24gZm9yIGBtaWNyb21hcmtgIHRoYXQgY2FuIGJlIHBhc3NlZCBpbiBgZXh0ZW5zaW9uc2AsIHRvXG4gKiAgIGVuYWJsZSBtYXRoIHN5bnRheC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIG1hdGgob3B0aW9ucykge1xuICByZXR1cm4ge1xuICAgIGZsb3c6IHtbY29kZXMuZG9sbGFyU2lnbl06IG1hdGhGbG93fSxcbiAgICB0ZXh0OiB7W2NvZGVzLmRvbGxhclNpZ25dOiBtYXRoVGV4dChvcHRpb25zKX1cbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-math/dev/lib/syntax.js\n");

/***/ })

};
;