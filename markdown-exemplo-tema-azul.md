# Demonstração do Novo Tema Azul para Markdown

Este documento demonstra todos os elementos estilizados com o novo tema azul elegante do RafthorIA.

## Elementos Básicos de Texto

### Parágrafos e Formatação

Este é um parágrafo normal que demonstra a tipografia base do tema azul. O texto possui boa legibilidade com espaçamento otimizado e cores suaves que não cansam a vista durante leitura prolongada.

**Texto em negrito** e *texto em itálico* mantêm a harmonia visual com o tema. Você também pode usar ~~texto riscado~~ quando necessário.

### Links e Navegação

Aqui temos alguns [links de exemplo](https://example.com) que demonstram o novo estilo interativo com efeitos hover elegantes. Os links possuem animações suaves e indicadores visuais claros.

Outro [link interessante](https://github.com) para mostrar a consistência do design.

## Hierarquia de Títulos

# Título Nível 1 (H1)
O título principal possui gradiente azul e linha decorativa inferior.

## Título Nível 2 (H2)
Títulos de segundo nível têm uma barra lateral azul elegante.

### Título Nível 3 (H3)
Títulos de terceiro nível em azul médio.

#### Título Nível 4 (H4)
Títulos menores em tons azuis mais claros.

##### Título Nível 5 (H5)
Subtítulos em azul suave.

###### Título Nível 6 (H6)
Títulos menores em maiúsculas com espaçamento.

## Listas e Organização

### Lista Não Ordenada

- Primeiro item da lista com marcador azul elegante
- Segundo item demonstrando o espaçamento otimizado
- Terceiro item com sublista:
  - Subitem aninhado com marcador menor
  - Outro subitem para demonstrar hierarquia
    - Item de terceiro nível
- Último item da lista principal

### Lista Ordenada

1. Primeiro item numerado com contador estilizado
2. Segundo item mostrando a numeração azul
3. Terceiro item com sublista:
   1. Subitem numerado
   2. Outro subitem aninhado
   3. Terceiro subitem
4. Item final da lista principal

## Blocos de Código

### Código Inline

Use `console.log()` para debug, ou `npm install` para instalar pacotes. O código inline possui fundo azul sutil e bordas arredondadas.

### Blocos de Código

```javascript
// Exemplo de código JavaScript com syntax highlighting azul
function exemploFuncao(parametro) {
  const variavel = "string de exemplo";
  const numero = 42;
  
  // Comentário explicativo
  if (parametro > 0) {
    return {
      sucesso: true,
      dados: variavel,
      valor: numero * 2
    };
  }
  
  throw new Error("Parâmetro inválido");
}

// Chamada da função
const resultado = exemploFuncao(10);
console.log(resultado);
```

```python
# Exemplo em Python
import numpy as np
import matplotlib.pyplot as plt

def calcular_fibonacci(n):
    """Calcula a sequência de Fibonacci até n termos."""
    if n <= 0:
        return []
    elif n == 1:
        return [0]
    elif n == 2:
        return [0, 1]
    
    fib = [0, 1]
    for i in range(2, n):
        fib.append(fib[i-1] + fib[i-2])
    
    return fib

# Gerar e plotar a sequência
fibonacci = calcular_fibonacci(15)
plt.plot(fibonacci, 'bo-', color='#3b82f6')
plt.title('Sequência de Fibonacci')
plt.show()
```

## Fórmulas Matemáticas (LaTeX)

### Fórmulas Inline

A famosa equação de Einstein é $E = mc^2$, onde $E$ é energia, $m$ é massa e $c$ é a velocidade da luz.

A fórmula quadrática é $x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}$ para resolver equações do segundo grau.

### Fórmulas em Bloco

$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$

$$\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}$$

$$\begin{pmatrix}
a & b \\
c & d
\end{pmatrix}
\begin{pmatrix}
x \\
y
\end{pmatrix}
=
\begin{pmatrix}
ax + by \\
cx + dy
\end{pmatrix}$$

## Citações e Blockquotes

> Esta é uma citação elegante que demonstra o novo design azul. As citações possuem fundo sutil, borda lateral colorida e aspas decorativas.
> 
> Múltiplos parágrafos em citações mantêm a formatação consistente e a legibilidade otimizada.

> "A imaginação é mais importante que o conhecimento." - Albert Einstein

## Tabelas

| Elemento | Cor Principal | Uso | Exemplo |
|----------|---------------|-----|---------|
| Títulos | Gradiente Azul | Hierarquia | H1, H2, H3 |
| Links | Azul Claro | Navegação | [exemplo](/) |
| Código | Azul Escuro | Programação | `function()` |
| Citações | Azul Sutil | Destaque | > Citação |
| Tabelas | Azul Médio | Dados | Esta tabela |

## Separadores

---

## Elementos Especiais

### Código com Diferentes Linguagens

```css
/* Exemplo de CSS com o tema azul */
.elemento {
  background: linear-gradient(135deg, #1e3a8a, #3b82f6);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.2);
  color: #f1f5f9;
  padding: 16px;
  transition: all 0.3s ease;
}

.elemento:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.25);
}
```

```json
{
  "tema": "azul-elegante",
  "versao": "2.0.0",
  "caracteristicas": [
    "Gradientes azuis",
    "Sombras suaves",
    "Bordas arredondadas",
    "Animações fluidas"
  ],
  "paleta": {
    "primaria": "#3b82f6",
    "secundaria": "#1e3a8a",
    "acento": "#60a5fa",
    "texto": "#f1f5f9"
  }
}
```

### Mais Fórmulas Matemáticas

Equação de Schrödinger:
$$i\hbar\frac{\partial}{\partial t}\Psi(\mathbf{r},t) = \hat{H}\Psi(\mathbf{r},t)$$

Transformada de Fourier:
$$\mathcal{F}\{f(t)\} = \int_{-\infty}^{\infty} f(t) e^{-2\pi i \xi t} dt$$

## Conclusão

Este tema azul elegante oferece:

- **Visual Moderno**: Gradientes e sombras suaves
- **Legibilidade Otimizada**: Contraste adequado e tipografia clara  
- **Responsividade**: Adaptação perfeita para todos os dispositivos
- **Interatividade**: Animações e efeitos hover elegantes
- **Consistência**: Paleta de cores harmoniosa em todos os elementos

O novo tema transforma a experiência de leitura e escrita, mantendo a funcionalidade completa do markdown com um visual profissional e moderno.
