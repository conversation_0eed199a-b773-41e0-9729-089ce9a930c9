'use client';

import { useState, useEffect } from 'react';
import { MessageStatistics, StatCard } from '@/lib/types/statistics';
import { ChatMessage } from '@/lib/types/chat';
import statisticsService from '@/lib/services/statisticsService';

interface StatisticsModalProps {
  isOpen: boolean;
  onClose: () => void;
  messages: ChatMessage[];
  chatName?: string;
}

export default function StatisticsModal({
  isOpen,
  onClose,
  messages,
  chatName = 'Todas as Conversas'
}: StatisticsModalProps) {
  const [statistics, setStatistics] = useState<MessageStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'details' | 'charts'>('overview');

  useEffect(() => {
    if (isOpen && messages.length > 0) {
      setLoading(true);
      try {
        const stats = statisticsService.calculateMessageStatistics(messages);
        setStatistics(stats);
      } catch (error) {
        console.error('Erro ao calcular estatísticas:', error);
      } finally {
        setLoading(false);
      }
    }
  }, [isOpen, messages]);

  if (!isOpen) return null;

  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat('pt-BR').format(Math.round(num));
  };

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4
    }).format(value);
  };

  const formatTime = (ms: number): string => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}min`;
  };

  const getStatCards = (): StatCard[] => {
    if (!statistics) return [];

    return [
      {
        title: 'Total de Mensagens',
        value: formatNumber(statistics.totalMessages),
        icon: 'message',
        color: 'blue'
      },
      {
        title: 'Total de Palavras',
        value: formatNumber(statistics.totalWords),
        icon: 'text',
        color: 'green'
      },
      {
        title: 'Palavras da IA',
        value: formatNumber(statistics.totalWordsAI),
        icon: 'robot',
        color: 'purple'
      },
      {
        title: 'Palavras do Usuário',
        value: formatNumber(statistics.totalWordsUser),
        icon: 'user',
        color: 'orange'
      },

      {
        title: 'Tempo Médio de Resposta',
        value: formatTime(statistics.averageResponseTime),
        icon: 'clock',
        color: 'purple'
      },
      {
        title: 'Comprimento Médio',
        value: formatNumber(statistics.averageMessageLength),
        subtitle: 'caracteres',
        icon: 'length',
        color: 'orange'
      },
      {
        title: 'Palavras por Mensagem',
        value: formatNumber(statistics.averageWordsPerMessage),
        icon: 'average',
        color: 'cyan'
      },
      {
        title: 'Frases por Mensagem',
        value: formatNumber(statistics.averageSentencesPerMessage),
        icon: 'sentence',
        color: 'blue'
      },
      {
        title: 'Tempo de Leitura',
        value: `${statistics.estimatedReadingTime}min`,
        icon: 'read',
        color: 'green'
      },
      {
        title: 'Caracteres da IA',
        value: formatNumber(statistics.totalCharactersAI),
        icon: 'robot',
        color: 'purple'
      },
      {
        title: 'Caracteres do Usuário',
        value: formatNumber(statistics.totalCharactersUser),
        icon: 'user',
        color: 'orange'
      },
      {
        title: 'Total de Caracteres',
        value: formatNumber(statistics.totalCharacters),
        icon: 'total',
        color: 'cyan'
      }
    ];
  };

  const getIconSvg = (iconName: string) => {
    const icons: { [key: string]: string } = {
      message: 'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z',
      text: 'M4 6h16M4 12h16M4 18h7',
      robot: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
      user: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
      input: 'M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z',
      output: 'M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14',
      dollar: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1',
      clock: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z',
      length: 'M7 16l-4-4m0 0l4-4m-4 4h18',
      average: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
      sentence: 'M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253',
      read: 'M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253',
      total: 'M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z'
    };
    return icons[iconName] || icons.message;
  };

  const getColorClasses = (color: StatCard['color']) => {
    const colors = {
      blue: 'from-blue-500/20 to-blue-600/20 border-blue-500/30 text-blue-300',
      green: 'from-green-500/20 to-green-600/20 border-green-500/30 text-green-300',
      purple: 'from-purple-500/20 to-purple-600/20 border-purple-500/30 text-purple-300',
      orange: 'from-orange-500/20 to-orange-600/20 border-orange-500/30 text-orange-300',
      red: 'from-red-500/20 to-red-600/20 border-red-500/30 text-red-300',
      cyan: 'from-cyan-500/20 to-cyan-600/20 border-cyan-500/30 text-cyan-300'
    };
    return colors[color];
  };

  // Função para gerar dados do gráfico de evolução temporal (mensagens por dia)
  const getTemporalEvolutionData = (): LineChartDataType | null => {
    if (!messages.length) return null;

    const dailyData = new Map<string, number>();
    messages.forEach(message => {
      const date = new Date(message.timestamp).toISOString().split('T')[0];
      dailyData.set(date, (dailyData.get(date) || 0) + 1);
    });

    const sortedDates = Array.from(dailyData.keys()).sort();
    const last30Days = sortedDates.slice(-30); // Últimos 30 dias

    return {
      labels: last30Days.map(date => {
        const d = new Date(date);
        return `${d.getDate()}/${d.getMonth() + 1}`;
      }),
      data: last30Days.map(date => dailyData.get(date) || 0)
    };
  };

  // Função para gerar dados do gráfico de comprimento de mensagens
  const getMessageLengthDistributionData = (): ChartDataType | null => {
    if (!messages.length) return null;

    const ranges = [
      { label: '1-50 chars', min: 1, max: 50 },
      { label: '51-100 chars', min: 51, max: 100 },
      { label: '101-200 chars', min: 101, max: 200 },
      { label: '201-500 chars', min: 201, max: 500 },
      { label: '501-1000 chars', min: 501, max: 1000 },
      { label: '1000+ chars', min: 1001, max: Infinity }
    ];

    const distribution = ranges.map(range => {
      return messages.filter(m =>
        m.content.length >= range.min && m.content.length <= range.max
      ).length;
    });

    return {
      labels: ranges.map(r => r.label),
      data: distribution,
      colors: ['#10B981', '#3B82F6', '#8B5CF6', '#F59E0B', '#EF4444', '#EC4899']
    };
  };

  // Função para gerar dados do gráfico de tempo de resposta
  const getResponseTimeData = (): LineChartDataType | null => {
    const aiMessages = messages.filter(m => m.role === 'assistant' && m.responseTime);
    if (aiMessages.length < 2) return null;

    const last20Responses = aiMessages.slice(-20); // Últimas 20 respostas

    return {
      labels: last20Responses.map((_, index) => `Resp ${index + 1}`),
      data: last20Responses.map(m => m.responseTime! / 1000) // Converter para segundos
    };
  };

  // Função para gerar dados do gráfico de atividade por dia da semana
  const getWeeklyActivityData = (): ChartDataType | null => {
    if (!messages.length) return null;

    const daysOfWeek = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
    const weeklyData = new Array(7).fill(0);

    messages.forEach(message => {
      const dayOfWeek = new Date(message.timestamp).getDay();
      weeklyData[dayOfWeek]++;
    });

    return {
      labels: daysOfWeek,
      data: weeklyData,
      colors: ['#EF4444', '#F59E0B', '#10B981', '#3B82F6', '#8B5CF6', '#EC4899', '#06B6D4']
    };
  };

  // Interfaces para os dados dos gráficos
  interface ChartDataType {
    labels: string[];
    data: number[];
    colors: string[];
  }

  interface SliceType {
    path: string;
    color: string;
    label: string;
    value: number;
    percentage: string;
  }

  // Componente de gráfico de pizza simples
  const PieChart = ({ data, title }: { data: ChartDataType | null, title: string }) => {
    if (!data) return null;

    const total = data.data.reduce((sum: number, value: number) => sum + value, 0);
    let currentAngle = 0;

    const slices: SliceType[] = data.data.map((value: number, index: number) => {
      const percentage = (value / total) * 100;
      const angle = (value / total) * 360;
      const startAngle = currentAngle;
      const endAngle = currentAngle + angle;
      currentAngle += angle;

      const x1 = 50 + 40 * Math.cos((startAngle - 90) * Math.PI / 180);
      const y1 = 50 + 40 * Math.sin((startAngle - 90) * Math.PI / 180);
      const x2 = 50 + 40 * Math.cos((endAngle - 90) * Math.PI / 180);
      const y2 = 50 + 40 * Math.sin((endAngle - 90) * Math.PI / 180);

      const largeArcFlag = angle > 180 ? 1 : 0;

      return {
        path: `M 50 50 L ${x1} ${y1} A 40 40 0 ${largeArcFlag} 1 ${x2} ${y2} Z`,
        color: data.colors[index],
        label: data.labels[index],
        value: value,
        percentage: percentage.toFixed(1)
      };
    });

    return (
      <div className="bg-blue-900/20 border border-blue-700/30 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">{title}</h3>
        <div className="flex items-center gap-6">
          <svg width="200" height="200" viewBox="0 0 100 100" className="flex-shrink-0">
            {slices.map((slice: SliceType, index: number) => (
              <path
                key={index}
                d={slice.path}
                fill={slice.color}
                className="hover:opacity-80 transition-opacity"
              />
            ))}
          </svg>
          <div className="space-y-2">
            {slices.map((slice: SliceType, index: number) => (
              <div key={index} className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: slice.color }}
                ></div>
                <span className="text-sm text-blue-300">{slice.label}</span>
                <span className="text-sm text-white font-medium">
                  {formatNumber(slice.value)} ({slice.percentage}%)
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // Componente de gráfico de barras simples
  const BarChart = ({ data, title, height = 200 }: { data: ChartDataType | null, title: string, height?: number }) => {
    if (!data) return null;

    const maxValue = Math.max(...data.data);
    const barWidth = 300 / data.data.length - 10;

    return (
      <div className="bg-blue-900/20 border border-blue-700/30 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">{title}</h3>
        <div className="overflow-x-auto">
          <svg width="100%" height={height + 60} viewBox={`0 0 ${Math.max(400, data.data.length * 50)} ${height + 60}`}>
            {data.data.map((value: number, index: number) => {
              const barHeight = (value / maxValue) * height;
              const x = index * (barWidth + 10) + 20;
              const y = height - barHeight + 20;

              return (
                <g key={index}>
                  <rect
                    x={x}
                    y={y}
                    width={barWidth}
                    height={barHeight}
                    fill={data.colors ? data.colors[index] : '#3B82F6'}
                    className="hover:opacity-80 transition-opacity"
                    rx="2"
                  />
                  <text
                    x={x + barWidth / 2}
                    y={y - 5}
                    textAnchor="middle"
                    className="fill-white text-xs"
                  >
                    {formatNumber(value)}
                  </text>
                  <text
                    x={x + barWidth / 2}
                    y={height + 35}
                    textAnchor="middle"
                    className="fill-blue-300 text-xs"
                    transform={data.labels[index].length > 8 ? `rotate(-45, ${x + barWidth / 2}, ${height + 35})` : ''}
                  >
                    {data.labels[index].length > 10 ? data.labels[index].substring(0, 8) + '...' : data.labels[index]}
                  </text>
                </g>
              );
            })}
          </svg>
        </div>
      </div>
    );
  };

  // Interface para dados de linha
  interface LineChartDataType {
    labels: string[];
    data: number[];
  }

  // Componente de gráfico de linha simples
  const LineChart = ({ data, title }: { data: LineChartDataType | null, title: string }) => {
    if (!data) return null;

    const maxValue = Math.max(...data.data);
    const width = 600;
    const height = 200;
    const padding = 40;

    const points = data.data.map((value: number, index: number) => {
      const x = (index / (data.data.length - 1)) * (width - 2 * padding) + padding;
      const y = height - (value / maxValue) * (height - 2 * padding) - padding;
      return `${x},${y}`;
    }).join(' ');

    return (
      <div className="bg-blue-900/20 border border-blue-700/30 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">{title}</h3>
        <div className="overflow-x-auto">
          <svg width={width} height={height + 40} viewBox={`0 0 ${width} ${height + 40}`}>
            {/* Grid lines */}
            {[0, 1, 2, 3, 4].map(i => {
              const y = height - (i / 4) * (height - 2 * padding) - padding;
              return (
                <line
                  key={i}
                  x1={padding}
                  y1={y}
                  x2={width - padding}
                  y2={y}
                  stroke="#1E40AF"
                  strokeOpacity="0.3"
                  strokeWidth="1"
                />
              );
            })}

            {/* Line */}
            <polyline
              points={points}
              fill="none"
              stroke="#3B82F6"
              strokeWidth="2"
              className="drop-shadow-sm"
            />

            {/* Points */}
            {data.data.map((value: number, index: number) => {
              const x = (index / (data.data.length - 1)) * (width - 2 * padding) + padding;
              const y = height - (value / maxValue) * (height - 2 * padding) - padding;

              return (
                <g key={index}>
                  <circle
                    cx={x}
                    cy={y}
                    r="3"
                    fill="#3B82F6"
                    className="hover:r-4 transition-all"
                  />
                  {index % 4 === 0 && (
                    <text
                      x={x}
                      y={height + 20}
                      textAnchor="middle"
                      className="fill-blue-300 text-xs"
                    >
                      {data.labels[index]}
                    </text>
                  )}
                </g>
              );
            })}
          </svg>
        </div>
      </div>
    );
  };

  // Componente de gráfico de área
  const AreaChart = ({ data, title }: { data: LineChartDataType | null, title: string }) => {
    if (!data) return null;

    const maxValue = Math.max(...data.data);
    const width = 600;
    const height = 200;
    const padding = 40;

    const points = data.data.map((value: number, index: number) => {
      const x = (index / (data.data.length - 1)) * (width - 2 * padding) + padding;
      const y = height - (value / maxValue) * (height - 2 * padding) - padding;
      return { x, y, value };
    });

    const pathData = points.map((point, index) =>
      `${index === 0 ? 'M' : 'L'} ${point.x},${point.y}`
    ).join(' ');

    const areaPath = `${pathData} L ${points[points.length - 1].x},${height - padding} L ${padding},${height - padding} Z`;

    return (
      <div className="bg-blue-900/20 border border-blue-700/30 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">{title}</h3>
        <div className="overflow-x-auto">
          <svg width={width} height={height + 40} viewBox={`0 0 ${width} ${height + 40}`}>
            {/* Grid lines */}
            {[0, 1, 2, 3, 4].map(i => {
              const y = height - (i / 4) * (height - 2 * padding) - padding;
              return (
                <line
                  key={i}
                  x1={padding}
                  y1={y}
                  x2={width - padding}
                  y2={y}
                  stroke="#1E40AF"
                  strokeOpacity="0.3"
                  strokeWidth="1"
                />
              );
            })}

            {/* Gradient definition */}
            <defs>
              <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#3B82F6" stopOpacity="0.1"/>
              </linearGradient>
            </defs>

            {/* Area fill */}
            <path
              d={areaPath}
              fill="url(#areaGradient)"
              opacity="0.6"
            />

            {/* Line */}
            <path
              d={pathData}
              fill="none"
              stroke="#3B82F6"
              strokeWidth="2"
              className="drop-shadow-sm"
            />

            {/* Points */}
            {points.map((point, index) => (
              <g key={index}>
                <circle
                  cx={point.x}
                  cy={point.y}
                  r="3"
                  fill="#3B82F6"
                  className="hover:r-4 transition-all"
                />
                {index % Math.ceil(data.data.length / 8) === 0 && (
                  <text
                    x={point.x}
                    y={height + 20}
                    textAnchor="middle"
                    className="fill-blue-300 text-xs"
                  >
                    {data.labels[index]}
                  </text>
                )}
              </g>
            ))}
          </svg>
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl border border-blue-700/30 rounded-2xl shadow-2xl w-full max-w-7xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-blue-700/30">
          <div>
            <h2 className="text-2xl font-bold text-white flex items-center gap-3">
              <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Estatísticas
            </h2>
            <p className="text-blue-300 mt-1">{chatName}</p>
          </div>
          <button
            onClick={onClose}
            className="text-blue-300 hover:text-white transition-colors p-2 hover:bg-blue-800/30 rounded-lg"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-blue-700/30">
          {[
            { id: 'overview', label: 'Visão Geral', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },
            { id: 'details', label: 'Detalhes', icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z' },
            { id: 'charts', label: 'Gráficos', icon: 'M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-6 py-3 font-medium transition-all ${
                activeTab === tab.id
                  ? 'text-blue-300 border-b-2 border-blue-400 bg-blue-900/20'
                  : 'text-blue-400 hover:text-blue-300 hover:bg-blue-900/10'
              }`}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={tab.icon} />
              </svg>
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400"></div>
              <span className="ml-3 text-blue-300">Calculando estatísticas...</span>
            </div>
          ) : !statistics ? (
            <div className="text-center py-12">
              <p className="text-blue-300">Nenhuma estatística disponível</p>
            </div>
          ) : (
            <>
              {activeTab === 'overview' && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {getStatCards().map((card, index) => (
                    <div
                      key={index}
                      className={`bg-gradient-to-br ${getColorClasses(card.color)} border rounded-xl p-4 hover:scale-105 transition-transform duration-200`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={getIconSvg(card.icon)} />
                        </svg>
                      </div>
                      <h3 className="text-sm font-medium text-white/80 mb-1">{card.title}</h3>
                      <p className="text-2xl font-bold text-white">{card.value}</p>
                      {card.subtitle && (
                        <p className="text-xs text-white/60 mt-1">{card.subtitle}</p>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {activeTab === 'details' && (
                <div className="space-y-6">
                  {/* Palavras mais usadas */}
                  <div className="bg-blue-900/20 border border-blue-700/30 rounded-xl p-6">
                    <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
                      <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                      </svg>
                      Palavras Mais Usadas
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {statistics.mostUsedWords.slice(0, 9).map((word, index) => (
                        <div key={index} className="bg-blue-800/30 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-white font-medium">#{index + 1}</span>
                            <span className="text-blue-300 text-sm">{word.percentage.toFixed(1)}%</span>
                          </div>
                          <p className="text-lg font-semibold text-white">{word.word}</p>
                          <p className="text-blue-400 text-sm">{formatNumber(word.count)} vezes</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Distribuição de mensagens */}
                  <div className="bg-blue-900/20 border border-blue-700/30 rounded-xl p-6">
                    <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
                      <svg className="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      Distribuição por Usuário
                    </h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-blue-300">Mensagens do Usuário</span>
                        <span className="text-white font-semibold">
                          {formatNumber(messages.filter(m => m.role === 'user').length)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-blue-300">Mensagens da IA</span>
                        <span className="text-white font-semibold">
                          {formatNumber(messages.filter(m => m.role === 'assistant').length)}
                        </span>
                      </div>
                      <div className="w-full bg-blue-800/30 rounded-full h-3">
                        <div
                          className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-500"
                          style={{
                            width: `${(messages.filter(m => m.role === 'user').length / messages.length) * 100}%`
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'charts' && (
                <div className="space-y-6">
                  {/* Evolução temporal das mensagens */}
                  <AreaChart
                    data={getTemporalEvolutionData()}
                    title="Evolução das Mensagens (Últimos 30 Dias)"
                  />

                  {/* Gráficos de análise comportamental */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <BarChart
                      data={getMessageLengthDistributionData()}
                      title="Distribuição por Tamanho de Mensagem"
                      height={200}
                    />
                    <BarChart
                      data={getWeeklyActivityData()}
                      title="Atividade por Dia da Semana"
                      height={200}
                    />
                  </div>

                  {/* Gráfico de tempo de resposta */}
                  <LineChart
                    data={getResponseTimeData()}
                    title="Tempo de Resposta da IA (Últimas 20 Respostas)"
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
