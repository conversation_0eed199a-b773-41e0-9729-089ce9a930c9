"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-markdown";
exports.ids = ["vendor-chunks/react-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-markdown/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/react-markdown/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Markdown: () => (/* binding */ Markdown),\n/* harmony export */   MarkdownAsync: () => (/* binding */ MarkdownAsync),\n/* harmony export */   MarkdownHooks: () => (/* binding */ MarkdownHooks),\n/* harmony export */   defaultUrlTransform: () => (/* binding */ defaultUrlTransform)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! hast-util-to-jsx-runtime */ \"(ssr)/./node_modules/hast-util-to-jsx-runtime/lib/index.js\");\n/* harmony import */ var html_url_attributes__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! html-url-attributes */ \"(ssr)/./node_modules/html-url-attributes/lib/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var remark_parse__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! remark-parse */ \"(ssr)/./node_modules/remark-parse/lib/index.js\");\n/* harmony import */ var remark_rehype__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! remark-rehype */ \"(ssr)/./node_modules/remark-rehype/lib/index.js\");\n/* harmony import */ var unified__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unified */ \"(ssr)/./node_modules/unified/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/vfile/lib/index.js\");\n/**\n * @import {Element, Nodes, Parents, Root} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {ComponentType, JSX, ReactElement, ReactNode} from 'react'\n * @import {Options as RemarkRehypeOptions} from 'remark-rehype'\n * @import {BuildVisitor} from 'unist-util-visit'\n * @import {PluggableList, Processor} from 'unified'\n */\n\n/**\n * @callback AllowElement\n *   Filter elements.\n * @param {Readonly<Element>} element\n *   Element to check.\n * @param {number} index\n *   Index of `element` in `parent`.\n * @param {Readonly<Parents> | undefined} parent\n *   Parent of `element`.\n * @returns {boolean | null | undefined}\n *   Whether to allow `element` (default: `false`).\n */\n\n/**\n * @typedef ExtraProps\n *   Extra fields we pass.\n * @property {Element | undefined} [node]\n *   passed when `passNode` is on.\n */\n\n/**\n * @typedef {{\n *   [Key in keyof JSX.IntrinsicElements]?: ComponentType<JSX.IntrinsicElements[Key] & ExtraProps> | keyof JSX.IntrinsicElements\n * }} Components\n *   Map tag names to components.\n */\n\n/**\n * @typedef Deprecation\n *   Deprecation.\n * @property {string} from\n *   Old field.\n * @property {string} id\n *   ID in readme.\n * @property {keyof Options} [to]\n *   New field.\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {AllowElement | null | undefined} [allowElement]\n *   Filter elements (optional);\n *   `allowedElements` / `disallowedElements` is used first.\n * @property {ReadonlyArray<string> | null | undefined} [allowedElements]\n *   Tag names to allow (default: all tag names);\n *   cannot combine w/ `disallowedElements`.\n * @property {string | null | undefined} [children]\n *   Markdown.\n * @property {Components | null | undefined} [components]\n *   Map tag names to components.\n * @property {ReadonlyArray<string> | null | undefined} [disallowedElements]\n *   Tag names to disallow (default: `[]`);\n *   cannot combine w/ `allowedElements`.\n * @property {PluggableList | null | undefined} [rehypePlugins]\n *   List of rehype plugins to use.\n * @property {PluggableList | null | undefined} [remarkPlugins]\n *   List of remark plugins to use.\n * @property {Readonly<RemarkRehypeOptions> | null | undefined} [remarkRehypeOptions]\n *   Options to pass through to `remark-rehype`.\n * @property {boolean | null | undefined} [skipHtml=false]\n *   Ignore HTML in markdown completely (default: `false`).\n * @property {boolean | null | undefined} [unwrapDisallowed=false]\n *   Extract (unwrap) what’s in disallowed elements (default: `false`);\n *   normally when say `strong` is not allowed, it and it’s children are dropped,\n *   with `unwrapDisallowed` the element itself is replaced by its children.\n * @property {UrlTransform | null | undefined} [urlTransform]\n *   Change URLs (default: `defaultUrlTransform`)\n */\n\n/**\n * @typedef HooksOptionsOnly\n *   Configuration specifically for {@linkcode MarkdownHooks}.\n * @property {ReactNode | null | undefined} [fallback]\n *   Content to render while the processor processing the markdown (optional).\n */\n\n/**\n * @typedef {Options & HooksOptionsOnly} HooksOptions\n *   Configuration for {@linkcode MarkdownHooks};\n *   extends the regular {@linkcode Options} with a `fallback` prop.\n */\n\n/**\n * @callback UrlTransform\n *   Transform all URLs.\n * @param {string} url\n *   URL.\n * @param {string} key\n *   Property name (example: `'href'`).\n * @param {Readonly<Element>} node\n *   Node.\n * @returns {string | null | undefined}\n *   Transformed URL (optional).\n */\n\n\n\n\n\n\n\n\n\n\n\n\nconst changelog =\n  'https://github.com/remarkjs/react-markdown/blob/main/changelog.md'\n\n/** @type {PluggableList} */\nconst emptyPlugins = []\n/** @type {Readonly<RemarkRehypeOptions>} */\nconst emptyRemarkRehypeOptions = {allowDangerousHtml: true}\nconst safeProtocol = /^(https?|ircs?|mailto|xmpp)$/i\n\n// Mutable because we `delete` any time it’s used and a message is sent.\n/** @type {ReadonlyArray<Readonly<Deprecation>>} */\nconst deprecations = [\n  {from: 'astPlugins', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'allowDangerousHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {\n    from: 'allowNode',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowElement'\n  },\n  {\n    from: 'allowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowedElements'\n  },\n  {from: 'className', id: 'remove-classname'},\n  {\n    from: 'disallowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'disallowedElements'\n  },\n  {from: 'escapeHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'includeElementIndex', id: '#remove-includeelementindex'},\n  {\n    from: 'includeNodeIndex',\n    id: 'change-includenodeindex-to-includeelementindex'\n  },\n  {from: 'linkTarget', id: 'remove-linktarget'},\n  {from: 'plugins', id: 'change-plugins-to-remarkplugins', to: 'remarkPlugins'},\n  {from: 'rawSourcePos', id: '#remove-rawsourcepos'},\n  {from: 'renderers', id: 'change-renderers-to-components', to: 'components'},\n  {from: 'source', id: 'change-source-to-children', to: 'children'},\n  {from: 'sourcePos', id: '#remove-sourcepos'},\n  {from: 'transformImageUri', id: '#add-urltransform', to: 'urlTransform'},\n  {from: 'transformLinkUri', id: '#add-urltransform', to: 'urlTransform'}\n]\n\n/**\n * Component to render markdown.\n *\n * This is a synchronous component.\n * When using async plugins,\n * see {@linkcode MarkdownAsync} or {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nfunction Markdown(options) {\n  const processor = createProcessor(options)\n  const file = createFile(options)\n  return post(processor.runSync(processor.parse(file), file), options)\n}\n\n/**\n * Component to render markdown with support for async plugins\n * through async/await.\n *\n * Components returning promises are supported on the server.\n * For async support on the client,\n * see {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Promise<ReactElement>}\n *   Promise to a React element.\n */\nasync function MarkdownAsync(options) {\n  const processor = createProcessor(options)\n  const file = createFile(options)\n  const tree = await processor.run(processor.parse(file), file)\n  return post(tree, options)\n}\n\n/**\n * Component to render markdown with support for async plugins through hooks.\n *\n * This uses `useEffect` and `useState` hooks.\n * Hooks run on the client and do not immediately render something.\n * For async support on the server,\n * see {@linkcode MarkdownAsync}.\n *\n * @param {Readonly<HooksOptions>} options\n *   Props.\n * @returns {ReactNode}\n *   React node.\n */\nfunction MarkdownHooks(options) {\n  const processor = createProcessor(options)\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\n    /** @type {Error | undefined} */ (undefined)\n  )\n  const [tree, setTree] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(/** @type {Root | undefined} */ (undefined))\n\n  ;(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(\n    function () {\n      let cancelled = false\n      const file = createFile(options)\n\n      processor.run(processor.parse(file), file, function (error, tree) {\n        if (!cancelled) {\n          setError(error)\n          setTree(tree)\n        }\n      })\n\n      /**\n       * @returns {undefined}\n       *   Nothing.\n       */\n      return function () {\n        cancelled = true\n      }\n    },\n    [\n      options.children,\n      options.rehypePlugins,\n      options.remarkPlugins,\n      options.remarkRehypeOptions\n    ]\n  )\n\n  if (error) throw error\n\n  return tree ? post(tree, options) : options.fallback\n}\n\n/**\n * Set up the `unified` processor.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Processor<MdastRoot, MdastRoot, Root, undefined, undefined>}\n *   Result.\n */\nfunction createProcessor(options) {\n  const rehypePlugins = options.rehypePlugins || emptyPlugins\n  const remarkPlugins = options.remarkPlugins || emptyPlugins\n  const remarkRehypeOptions = options.remarkRehypeOptions\n    ? {...options.remarkRehypeOptions, ...emptyRemarkRehypeOptions}\n    : emptyRemarkRehypeOptions\n\n  const processor = (0,unified__WEBPACK_IMPORTED_MODULE_2__.unified)()\n    .use(remark_parse__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n    .use(remarkPlugins)\n    .use(remark_rehype__WEBPACK_IMPORTED_MODULE_4__[\"default\"], remarkRehypeOptions)\n    .use(rehypePlugins)\n\n  return processor\n}\n\n/**\n * Set up the virtual file.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {VFile}\n *   Result.\n */\nfunction createFile(options) {\n  const children = options.children || ''\n  const file = new vfile__WEBPACK_IMPORTED_MODULE_5__.VFile()\n\n  if (typeof children === 'string') {\n    file.value = children\n  } else {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)(\n      'Unexpected value `' +\n        children +\n        '` for `children` prop, expected `string`'\n    )\n  }\n\n  return file\n}\n\n/**\n * Process the result from unified some more.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nfunction post(tree, options) {\n  const allowedElements = options.allowedElements\n  const allowElement = options.allowElement\n  const components = options.components\n  const disallowedElements = options.disallowedElements\n  const skipHtml = options.skipHtml\n  const unwrapDisallowed = options.unwrapDisallowed\n  const urlTransform = options.urlTransform || defaultUrlTransform\n\n  for (const deprecation of deprecations) {\n    if (Object.hasOwn(options, deprecation.from)) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)(\n        'Unexpected `' +\n          deprecation.from +\n          '` prop, ' +\n          (deprecation.to\n            ? 'use `' + deprecation.to + '` instead'\n            : 'remove it') +\n          ' (see <' +\n          changelog +\n          '#' +\n          deprecation.id +\n          '> for more info)'\n      )\n    }\n  }\n\n  if (allowedElements && disallowedElements) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)(\n      'Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other'\n    )\n  }\n\n  (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_7__.visit)(tree, transform)\n\n  return (0,hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.toJsxRuntime)(tree, {\n    Fragment: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment,\n    components,\n    ignoreInvalidStyle: true,\n    jsx: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx,\n    jsxs: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs,\n    passKeys: true,\n    passNode: true\n  })\n\n  /** @type {BuildVisitor<Root>} */\n  function transform(node, index, parent) {\n    if (node.type === 'raw' && parent && typeof index === 'number') {\n      if (skipHtml) {\n        parent.children.splice(index, 1)\n      } else {\n        parent.children[index] = {type: 'text', value: node.value}\n      }\n\n      return index\n    }\n\n    if (node.type === 'element') {\n      /** @type {string} */\n      let key\n\n      for (key in html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes) {\n        if (\n          Object.hasOwn(html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes, key) &&\n          Object.hasOwn(node.properties, key)\n        ) {\n          const value = node.properties[key]\n          const test = html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes[key]\n          if (test === null || test.includes(node.tagName)) {\n            node.properties[key] = urlTransform(String(value || ''), key, node)\n          }\n        }\n      }\n    }\n\n    if (node.type === 'element') {\n      let remove = allowedElements\n        ? !allowedElements.includes(node.tagName)\n        : disallowedElements\n          ? disallowedElements.includes(node.tagName)\n          : false\n\n      if (!remove && allowElement && typeof index === 'number') {\n        remove = !allowElement(node, index, parent)\n      }\n\n      if (remove && parent && typeof index === 'number') {\n        if (unwrapDisallowed && node.children) {\n          parent.children.splice(index, 1, ...node.children)\n        } else {\n          parent.children.splice(index, 1)\n        }\n\n        return index\n      }\n    }\n  }\n}\n\n/**\n * Make a URL safe.\n *\n * @satisfies {UrlTransform}\n * @param {string} value\n *   URL.\n * @returns {string}\n *   Safe URL.\n */\nfunction defaultUrlTransform(value) {\n  // Same as:\n  // <https://github.com/micromark/micromark/blob/929275e/packages/micromark-util-sanitize-uri/dev/index.js#L34>\n  // But without the `encode` part.\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n\n  if (\n    // If there is no protocol, it’s relative.\n    colon === -1 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash !== -1 && colon > slash) ||\n    (questionMark !== -1 && colon > questionMark) ||\n    (numberSign !== -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    safeProtocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n\n  return ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-markdown/lib/index.js\n");

/***/ })

};
;