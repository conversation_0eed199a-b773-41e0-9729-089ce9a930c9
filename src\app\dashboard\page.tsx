'use client';

import { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { db } from '@/lib/firebase';
import Sidebar from '@/components/dashboard/Sidebar';
import ChatArea from '@/components/dashboard/ChatArea';
import SettingsModal from '@/components/dashboard/SettingsModal';

interface UserData {
  username: string;
  email: string;
  balance: number;
  createdAt: string;
}

export default function Dashboard() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [currentChat, setCurrentChat] = useState<string | null>(null);
  const sidebarRef = useRef<{ reloadChats: () => void; updateOpenRouterBalance: () => void; moveToTop: (chatId: string) => void }>(null);
  const chatAreaRef = useRef<{
    handleDownloadModal: (chatId?: string) => void;
    handleAttachmentsModal: (chatId?: string) => void;
    handleStatisticsModal: (chatId?: string) => void;
  }>(null);

  // Função para lidar com a criação de chat automático
  const handleChatCreated = (chatId: string) => {
    setCurrentChat(chatId);
    // Recarregar a sidebar para mostrar o novo chat
    sidebarRef.current?.reloadChats();
  };

  // Função para toggle da sidebar
  const handleSidebarToggle = () => {
    // Em mobile, controla sidebarOpen
    // Em desktop, controla sidebarCollapsed
    if (window.innerWidth < 1024) { // lg breakpoint
      setSidebarOpen(!sidebarOpen);
    } else {
      setSidebarCollapsed(!sidebarCollapsed);
    }
  };

  // Redirecionar se não estiver logado
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login');
    }
  }, [user, authLoading, router]);

  // Buscar dados do usuário
  useEffect(() => {
    const fetchUserData = async () => {
      if (!user) return;

      try {
        // Buscar todos os documentos na coleção usuarios para encontrar o usuário pelo email
        const { collection, query, where, getDocs } = await import('firebase/firestore');
        const usuariosRef = collection(db, 'usuarios');
        const q = query(usuariosRef, where('email', '==', user.email));
        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
          // Usuário encontrado
          const userDoc = querySnapshot.docs[0];
          const data = userDoc.data();
          setUserData({
            username: data.username || user.email?.split('@')[0] || 'Usuário',
            email: data.email || user.email || '',
            balance: data.balance || 0,
            createdAt: data.createdAt || new Date().toISOString()
          });
        } else {
          // Se não encontrar o documento, criar dados padrão
          setUserData({
            username: user.email?.split('@')[0] || 'Usuário',
            email: user.email || '',
            balance: 0,
            createdAt: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('Erro ao buscar dados do usuário:', error);
        // Dados padrão em caso de erro
        setUserData({
          username: user.email?.split('@')[0] || 'Usuário',
          email: user.email || '',
          balance: 0,
          createdAt: new Date().toISOString()
        });
      } finally {
        setLoading(false);
      }
    };

    if (user && !authLoading) {
      fetchUserData();
    }
  }, [user, authLoading]);

  const handleUpdateOpenRouterBalance = () => {
    if (sidebarRef.current) {
      sidebarRef.current.updateOpenRouterBalance();
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gradient-rafthor flex items-center justify-center">
        <div className="text-white text-xl">Carregando...</div>
      </div>
    );
  }

  if (!user || !userData) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-rafthor flex">
      {/* Sidebar */}
      <Sidebar
        ref={sidebarRef}
        userData={userData}
        isOpen={sidebarOpen}
        isCollapsed={sidebarCollapsed}
        onToggle={handleSidebarToggle}
        onSettingsOpen={() => setSettingsOpen(true)}
        onChatSelect={setCurrentChat}
        currentChat={currentChat}
        showCloseButton={true}
        onDownloadModal={(chatId) => chatAreaRef.current?.handleDownloadModal(chatId)}
        onAttachmentsModal={(chatId) => chatAreaRef.current?.handleAttachmentsModal(chatId)}
        onStatisticsModal={(chatId) => chatAreaRef.current?.handleStatisticsModal(chatId)}
      />

      {/* Área principal */}
      <div className={`flex-1 flex flex-col h-screen overflow-hidden transition-all duration-300 ${
        sidebarCollapsed ? 'lg:ml-0' : 'lg:ml-80'
      }`}>
        {/* Header mobile */}
        <div className="lg:hidden bg-white/10 backdrop-blur-sm border-b border-white/20 p-4">
          <button
            onClick={() => setSidebarOpen(true)}
            className="text-white hover:text-white/80 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Área do chat */}
        <ChatArea
          ref={chatAreaRef}
          currentChat={currentChat}
          onChatCreated={handleChatCreated}
          onUpdateOpenRouterBalance={handleUpdateOpenRouterBalance}
          onChatSelect={setCurrentChat}
          sidebarRef={sidebarRef}
        />
      </div>

      {/* Modal de configurações */}
      <SettingsModal
        isOpen={settingsOpen}
        onClose={() => setSettingsOpen(false)}
        userData={userData}
        onUserDataUpdate={setUserData}
      />

      {/* Overlay mobile */}
      {sidebarOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black/50 z-40"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
}
