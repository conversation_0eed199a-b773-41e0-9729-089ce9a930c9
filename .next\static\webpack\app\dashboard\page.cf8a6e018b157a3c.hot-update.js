"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/CreateFolderModal.tsx":
/*!********************************************************!*\
  !*** ./src/components/dashboard/CreateFolderModal.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CreateFolderModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst FOLDER_COLORS = [\n    {\n        name: \"Azul\",\n        value: \"blue\",\n        bg: \"bg-blue-500\",\n        selected: \"bg-blue-600\"\n    },\n    {\n        name: \"Verde\",\n        value: \"green\",\n        bg: \"bg-green-500\",\n        selected: \"bg-green-600\"\n    },\n    {\n        name: \"Amarelo\",\n        value: \"yellow\",\n        bg: \"bg-yellow-500\",\n        selected: \"bg-yellow-600\"\n    },\n    {\n        name: \"Vermelho\",\n        value: \"red\",\n        bg: \"bg-red-500\",\n        selected: \"bg-red-600\"\n    },\n    {\n        name: \"Roxo\",\n        value: \"purple\",\n        bg: \"bg-purple-500\",\n        selected: \"bg-purple-600\"\n    },\n    {\n        name: \"Ciano\",\n        value: \"cyan\",\n        bg: \"bg-cyan-500\",\n        selected: \"bg-cyan-600\"\n    },\n    {\n        name: \"Lima\",\n        value: \"lime\",\n        bg: \"bg-lime-500\",\n        selected: \"bg-lime-600\"\n    },\n    {\n        name: \"Laranja\",\n        value: \"orange\",\n        bg: \"bg-orange-500\",\n        selected: \"bg-orange-600\"\n    },\n    {\n        name: \"Rosa\",\n        value: \"pink\",\n        bg: \"bg-pink-500\",\n        selected: \"bg-pink-600\"\n    },\n    {\n        name: \"Cinza\",\n        value: \"gray\",\n        bg: \"bg-gray-500\",\n        selected: \"bg-gray-600\"\n    }\n];\nfunction CreateFolderModal(param) {\n    let { isOpen, onClose, username, onFolderCreated, editingFolder = null } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [folderData, setFolderData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        description: \"\",\n        color: \"blue\",\n        expandedByDefault: true\n    });\n    // Carregar dados quando estiver editando\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (editingFolder) {\n            setFolderData({\n                name: editingFolder.name,\n                description: editingFolder.description || \"\",\n                color: editingFolder.color,\n                expandedByDefault: editingFolder.expandedByDefault\n            });\n        } else {\n            // Reset para criar nova pasta\n            setFolderData({\n                name: \"\",\n                description: \"\",\n                color: \"blue\",\n                expandedByDefault: true\n            });\n        }\n    }, [\n        editingFolder\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFolderData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const generateFolderId = ()=>{\n        const timestamp = Date.now();\n        const random = Math.random().toString(36).substring(2, 8);\n        return \"folder_\".concat(timestamp, \"_\").concat(random);\n    };\n    const createFolder = async ()=>{\n        if (!folderData.name.trim()) {\n            setError(\"Nome da pasta \\xe9 obrigat\\xf3rio\");\n            return;\n        }\n        setLoading(true);\n        setError(\"\");\n        try {\n            const now = new Date().toISOString();\n            if (editingFolder) {\n                // Editando pasta existente\n                const firestoreData = {\n                    name: folderData.name,\n                    description: folderData.description,\n                    color: folderData.color,\n                    expandedByDefault: folderData.expandedByDefault,\n                    updatedAt: now\n                };\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\", username, \"pastas\", editingFolder.id), firestoreData);\n                console.log(\"Pasta atualizada com sucesso:\", editingFolder.id);\n                onFolderCreated(editingFolder.id);\n            } else {\n                // Criando nova pasta\n                const folderId = generateFolderId();\n                const firestoreData = {\n                    name: folderData.name,\n                    description: folderData.description,\n                    color: folderData.color,\n                    expandedByDefault: folderData.expandedByDefault,\n                    createdAt: now,\n                    updatedAt: now,\n                    chatCount: 0\n                };\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\", username, \"pastas\", folderId), firestoreData);\n                console.log(\"Pasta criada com sucesso:\", folderId);\n                onFolderCreated(folderId);\n            }\n            onClose();\n            // Reset form apenas se não estiver editando\n            if (!editingFolder) {\n                setFolderData({\n                    name: \"\",\n                    description: \"\",\n                    color: \"blue\",\n                    expandedByDefault: true\n                });\n            }\n        } catch (error) {\n            console.error(\"Erro ao salvar pasta:\", error);\n            setError(editingFolder ? \"Erro ao atualizar pasta. Tente novamente.\" : \"Erro ao criar pasta. Tente novamente.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    const handleBackgroundClick = (e)=>{\n        // Só fechar se foi um clique real, não um evento de drag\n        if (e.type === \"click\" && e.target === e.currentTarget) {\n            onClose();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            className: \"fixed inset-0 z-50 flex items-center justify-center p-4\",\n            onClick: handleBackgroundClick,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"absolute inset-0 bg-gradient-to-br from-black/40 via-blue-900/30 to-purple-900/40 backdrop-blur-xl\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        scale: 0.8,\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        scale: 0.8,\n                        opacity: 0,\n                        y: 50\n                    },\n                    transition: {\n                        type: \"spring\",\n                        duration: 0.6,\n                        bounce: 0.3\n                    },\n                    className: \"relative bg-gradient-to-br from-slate-900/90 via-blue-900/90 to-indigo-900/90 backdrop-blur-2xl border border-white/20 rounded-3xl w-full max-w-md overflow-hidden shadow-2xl shadow-blue-900/50\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative p-6 border-b border-white/10 bg-gradient-to-r from-white/5 to-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/30\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold text-white\",\n                                                children: editingFolder ? \"Editar Pasta\" : \"Nova Pasta\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"absolute top-4 right-4 text-white/60 hover:text-white transition-all duration-300 p-2 hover:bg-white/10 rounded-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 space-y-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium text-blue-300 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-blue-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Nome da pasta\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-400 text-xs bg-red-500/20 px-2 py-1 rounded-full\",\n                                                    children: \"obrigat\\xf3rio\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: folderData.name,\n                                            onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                            placeholder: \"Digite o nome da pasta\",\n                                            className: \"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-xs\",\n                                            children: \"Nome para identificar a pasta de chats\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-blue-300 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-blue-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Descri\\xe7\\xe3o\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: folderData.description,\n                                            onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                            placeholder: \"Descri\\xe7\\xe3o opcional da pasta\",\n                                            rows: 2,\n                                            className: \"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15 resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-xs\",\n                                            children: \"Descri\\xe7\\xe3o opcional para a pasta\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-blue-300\",\n                                            children: \"Cor da pasta\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-5 gap-2\",\n                                            children: FOLDER_COLORS.map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleInputChange(\"color\", color.value),\n                                                    className: \"w-10 h-10 rounded-xl transition-all duration-200 \".concat(folderData.color === color.value ? \"\".concat(color.selected, \" ring-2 ring-white/50 scale-110\") : \"\".concat(color.bg, \" hover:scale-105\")),\n                                                    title: color.name,\n                                                    children: folderData.color === color.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-white mx-auto\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M5 13l4 4L19 7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, color.value, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/60 text-xs\",\n                                            children: \"Escolha uma cor para identificar visualmente a pasta\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-4 bg-white/5 rounded-2xl border border-white/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-blue-300\",\n                                                        children: \"Expandida por padr\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/60 text-xs mt-1\",\n                                                        children: \"A pasta ficar\\xe1 aberta quando voc\\xea acessar o chat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleInputChange(\"expandedByDefault\", !folderData.expandedByDefault),\n                                                className: \"relative w-14 h-7 rounded-full transition-all duration-300 \".concat(folderData.expandedByDefault ? \"bg-gradient-to-r from-blue-500 to-cyan-500\" : \"bg-white/20\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-1 w-5 h-5 bg-white rounded-full shadow-lg transition-all duration-300 \".concat(folderData.expandedByDefault ? \"left-8\" : \"left-1\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"p-3 bg-red-500/20 border border-red-500/30 rounded-xl text-red-300 text-sm\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-t border-white/10 bg-white/5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        disabled: loading,\n                                        className: \"px-6 py-3 text-white/70 hover:text-white transition-all duration-300 hover:bg-white/10 rounded-xl disabled:opacity-50 font-medium\",\n                                        children: \"Cancelar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: createFolder,\n                                        disabled: loading || !folderData.name.trim(),\n                                        className: \"px-8 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 font-medium shadow-lg shadow-blue-500/25 hover:shadow-blue-500/40\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: editingFolder ? \"Salvando...\" : \"Criando...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: editingFolder ? \"M5 13l4 4L19 7\" : \"M12 4v16m8-8H4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: editingFolder ? \"Salvar Altera\\xe7\\xf5es\" : \"Criar Pasta\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n            lineNumber: 166,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateFolderModal.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateFolderModal, \"FHck0ukA+/pUxZizsUr6Kx/KHRg=\");\n_c = CreateFolderModal;\nvar _c;\n$RefreshReg$(_c, \"CreateFolderModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/CreateFolderModal.tsx\n"));

/***/ })

});