import { useState, useEffect, useCallback, useMemo } from 'react';
import { AIModel } from '@/lib/types/chat';

interface SearchResult {
  model: AIModel;
  score: number;
  matchedFields: string[];
  highlightedName?: string;
  highlightedDescription?: string;
}

interface UseAdvancedSearchOptions {
  debounceMs?: number;
  enableSuggestions?: boolean;
  cacheResults?: boolean;
  fuzzyThreshold?: number;
  maxResults?: number;
  boostFavorites?: boolean;
}

interface UseAdvancedSearchReturn {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  searchResults: SearchResult[];
  suggestions: string[];
  isSearching: boolean;
  hasSearched: boolean;
  clearSearch: () => void;
}

export function useAdvancedSearch(
  models: AIModel[],
  options: UseAdvancedSearchOptions = {}
): UseAdvancedSearchReturn {
  const {
    debounceMs = 300,
    enableSuggestions = true,
    cacheResults = true,
    fuzzyThreshold = 0.6,
    maxResults = 50,
    boostFavorites = false
  } = options;

  const [searchTerm, setSearchTermState] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  // Cache para resultados de busca
  const [searchCache] = useState(new Map<string, SearchResult[]>());

  // Função de busca principal
  const performSearch = useCallback(async (term: string) => {
    if (!term.trim()) {
      setSearchResults([]);
      setHasSearched(false);
      return;
    }

    setIsSearching(true);

    try {
      // Verificar cache se habilitado
      if (cacheResults && searchCache.has(term)) {
        setSearchResults(searchCache.get(term)!);
        setHasSearched(true);
        setIsSearching(false);
        return;
      }

      // Realizar busca
      const results = searchModels(models, term, {
        fuzzyThreshold,
        maxResults,
        boostFavorites
      });

      // Salvar no cache se habilitado
      if (cacheResults) {
        searchCache.set(term, results);
      }

      setSearchResults(results);
      setHasSearched(true);
    } catch (error) {
      console.error('Error performing search:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [models, fuzzyThreshold, maxResults, boostFavorites, cacheResults, searchCache]);

  // Função para definir termo de busca com debounce
  const setSearchTerm = useCallback((term: string) => {
    setSearchTermState(term);

    // Limpar timer anterior
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // Configurar novo timer
    const timer = setTimeout(() => {
      performSearch(term);
    }, debounceMs);

    setDebounceTimer(timer);
  }, [debounceTimer, debounceMs, performSearch]);

  // Sistema de sugestões removido junto com analytics
  useEffect(() => {
    setSuggestions([]);
  }, [searchTerm, enableSuggestions]);

  // Limpar busca
  const clearSearch = useCallback(() => {
    setSearchTermState('');
    setSearchResults([]);
    setHasSearched(false);
    setSuggestions([]);
    
    if (debounceTimer) {
      clearTimeout(debounceTimer);
      setDebounceTimer(null);
    }
  }, [debounceTimer]);



  // Limpar timer ao desmontar
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  return {
    searchTerm,
    setSearchTerm,
    searchResults,
    suggestions,
    isSearching,
    hasSearched,
    clearSearch
  };
}

// Função de busca com fuzzy matching
function searchModels(
  models: AIModel[],
  searchTerm: string,
  options: {
    fuzzyThreshold?: number;
    maxResults?: number;
    boostFavorites?: boolean;
  } = {}
): SearchResult[] {
  const {
    fuzzyThreshold = 0.6,
    maxResults = 50,
    boostFavorites = false
  } = options;

  if (!searchTerm.trim()) {
    return [];
  }

  const term = searchTerm.toLowerCase();
  const results: SearchResult[] = [];

  for (const model of models) {
    let score = 0;
    const matchedFields: string[] = [];
    let highlightedName = model.name;
    let highlightedDescription = model.description || '';

    // Busca no nome (peso maior)
    if (model.name.toLowerCase().includes(term)) {
      score += 10;
      matchedFields.push('name');
      highlightedName = highlightText(model.name, term);
    }

    // Busca no ID (peso médio)
    if (model.id.toLowerCase().includes(term)) {
      score += 7;
      matchedFields.push('id');
    }

    // Busca na descrição (peso menor)
    if (model.description && model.description.toLowerCase().includes(term)) {
      score += 3;
      matchedFields.push('description');
      highlightedDescription = highlightText(model.description, term);
    }

    // Boost para favoritos
    if (boostFavorites && model.isFavorite) {
      score *= 1.5;
    }

    // Boost para modelos gratuitos se buscar por "free" ou "grátis"
    if ((term.includes('free') || term.includes('grátis')) && isModelFree(model)) {
      score += 5;
    }

    // Boost para modelos caros se buscar por "expensive" ou "caro"
    if ((term.includes('expensive') || term.includes('caro')) && getTotalPrice(model) > 0.00002) {
      score += 5;
    }

    if (score > 0) {
      results.push({
        model,
        score,
        matchedFields,
        highlightedName,
        highlightedDescription
      });
    }
  }

  // Ordenar por score e limitar resultados
  return results
    .sort((a, b) => b.score - a.score)
    .slice(0, maxResults);
}

// Função auxiliar para destacar texto
function highlightText(text: string, term: string): string {
  const regex = new RegExp(`(${term})`, 'gi');
  return text.replace(regex, '<mark class="bg-yellow-300 text-black px-1 rounded">$1</mark>');
}

// Função auxiliar para verificar se modelo é gratuito
function isModelFree(model: AIModel): boolean {
  const promptPrice = parseFloat(model.pricing.prompt);
  const completionPrice = parseFloat(model.pricing.completion);
  return promptPrice === 0 && completionPrice === 0;
}

// Função auxiliar para obter preço total
function getTotalPrice(model: AIModel): number {
  const promptPrice = parseFloat(model.pricing.prompt);
  const completionPrice = parseFloat(model.pricing.completion);
  return promptPrice + completionPrice;
}
