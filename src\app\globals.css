@tailwind base;
@tailwind components;
@tailwind utilities;

/* Scrollbar customizada */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-blue-900 {
  scrollbar-color: rgba(30, 58, 138, 0.8) transparent;
}

.scrollbar-track-transparent {
  scrollbar-track-color: transparent;
}

/* Variáveis CSS */
:root {
  --sidebar-width: 280px;
  --dashboard-bg: #0f172a;
  --sidebar-bg: rgba(67, 56, 202, 0.15);

  /* Configurações de aparência do chat - valores padrão */
  --chat-font-family: 'Inter';
  --chat-font-size: 14px;
  --chat-words-per-session: 5000;
}

:root {
  --foreground-rgb: 255, 255, 255;
  --navy-blue: #0d1c4a;
  --dark-royal-blue: #1b2e76;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(135deg, var(--navy-blue) 0%, var(--dark-royal-blue) 100%);
  min-height: 100vh;
}

.bg-gradient-rafthor {
  background: linear-gradient(135deg, #0d1c4a 0%, #1b2e76 100%);
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.8);
  }
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes buttonHover {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

/* Utility classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.message-slide-in {
  animation: messageSlideIn 0.3s ease-out;
}

.button-hover-effect:hover {
  animation: buttonHover 0.2s ease-in-out;
}

.shimmer-effect {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

.typing-indicator {
  animation: typing 1.4s infinite ease-in-out;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-border {
  position: relative;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border: 1px solid transparent;
}

/* ===== ESTILOS PROFISSIONAIS PARA MARKDOWN ===== */

.markdown-content {
  line-height: 1.7;
  color: #e2e8f0;
  font-size: var(--chat-font-size, 16px);
  font-family: var(--chat-font-family, 'Inter');
  max-width: none;
  word-wrap: break-word;
}

/* ===== HEADINGS COM GRADIENTE E ESPAÇAMENTO ELEGANTE ===== */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
  line-height: 1.3;
  background: linear-gradient(135deg, #60a5fa, #93c5fd, #dbeafe);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.markdown-content h1 {
  font-size: 2.25rem;
  margin-top: 0;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid rgba(59, 130, 246, 0.3);
}

.markdown-content h2 {
  font-size: 1.875rem;
  margin-bottom: 1.25rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.markdown-content h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.markdown-content h4 {
  font-size: 1.25rem;
  margin-bottom: 0.875rem;
}

.markdown-content h5 {
  font-size: 1.125rem;
  margin-bottom: 0.75rem;
}

.markdown-content h6 {
  font-size: 1rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

/* ===== PARÁGRAFOS COM ESPAÇAMENTO OTIMIZADO ===== */
.markdown-content p {
  margin-bottom: 1.25rem;
  line-height: 1.75;
  color: #cbd5e1;
  text-align: justify;
}

.markdown-content p:last-child {
  margin-bottom: 0;
}

/* ===== LISTAS ESTILIZADAS ===== */
.markdown-content ul,
.markdown-content ol {
  margin-bottom: 1.25rem;
  padding-left: 1.75rem;
  color: #cbd5e1;
}

.markdown-content ul {
  list-style: none;
}

.markdown-content ul li {
  position: relative;
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
}

.markdown-content ul li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.75rem;
  width: 6px;
  height: 6px;
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  border-radius: 50%;
  transform: translateY(-50%);
}

.markdown-content ol {
  list-style: none;
  counter-reset: item;
}

.markdown-content ol li {
  position: relative;
  margin-bottom: 0.5rem;
  padding-left: 2rem;
  counter-increment: item;
}

.markdown-content ol li::before {
  content: counter(item);
  position: absolute;
  left: 0;
  top: 0;
  width: 1.5rem;
  height: 1.5rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

/* ===== NESTED LISTS ===== */
.markdown-content ul ul,
.markdown-content ol ol,
.markdown-content ul ol,
.markdown-content ol ul {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.markdown-content ul ul li::before {
  width: 4px;
  height: 4px;
  background: linear-gradient(135deg, #60a5fa, #93c5fd);
}

/* ===== BLOCKQUOTES ELEGANTES ===== */
.markdown-content blockquote {
  position: relative;
  margin: 1.5rem 0;
  padding: 1.25rem 1.5rem;
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.1), rgba(59, 130, 246, 0.05));
  border-left: 4px solid #3b82f6;
  border-radius: 0 0.5rem 0.5rem 0;
  font-style: italic;
  color: #e2e8f0;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.markdown-content blockquote::before {
  content: '"';
  position: absolute;
  top: -0.5rem;
  left: 1rem;
  font-size: 3rem;
  color: #3b82f6;
  opacity: 0.3;
  font-family: serif;
}

.markdown-content blockquote p {
  margin-bottom: 0.75rem;
  text-align: left;
}

.markdown-content blockquote p:last-child {
  margin-bottom: 0;
}

/* ===== CÓDIGO INLINE E BLOCOS ===== */
.markdown-content code {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.3), rgba(59, 130, 246, 0.2));
  color: #fbbf24;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.markdown-content pre {
  background: linear-gradient(135deg, rgba(13, 28, 74, 0.8), rgba(30, 58, 138, 0.6)) !important;
  border: 1px solid rgba(59, 130, 246, 0.4);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin: 1.5rem 0;
  overflow-x: auto;
  position: relative;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.markdown-content pre::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #60a5fa, #93c5fd);
  border-radius: 0.75rem 0.75rem 0 0;
}

.markdown-content pre code {
  background: transparent !important;
  color: #e2e8f0 !important;
  padding: 0 !important;
  border: none !important;
  box-shadow: none !important;
  font-size: 0.875rem;
  line-height: 1.6;
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
}

/* ===== HIGHLIGHT.JS CUSTOMIZAÇÃO ===== */
.markdown-content .hljs {
  background: transparent !important;
  color: #e2e8f0;
  padding: 0;
}

.markdown-content .hljs-keyword {
  color: #c084fc !important;
  font-weight: 600;
}

.markdown-content .hljs-string {
  color: #34d399 !important;
}

.markdown-content .hljs-number {
  color: #fbbf24 !important;
}

.markdown-content .hljs-comment {
  color: #6b7280 !important;
  font-style: italic;
}

.markdown-content .hljs-function {
  color: #60a5fa !important;
}

.markdown-content .hljs-variable {
  color: #f87171 !important;
}

/* ===== LINKS ELEGANTES ===== */
.markdown-content a {
  color: #60a5fa;
  text-decoration: none;
  position: relative;
  font-weight: 500;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, transparent, transparent);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.markdown-content a::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  transition: width 0.3s ease;
}

.markdown-content a:hover {
  color: #93c5fd;
  background: rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.markdown-content a:hover::before {
  width: 100%;
}

/* ===== TABELAS PROFISSIONAIS ===== */
.markdown-content table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin: 1.5rem 0;
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.1), rgba(59, 130, 246, 0.05));
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.markdown-content th {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.8), rgba(59, 130, 246, 0.6));
  color: #ffffff;
  font-weight: 600;
  padding: 1rem 1.25rem;
  text-align: left;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border: none;
}

.markdown-content th:first-child {
  border-radius: 0.75rem 0 0 0;
}

.markdown-content th:last-child {
  border-radius: 0 0.75rem 0 0;
}

.markdown-content td {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  color: #cbd5e1;
  transition: background-color 0.2s ease;
}

.markdown-content tr:hover td {
  background: rgba(59, 130, 246, 0.05);
}

.markdown-content tr:last-child td {
  border-bottom: none;
}

.markdown-content tr:last-child td:first-child {
  border-radius: 0 0 0 0.75rem;
}

.markdown-content tr:last-child td:last-child {
  border-radius: 0 0 0.75rem 0;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(135deg, #3b82f6, #9333ea);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}

/* ===== LINHA HORIZONTAL ELEGANTE ===== */
.markdown-content hr {
  margin: 2.5rem 0;
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3b82f6, #60a5fa, #3b82f6, transparent);
  border-radius: 1px;
  position: relative;
}

.markdown-content hr::before {
  content: '';
  position: absolute;
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

/* ===== SCROLLBARS CUSTOMIZADAS PARA MARKDOWN ===== */
.markdown-content pre::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.markdown-content pre::-webkit-scrollbar-track {
  background: rgba(30, 58, 138, 0.3);
  border-radius: 4px;
}

.markdown-content pre::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  border-radius: 4px;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.markdown-content pre::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb, #3b82f6);
}

.markdown-content pre::-webkit-scrollbar-corner {
  background: rgba(30, 58, 138, 0.3);
}

/* ===== ELEMENTOS ESPECIAIS ===== */
.markdown-content strong {
  color: #f1f5f9;
  font-weight: 700;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), transparent);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.markdown-content em {
  color: #cbd5e1;
  font-style: italic;
  position: relative;
}

.markdown-content del {
  color: #6b7280;
  text-decoration: line-through;
  text-decoration-color: #ef4444;
}

.markdown-content mark {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.3), rgba(245, 158, 11, 0.2));
  color: #fbbf24;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* ===== IMAGENS RESPONSIVAS ===== */
.markdown-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  margin: 1.5rem 0;
  border: 1px solid rgba(59, 130, 246, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.markdown-content img:hover {
  transform: scale(1.02);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

/* ===== RESPONSIVIDADE ===== */
@media (max-width: 768px) {
  .markdown-content {
    font-size: 14px;
    line-height: 1.6;
  }

  .markdown-content h1 {
    font-size: 1.875rem;
  }

  .markdown-content h2 {
    font-size: 1.5rem;
  }

  .markdown-content h3 {
    font-size: 1.25rem;
  }

  .markdown-content pre {
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 0.5rem;
  }

  .markdown-content blockquote {
    padding: 1rem;
    margin: 1rem 0;
  }

  .markdown-content table {
    font-size: 0.875rem;
  }

  .markdown-content th,
  .markdown-content td {
    padding: 0.75rem;
  }
}

@media (max-width: 480px) {
  .markdown-content {
    font-size: 13px;
  }

  .markdown-content h1 {
    font-size: 1.5rem;
  }

  .markdown-content h2 {
    font-size: 1.25rem;
  }

  .markdown-content h3 {
    font-size: 1.125rem;
  }

  .markdown-content pre {
    padding: 0.75rem;
    font-size: 0.75rem;
  }

  .markdown-content ul,
  .markdown-content ol {
    padding-left: 1.25rem;
  }
}

/* ===== ANIMAÇÕES SUAVES ===== */
.markdown-content * {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* ===== MELHORIAS DE ACESSIBILIDADE ===== */
.markdown-content :focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 0.25rem;
}

.markdown-content a:focus {
  background: rgba(59, 130, 246, 0.2);
}

/* Scrollbar customization global */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.6);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.8);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out;
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Custom slider styles */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3B82F6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.slider::-webkit-slider-thumb:hover {
  background: #2563EB;
  transform: scale(1.1);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3B82F6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb:hover {
  background: #2563EB;
  transform: scale(1.1);
}
