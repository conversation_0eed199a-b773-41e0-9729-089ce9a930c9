"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/CreateChatModal.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/CreateChatModal.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CreateChatModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst SliderInput = (param)=>{\n    let { label, value, onChange, min, max, step, description, icon } = param;\n    const percentage = (value - min) / (max - min) * 100;\n    const getStatus = ()=>{\n        const middle = (min + max) / 2;\n        if (value < middle * 0.8) return \"baixo\";\n        if (value > middle * 1.2) return \"alto\";\n        return \"medio\";\n    };\n    const status = getStatus();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"text-sm font-medium text-blue-300 flex items-center gap-2\",\n                        children: [\n                            icon,\n                            label\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white/80 text-sm font-mono bg-white/10 px-3 py-1 rounded-lg\",\n                                children: value.toFixed(1)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs px-3 py-1 rounded-full bg-blue-500/20 text-blue-300 font-medium\",\n                                children: status === \"baixo\" ? \"Baixo\" : status === \"medio\" ? \"Padr\\xe3o\" : \"Alto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-1 bg-white/20 rounded-full relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute left-1/2 top-0 bottom-0 w-0.5 bg-white/40 transform -translate-x-1/2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"range\",\n                        min: min,\n                        max: max,\n                        step: step,\n                        value: value,\n                        onChange: (e)=>onChange(parseFloat(e.target.value)),\n                        className: \"absolute inset-0 w-full opacity-0 cursor-pointer z-10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-2 left-0 right-0 flex justify-between text-xs text-white/40\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: min\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold text-blue-300\",\n                                children: \"1.0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: max\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"absolute top-1/2 w-6 h-6 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full transform -translate-y-1/2 -translate-x-1/2 shadow-lg shadow-blue-500/30 border-2 border-white/20 cursor-pointer\",\n                        style: {\n                            left: \"\".concat(percentage, \"%\")\n                        },\n                        whileHover: {\n                            scale: 1.2\n                        },\n                        whileTap: {\n                            scale: 0.9\n                        },\n                        transition: {\n                            duration: 0.2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-1 bg-white/20 rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-white/60 text-xs leading-relaxed\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SliderInput;\nfunction CreateChatModal(param) {\n    let { isOpen, onClose, username, onChatCreated, editingChat } = param;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"geral\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [chatData, setChatData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        systemPrompt: \"\",\n        context: \"\",\n        password: \"\",\n        latexInstructions: false,\n        temperature: 1.0,\n        frequencyPenalty: 1.0,\n        repetitionPenalty: 1.0,\n        maxTokens: 2048\n    });\n    // Carregar dados do chat quando estiver editando\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (editingChat && isOpen) {\n            loadChatData();\n        } else if (!editingChat && isOpen) {\n            // Reset para valores padrão quando criar novo chat\n            setChatData({\n                name: \"\",\n                systemPrompt: \"\",\n                context: \"\",\n                password: \"\",\n                latexInstructions: false,\n                temperature: 1.0,\n                frequencyPenalty: 1.0,\n                repetitionPenalty: 1.0,\n                maxTokens: 2048\n            });\n        }\n    }, [\n        editingChat,\n        isOpen\n    ]);\n    const loadChatData = async ()=>{\n        if (!editingChat) return;\n        try {\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", editingChat.id));\n            if (chatDoc.exists()) {\n                const data = chatDoc.data();\n                setChatData({\n                    name: data.name || \"\",\n                    systemPrompt: data.systemPrompt || \"\",\n                    context: data.context || \"\",\n                    password: data.password || \"\",\n                    latexInstructions: data.latexInstructions || false,\n                    temperature: data.temperature || 1.0,\n                    frequencyPenalty: data.frequencyPenalty || 1.0,\n                    repetitionPenalty: data.repetitionPenalty || 1.0,\n                    maxTokens: data.maxTokens || 2048\n                });\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar dados do chat:\", error);\n            setError(\"Erro ao carregar dados do chat\");\n        }\n    };\n    const handleInputChange = (field, value)=>{\n        setChatData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const generateChatId = ()=>{\n        const timestamp = Date.now();\n        const random = Math.random().toString(36).substring(2, 8);\n        return \"chat_\".concat(timestamp, \"_\").concat(random);\n    };\n    const createChat = async ()=>{\n        if (!chatData.name.trim()) {\n            setError(\"Nome do chat \\xe9 obrigat\\xf3rio\");\n            return;\n        }\n        setLoading(true);\n        setError(\"\");\n        try {\n            if (editingChat) {\n                // Modo edição - atualizar chat existente\n                const now = new Date().toISOString();\n                const updateData = {\n                    context: chatData.context,\n                    frequencyPenalty: chatData.frequencyPenalty,\n                    lastUpdatedAt: now,\n                    latexInstructions: chatData.latexInstructions,\n                    maxTokens: chatData.maxTokens,\n                    name: chatData.name,\n                    password: chatData.password,\n                    repetitionPenalty: chatData.repetitionPenalty,\n                    systemPrompt: chatData.systemPrompt,\n                    temperature: chatData.temperature,\n                    updatedAt: now\n                };\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", editingChat.id), updateData);\n                console.log(\"Chat atualizado com sucesso:\", editingChat.id);\n                onChatCreated(editingChat.id);\n                onClose();\n            } else {\n                // Modo criação - criar novo chat\n                const chatId = generateChatId();\n                const now = new Date().toISOString();\n                // Dados para o Firestore\n                const firestoreData = {\n                    context: chatData.context,\n                    createdAt: now,\n                    folderId: null,\n                    frequencyPenalty: chatData.frequencyPenalty,\n                    isFixed: false,\n                    lastUpdatedAt: now,\n                    lastUsedModel: \"\",\n                    latexInstructions: chatData.latexInstructions,\n                    maxTokens: chatData.maxTokens,\n                    name: chatData.name,\n                    password: chatData.password,\n                    repetitionPenalty: chatData.repetitionPenalty,\n                    sessionTime: {\n                        lastSessionStart: null,\n                        lastUpdated: null,\n                        totalTime: 0\n                    },\n                    systemPrompt: chatData.systemPrompt,\n                    temperature: chatData.temperature,\n                    ultimaMensagem: \"\",\n                    ultimaMensagemEm: null,\n                    updatedAt: now\n                };\n                // Criar documento no Firestore\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n                // Criar arquivo chat.json no Storage\n                const chatJsonData = {\n                    id: chatId,\n                    name: chatData.name,\n                    messages: [],\n                    createdAt: now,\n                    lastUpdated: now\n                };\n                const chatJsonBlob = new Blob([\n                    JSON.stringify(chatJsonData, null, 2)\n                ], {\n                    type: \"application/json\"\n                });\n                const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n                await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n                console.log(\"Chat criado com sucesso:\", chatId);\n                onChatCreated(chatId);\n                onClose();\n                // Reset form\n                setChatData({\n                    name: \"\",\n                    systemPrompt: \"\",\n                    context: \"\",\n                    password: \"\",\n                    latexInstructions: false,\n                    temperature: 1.0,\n                    frequencyPenalty: 1.0,\n                    repetitionPenalty: 1.0,\n                    maxTokens: 2048\n                });\n            }\n        } catch (error) {\n            console.error(\"Erro ao processar chat:\", error);\n            setError(editingChat ? \"Erro ao atualizar conversa. Tente novamente.\" : \"Erro ao criar conversa. Tente novamente.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    const handleBackgroundClick = (e)=>{\n        // Só fechar se foi um clique real, não um evento de drag\n        if (e.type === \"click\" && e.target === e.currentTarget) {\n            onClose();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            className: \"fixed inset-0 z-50 flex items-center justify-center p-4\",\n            onClick: handleBackgroundClick,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"absolute inset-0 bg-gradient-to-br from-black/40 via-blue-900/30 to-purple-900/40 backdrop-blur-xl\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                    children: [\n                        ...Array(20)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            className: \"absolute w-2 h-2 bg-blue-400/20 rounded-full\",\n                            initial: {\n                                x: Math.random() * window.innerWidth,\n                                y: Math.random() * window.innerHeight,\n                                scale: 0\n                            },\n                            animate: {\n                                y: [\n                                    null,\n                                    -100\n                                ],\n                                scale: [\n                                    0,\n                                    1,\n                                    0\n                                ],\n                                opacity: [\n                                    0,\n                                    0.6,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: Math.random() * 3 + 2,\n                                repeat: Infinity,\n                                delay: Math.random() * 2\n                            }\n                        }, i, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        scale: 0.8,\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        scale: 0.8,\n                        opacity: 0,\n                        y: 50\n                    },\n                    transition: {\n                        type: \"spring\",\n                        duration: 0.6,\n                        bounce: 0.3\n                    },\n                    className: \"relative bg-gradient-to-br from-slate-900/90 via-blue-900/90 to-indigo-900/90 backdrop-blur-2xl border border-white/20 rounded-3xl w-full max-w-2xl max-h-[90vh] overflow-hidden shadow-2xl shadow-blue-900/50\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/50 to-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 bottom-0 left-0 w-px bg-gradient-to-b from-transparent via-white/30 to-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 bottom-0 right-0 w-px bg-gradient-to-b from-transparent via-white/30 to-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            className: \"relative p-6 border-b border-white/10 bg-gradient-to-r from-white/5 to-transparent\",\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            className: \"relative w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/30\",\n                                            whileHover: {\n                                                scale: 1.1,\n                                                rotate: 5\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.svg, {\n                                                    className: \"w-6 h-6 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    initial: {\n                                                        scale: 0\n                                                    },\n                                                    animate: {\n                                                        scale: 1\n                                                    },\n                                                    transition: {\n                                                        delay: 0.4,\n                                                        type: \"spring\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-white/20 rounded-2xl blur-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h2, {\n                                                    className: \"text-2xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.3\n                                                    },\n                                                    children: editingChat ? \"Editar Conversa\" : \"Nova Conversa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                                                    className: \"text-white/70 text-sm mt-1\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.4\n                                                    },\n                                                    children: editingChat ? \"Altere as configura\\xe7\\xf5es da conversa\" : \"Configure sua nova conversa com IA\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                    onClick: onClose,\n                                    className: \"absolute top-4 right-4 text-white/60 hover:text-white transition-all duration-300 p-3 hover:bg-white/10 rounded-2xl group\",\n                                    whileHover: {\n                                        scale: 1.1,\n                                        rotate: 90\n                                    },\n                                    whileTap: {\n                                        scale: 0.9\n                                    },\n                                    transition: {\n                                        duration: 0.2\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 transition-transform duration-300 group-hover:rotate-90\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            className: \"flex bg-gradient-to-r from-white/10 to-white/5 mx-6 mt-6 rounded-2xl p-1 backdrop-blur-sm border border-white/10\",\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                    onClick: ()=>setActiveTab(\"geral\"),\n                                    className: \"flex-1 flex items-center justify-center space-x-2 px-6 py-4 rounded-xl transition-all duration-500 relative overflow-hidden \".concat(activeTab === \"geral\" ? \"text-white shadow-xl\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: [\n                                        activeTab === \"geral\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl\",\n                                            layoutId: \"activeTab\",\n                                            transition: {\n                                                type: \"spring\",\n                                                bounce: 0.2,\n                                                duration: 0.6\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.svg, {\n                                            className: \"w-5 h-5 relative z-10\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            animate: {\n                                                rotate: activeTab === \"geral\" ? 360 : 0\n                                            },\n                                            transition: {\n                                                duration: 0.5\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold relative z-10\",\n                                            children: \"Geral\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                    onClick: ()=>setActiveTab(\"avancado\"),\n                                    className: \"flex-1 flex items-center justify-center space-x-2 px-6 py-4 rounded-xl transition-all duration-500 relative overflow-hidden \".concat(activeTab === \"avancado\" ? \"text-white shadow-xl\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: [\n                                        activeTab === \"avancado\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl\",\n                                            layoutId: \"activeTab\",\n                                            transition: {\n                                                type: \"spring\",\n                                                bounce: 0.2,\n                                                duration: 0.6\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.svg, {\n                                            className: \"w-5 h-5 relative z-10\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            animate: {\n                                                rotate: activeTab === \"avancado\" ? 360 : 0\n                                            },\n                                            transition: {\n                                                duration: 0.5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold relative z-10\",\n                                            children: \"Avan\\xe7ado\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 overflow-y-auto max-h-[60vh]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: [\n                                        activeTab === \"geral\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            className: \"space-y-5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-blue-300 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 text-blue-400\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: \"2\",\n                                                                                d: \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                                lineNumber: 532,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                            lineNumber: 531,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Nome do chat\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                    lineNumber: 530,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-400 text-xs bg-red-500/20 px-2 py-1 rounded-full\",\n                                                                    children: \"obrigat\\xf3rio\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: chatData.name,\n                                                            onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                                            placeholder: \"Ex: Projeto de f\\xedsica\",\n                                                            className: \"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-white/60 text-xs\",\n                                                            children: \"Nome obrigat\\xf3rio para identificar a conversa\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-blue-300 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 text-blue-400\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: \"2\",\n                                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                                lineNumber: 559,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"System Prompt\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-400 text-xs bg-blue-500/20 px-2 py-1 rounded-full\",\n                                                                    children: \"opcional\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: chatData.systemPrompt,\n                                                            onChange: (e)=>handleInputChange(\"systemPrompt\", e.target.value),\n                                                            placeholder: \"Instru\\xe7\\xf5es para o comportamento da IA...\",\n                                                            rows: 3,\n                                                            className: \"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15 resize-none\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-white/60 text-xs\",\n                                                            children: 'Define como a IA deve se comportar e responder (ex: \"Seja um assistente especializado em matem\\xe1tica\")'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-blue-300 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 text-blue-400\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: \"2\",\n                                                                                d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                                lineNumber: 584,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                            lineNumber: 583,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Contexto\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-400 text-xs bg-blue-500/20 px-2 py-1 rounded-full\",\n                                                                    children: \"opcional\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            value: chatData.context,\n                                                            onChange: (e)=>handleInputChange(\"context\", e.target.value),\n                                                            placeholder: \"Informa\\xe7\\xf5es adicionais de contexto para a conversa...\",\n                                                            rows: 3,\n                                                            className: \"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15 resize-none\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-white/60 text-xs\",\n                                                            children: \"Informa\\xe7\\xf5es de fundo que a IA deve considerar durante toda a conversa\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-blue-300 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 text-blue-400\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"2\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                                    width: \"18\",\n                                                                                    height: \"11\",\n                                                                                    x: \"3\",\n                                                                                    y: \"11\",\n                                                                                    rx: \"2\",\n                                                                                    ry: \"2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                                    lineNumber: 609,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M7 11V7a5 5 0 0 1 10 0v4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                                    lineNumber: 610,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                            lineNumber: 608,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Senha do Chat\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                    lineNumber: 607,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-400 text-xs bg-blue-500/20 px-2 py-1 rounded-full\",\n                                                                    children: \"opcional\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"password\",\n                                                            value: chatData.password,\n                                                            onChange: (e)=>handleInputChange(\"password\", e.target.value),\n                                                            placeholder: \"Deixe vazio para chat sem prote\\xe7\\xe3o\",\n                                                            className: \"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-white/60 text-xs\",\n                                                            children: \"Se definida, ser\\xe1 necess\\xe1rio inserir a senha para acessar este chat\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 625,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-4 bg-white/5 rounded-2xl border border-white/10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-medium text-blue-300 flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-4 h-4 text-blue-400\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: \"2\",\n                                                                                    d: \"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                                    lineNumber: 636,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                                lineNumber: 635,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Instru\\xe7\\xf5es LaTeX\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                        lineNumber: 634,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white/60 text-xs mt-1\",\n                                                                        children: \"Habilita formata\\xe7\\xe3o matem\\xe1tica avan\\xe7ada\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                        lineNumber: 640,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleInputChange(\"latexInstructions\", !chatData.latexInstructions),\n                                                                className: \"relative w-14 h-7 rounded-full transition-all duration-300 \".concat(chatData.latexInstructions ? \"bg-gradient-to-r from-blue-500 to-cyan-500\" : \"bg-white/20\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute top-1 w-5 h-5 bg-white rounded-full shadow-lg transition-all duration-300 \".concat(chatData.latexInstructions ? \"left-8\" : \"left-1\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                    lineNumber: 650,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, \"geral\", true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 17\n                                        }, this),\n                                        activeTab === \"avancado\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SliderInput, {\n                                                    label: \"Temperatura\",\n                                                    value: chatData.temperature,\n                                                    onChange: (value)=>handleInputChange(\"temperature\", value),\n                                                    min: 0,\n                                                    max: 2,\n                                                    step: 0.1,\n                                                    description: \"Controla a criatividade das respostas. Esquerda = mais preciso (0.1-0.8), Centro = balanceado (1.0), Direita = mais criativo (1.2-2.0).\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-blue-400\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M12.963 2.286a.75.75 0 0 0-1.071-.136 9.742 9.742 0 0 0-3.539 6.176 7.547 7.547 0 0 1-1.705-1.715.75.75 0 0 0-1.152-.082A9 9 0 1 0 15.68 4.534a7.46 7.46 0 0 1-2.717-2.248ZM15.75 14.25a3.75 3.75 0 1 1-7.313-1.172c.628.465 1.35.81 2.133 1a5.99 5.99 0 0 1 1.925-3.546 3.75 3.75 0 0 1 3.255 3.718Z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, void 0, void 0)\n                                                    }, void 0, false, void 0, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SliderInput, {\n                                                    label: \"Frequency Penalty\",\n                                                    value: chatData.frequencyPenalty,\n                                                    onChange: (value)=>handleInputChange(\"frequencyPenalty\", value),\n                                                    min: 0,\n                                                    max: 2,\n                                                    step: 0.1,\n                                                    description: \"Reduz repeti\\xe7\\xe3o de palavras. Esquerda = sem penalidade (0.0-0.8), Centro = padr\\xe3o (1.0), Direita = alta penalidade (1.2-2.0).\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-blue-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                        }, void 0, false, void 0, void 0)\n                                                    }, void 0, false, void 0, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                    lineNumber: 687,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SliderInput, {\n                                                    label: \"Repetition Penalty\",\n                                                    value: chatData.repetitionPenalty,\n                                                    onChange: (value)=>handleInputChange(\"repetitionPenalty\", value),\n                                                    min: 0,\n                                                    max: 2,\n                                                    step: 0.1,\n                                                    description: \"Penaliza tokens repetidos. Esquerda = sem penalidade (0.0-0.8), Centro = padr\\xe3o (1.0), Direita = alta penalidade (1.2-2.0).\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-blue-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                                                        }, void 0, false, void 0, void 0)\n                                                    }, void 0, false, void 0, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium text-blue-300 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 text-blue-400\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: \"2\",\n                                                                                d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                                lineNumber: 723,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                            lineNumber: 722,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Limite de Tokens\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                    lineNumber: 721,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white/80 text-sm font-mono bg-white/10 px-3 py-1 rounded-lg\",\n                                                                    children: chatData.maxTokens\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                                    lineNumber: 727,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 720,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: chatData.maxTokens,\n                                                            onChange: (e)=>handleInputChange(\"maxTokens\", parseInt(e.target.value) || 2048),\n                                                            min: 512,\n                                                            max: 8192,\n                                                            step: 256,\n                                                            className: \"w-full bg-white/10 border border-white/20 rounded-2xl px-4 py-3 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300 hover:bg-white/15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 732,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-white/60 text-xs leading-relaxed\",\n                                                            children: \"M\\xe1ximo de tokens que a IA pode gerar por resposta. Valores t\\xedpicos: 512-4096.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 744,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                    lineNumber: 719,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, \"avancado\", true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                            lineNumber: 662,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 13\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 10\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-xl text-red-300 text-sm\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            className: \"p-6 border-t border-white/10 bg-gradient-to-r from-white/5 to-transparent backdrop-blur-sm\",\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.6\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                        onClick: onClose,\n                                        disabled: loading,\n                                        className: \"px-8 py-4 text-white/70 hover:text-white transition-all duration-500 hover:bg-white/10 rounded-2xl disabled:opacity-50 font-semibold border border-white/20 hover:border-white/30 backdrop-blur-sm\",\n                                        whileHover: {\n                                            scale: 1.05,\n                                            y: -2\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        children: \"Cancelar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                        lineNumber: 772,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                        onClick: createChat,\n                                        disabled: loading || !chatData.name.trim(),\n                                        className: \"px-10 py-4 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white rounded-2xl transition-all duration-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-3 font-semibold shadow-xl shadow-blue-500/30 hover:shadow-blue-500/50 border border-blue-400/30 hover:border-blue-400/50 backdrop-blur-sm relative overflow-hidden\",\n                                        whileHover: {\n                                            scale: 1.05,\n                                            y: -2\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 17\n                                            }, this),\n                                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                        className: \"w-5 h-5 border-2 border-white/30 border-t-white rounded-full\",\n                                                        animate: {\n                                                            rotate: 360\n                                                        },\n                                                        transition: {\n                                                            duration: 1,\n                                                            repeat: Infinity,\n                                                            ease: \"linear\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                        lineNumber: 800,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: editingChat ? \"Salvando...\" : \"Criando...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                        lineNumber: 805,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.svg, {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        whileHover: {\n                                                            rotate: editingChat ? 0 : 90\n                                                        },\n                                                        transition: {\n                                                            duration: 0.3\n                                                        },\n                                                        children: editingChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M5 13l4 4L19 7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 818,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 4v16m8-8H4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                            lineNumber: 820,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                        lineNumber: 809,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: editingChat ? \"Salvar Altera\\xe7\\xf5es\" : \"Criar Conversa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                                lineNumber: 771,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                            lineNumber: 765,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n            lineNumber: 324,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\CreateChatModal.tsx\",\n        lineNumber: 323,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateChatModal, \"95/P6dPgjCogJx/ZEKzsmK8XE8E=\");\n_c1 = CreateChatModal;\nvar _c, _c1;\n$RefreshReg$(_c, \"SliderInput\");\n$RefreshReg$(_c1, \"CreateChatModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/CreateChatModal.tsx\n"));

/***/ })

});