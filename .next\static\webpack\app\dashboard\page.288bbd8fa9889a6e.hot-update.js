"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useAdvancedSearch.ts":
/*!****************************************!*\
  !*** ./src/hooks/useAdvancedSearch.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAdvancedSearch: function() { return /* binding */ useAdvancedSearch; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useAdvancedSearch(models) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { debounceMs = 300, enableSuggestions = true, cacheResults = true, fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n    const [searchTerm, setSearchTermState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [hasSearched, setHasSearched] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [debounceTimer, setDebounceTimer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Cache para resultados de busca\n    const [searchCache] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Map());\n    // Função de busca principal\n    const performSearch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (term)=>{\n        if (!term.trim()) {\n            setSearchResults([]);\n            setHasSearched(false);\n            return;\n        }\n        setIsSearching(true);\n        try {\n            // Verificar cache se habilitado\n            if (cacheResults && searchCache.has(term)) {\n                setSearchResults(searchCache.get(term));\n                setHasSearched(true);\n                setIsSearching(false);\n                return;\n            }\n            // Realizar busca\n            const results = searchModels(models, term, {\n                fuzzyThreshold,\n                maxResults,\n                boostFavorites\n            });\n            // Salvar no cache se habilitado\n            if (cacheResults) {\n                searchCache.set(term, results);\n            }\n            setSearchResults(results);\n            setHasSearched(true);\n        } catch (error) {\n            console.error(\"Error performing search:\", error);\n            setSearchResults([]);\n        } finally{\n            setIsSearching(false);\n        }\n    }, [\n        models,\n        fuzzyThreshold,\n        maxResults,\n        boostFavorites,\n        cacheResults,\n        searchCache\n    ]);\n    // Função para definir termo de busca com debounce\n    const setSearchTerm = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((term)=>{\n        setSearchTermState(term);\n        // Limpar timer anterior\n        if (debounceTimer) {\n            clearTimeout(debounceTimer);\n        }\n        // Configurar novo timer\n        const timer = setTimeout(()=>{\n            performSearch(term);\n        }, debounceMs);\n        setDebounceTimer(timer);\n    }, [\n        debounceTimer,\n        debounceMs,\n        performSearch\n    ]);\n    // Sistema de sugestões removido junto com analytics\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setSuggestions([]);\n    }, [\n        searchTerm,\n        enableSuggestions\n    ]);\n    // Limpar busca\n    const clearSearch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setSearchTermState(\"\");\n        setSearchResults([]);\n        setHasSearched(false);\n        setSuggestions([]);\n        if (debounceTimer) {\n            clearTimeout(debounceTimer);\n            setDebounceTimer(null);\n        }\n    }, [\n        debounceTimer\n    ]);\n    // Função vazia para manter compatibilidade\n    const trackModelSelection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (modelId)=>{\n    // Sistema de analytics removido\n    }, []);\n    // Limpar timer ao desmontar\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            if (debounceTimer) {\n                clearTimeout(debounceTimer);\n            }\n        };\n    }, [\n        debounceTimer\n    ]);\n    return {\n        searchTerm,\n        setSearchTerm,\n        searchResults,\n        suggestions,\n        isSearching,\n        hasSearched,\n        clearSearch,\n        trackModelSelection\n    };\n}\n// Função de busca com fuzzy matching\nfunction searchModels(models, searchTerm) {\n    let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n    const { fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n    if (!searchTerm.trim()) {\n        return [];\n    }\n    const term = searchTerm.toLowerCase();\n    const results = [];\n    for (const model of models){\n        let score = 0;\n        const matchedFields = [];\n        let highlightedName = model.name;\n        let highlightedDescription = model.description || \"\";\n        // Busca no nome (peso maior)\n        if (model.name.toLowerCase().includes(term)) {\n            score += 10;\n            matchedFields.push(\"name\");\n            highlightedName = highlightText(model.name, term);\n        }\n        // Busca no ID (peso médio)\n        if (model.id.toLowerCase().includes(term)) {\n            score += 7;\n            matchedFields.push(\"id\");\n        }\n        // Busca na descrição (peso menor)\n        if (model.description && model.description.toLowerCase().includes(term)) {\n            score += 3;\n            matchedFields.push(\"description\");\n            highlightedDescription = highlightText(model.description, term);\n        }\n        // Boost para favoritos\n        if (boostFavorites && model.isFavorite) {\n            score *= 1.5;\n        }\n        // Boost para modelos gratuitos se buscar por \"free\" ou \"grátis\"\n        if ((term.includes(\"free\") || term.includes(\"gr\\xe1tis\")) && isModelFree(model)) {\n            score += 5;\n        }\n        // Boost para modelos caros se buscar por \"expensive\" ou \"caro\"\n        if ((term.includes(\"expensive\") || term.includes(\"caro\")) && getTotalPrice(model) > 0.00002) {\n            score += 5;\n        }\n        if (score > 0) {\n            results.push({\n                model,\n                score,\n                matchedFields,\n                highlightedName,\n                highlightedDescription\n            });\n        }\n    }\n    // Ordenar por score e limitar resultados\n    return results.sort((a, b)=>b.score - a.score).slice(0, maxResults);\n}\n// Função auxiliar para destacar texto\nfunction highlightText(text, term) {\n    const regex = new RegExp(\"(\".concat(term, \")\"), \"gi\");\n    return text.replace(regex, '<mark class=\"bg-yellow-300 text-black px-1 rounded\">$1</mark>');\n}\n// Função auxiliar para verificar se modelo é gratuito\nfunction isModelFree(model) {\n    const promptPrice = parseFloat(model.pricing.prompt);\n    const completionPrice = parseFloat(model.pricing.completion);\n    return promptPrice === 0 && completionPrice === 0;\n}\n// Função auxiliar para obter preço total\nfunction getTotalPrice(model) {\n    const promptPrice = parseFloat(model.pricing.prompt);\n    const completionPrice = parseFloat(model.pricing.completion);\n    return promptPrice + completionPrice;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\n"));

/***/ })

});