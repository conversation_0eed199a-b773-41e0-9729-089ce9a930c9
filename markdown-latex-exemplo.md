# RafthorIA - Demonstração LaTeX com Tema Azul

Este documento demonstra as melhorias implementadas no sistema de renderização LaTeX do RafthorIA, seguindo a identidade visual azul elegante.

## Fórmulas Inline Aprimoradas

As fórmulas inline agora possuem fundo azul sutil e efeitos hover elegantes:

A famosa equação de Einstein $E = mc^2$ demonstra a equivalência massa-energia. A fórmula quadrática $x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}$ resolve equações de segundo grau. O teorema de Pitágoras $a^2 + b^2 = c^2$ é fundamental na geometria.

### Exemplos de Fórmulas Inline

- Área do círculo: $A = \pi r^2$
- Lei dos cossenos: $c^2 = a^2 + b^2 - 2ab\cos(C)$
- Derivada do seno: $\frac{d}{dx}\sin(x) = \cos(x)$
- Integral exponencial: $\int e^x dx = e^x + C$
- Limite fundamental: $\lim_{x \to 0} \frac{\sin(x)}{x} = 1$

## Blocos LaTeX Centralizados e Elegantes

### Equações Fundamentais

A equação de Schrödinger independente do tempo:

$$\hat{H}\psi = E\psi$$

A transformada de Fourier:

$$F(\omega) = \int_{-\infty}^{\infty} f(t) e^{-i\omega t} dt$$

### Cálculo Diferencial e Integral

A regra da cadeia:

$$\frac{d}{dx}[f(g(x))] = f'(g(x)) \cdot g'(x)$$

Integração por partes:

$$\int u \, dv = uv - \int v \, du$$

O teorema fundamental do cálculo:

$$\int_a^b f'(x) dx = f(b) - f(a)$$

### Álgebra Linear

Determinante de uma matriz 2x2:

$$\det\begin{pmatrix} a & b \\ c & d \end{pmatrix} = ad - bc$$

Produto escalar de vetores:

$$\vec{u} \cdot \vec{v} = |\vec{u}||\vec{v}|\cos(\theta)$$

### Estatística e Probabilidade

Distribuição normal:

$$f(x) = \frac{1}{\sigma\sqrt{2\pi}} e^{-\frac{1}{2}\left(\frac{x-\mu}{\sigma}\right)^2}$$

Teorema de Bayes:

$$P(A|B) = \frac{P(B|A) \cdot P(A)}{P(B)}$$

### Séries e Sequências

Série de Taylor:

$$f(x) = \sum_{n=0}^{\infty} \frac{f^{(n)}(a)}{n!}(x-a)^n$$

Série geométrica:

$$\sum_{n=0}^{\infty} ar^n = \frac{a}{1-r} \quad \text{para } |r| < 1$$

### Equações Diferenciais

Equação diferencial de primeira ordem:

$$\frac{dy}{dx} + P(x)y = Q(x)$$

Equação de onda:

$$\frac{\partial^2 u}{\partial t^2} = c^2 \frac{\partial^2 u}{\partial x^2}$$

### Equações Alinhadas

Derivadas sucessivas:

$$\begin{align}
f(x) &= x^3 + 2x^2 + x + 1 \\
f'(x) &= 3x^2 + 4x + 1 \\
f''(x) &= 6x + 4 \\
f'''(x) &= 6
\end{align}$$

### Geometria e Trigonometria

Lei dos senos:

$$\frac{a}{\sin(A)} = \frac{b}{\sin(B)} = \frac{c}{\sin(C)}$$

Identidade trigonométrica fundamental:

$$\sin^2(x) + \cos^2(x) = 1$$

### Números Complexos

Fórmula de Euler:

$$e^{i\theta} = \cos(\theta) + i\sin(\theta)$$

Forma polar de números complexos:

$$z = r(\cos(\theta) + i\sin(\theta)) = re^{i\theta}$$

### Sistemas de Equações

Sistema linear 2x2 usando array:

$$\left\{
\begin{array}{l}
ax + by = e \\
cx + dy = f
\end{array}
\right.$$

Sistema usando cases:

$$\begin{cases}
ax + by = e \\
cx + dy = f
\end{cases}$$

Solução usando determinantes (Regra de Cramer):

$$x = \frac{\begin{vmatrix} e & b \\ f & d \end{vmatrix}}{\begin{vmatrix} a & b \\ c & d \end{vmatrix}}, \quad y = \frac{\begin{vmatrix} a & e \\ c & f \end{vmatrix}}{\begin{vmatrix} a & b \\ c & d \end{vmatrix}}$$

## Características do Novo Design LaTeX

### Melhorias Visuais

- **Fórmulas Inline**: Fundo azul gradiente com bordas arredondadas
- **Blocos Centralizados**: Automaticamente centralizados na página
- **Efeitos Hover**: Animações suaves ao passar o mouse
- **Bordas Animadas**: Brilho sutil nas bordas dos blocos
- **Paleta Azul**: Cores consistentes com a identidade RafthorIA

### Elementos Coloridos

- **Operadores**: Azul claro (`#60a5fa`) para +, -, =, <, >
- **Funções**: Azul médio (`#93c5fd`) para sin, cos, log
- **Delimitadores**: Azul escuro (`#3b82f6`) para parênteses
- **Integrais**: Azul forte para símbolos de integração
- **Frações**: Linha divisória azul elegante

### Responsividade

- **Mobile**: Tamanho reduzido e padding otimizado
- **Tablet**: Largura ajustada para melhor visualização
- **Desktop**: Experiência completa com todos os efeitos

---

## Conclusão

O novo sistema LaTeX do RafthorIA oferece uma experiência matemática elegante e moderna, mantendo a legibilidade enquanto adiciona sofisticação visual. A centralização automática e os efeitos visuais criam uma apresentação profissional para conteúdo matemático e científico.
