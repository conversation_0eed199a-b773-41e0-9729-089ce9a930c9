"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useAdvancedSearch.ts":
/*!****************************************!*\
  !*** ./src/hooks/useAdvancedSearch.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAdvancedSearch: function() { return /* binding */ useAdvancedSearch; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useAdvancedSearch(models) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { debounceMs = 300, enableSuggestions = true, cacheResults = true, fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n    const [searchTerm, setSearchTermState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [hasSearched, setHasSearched] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [debounceTimer, setDebounceTimer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Cache para resultados de busca\n    const [searchCache] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Map());\n    // Função de busca principal\n    const performSearch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (term)=>{\n        if (!term.trim()) {\n            setSearchResults([]);\n            setHasSearched(false);\n            return;\n        }\n        setIsSearching(true);\n        try {\n            // Verificar cache se habilitado\n            if (cacheResults && searchCache.has(term)) {\n                setSearchResults(searchCache.get(term));\n                setHasSearched(true);\n                setIsSearching(false);\n                return;\n            }\n            // Realizar busca\n            const results = searchModels(models, term, {\n                fuzzyThreshold,\n                maxResults,\n                boostFavorites\n            });\n            // Salvar no cache se habilitado\n            if (cacheResults) {\n                searchCache.set(term, results);\n            }\n            setSearchResults(results);\n            setHasSearched(true);\n        } catch (error) {\n            console.error(\"Error performing search:\", error);\n            setSearchResults([]);\n        } finally{\n            setIsSearching(false);\n        }\n    }, [\n        models,\n        fuzzyThreshold,\n        maxResults,\n        boostFavorites,\n        cacheResults,\n        searchCache\n    ]);\n    // Função para definir termo de busca com debounce\n    const setSearchTerm = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((term)=>{\n        setSearchTermState(term);\n        // Limpar timer anterior\n        if (debounceTimer) {\n            clearTimeout(debounceTimer);\n        }\n        // Configurar novo timer\n        const timer = setTimeout(()=>{\n            performSearch(term);\n        }, debounceMs);\n        setDebounceTimer(timer);\n    }, [\n        debounceTimer,\n        debounceMs,\n        performSearch\n    ]);\n    // Sistema de sugestões removido junto com analytics\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setSuggestions([]);\n    }, [\n        searchTerm,\n        enableSuggestions\n    ]);\n    // Limpar busca\n    const clearSearch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setSearchTermState(\"\");\n        setSearchResults([]);\n        setHasSearched(false);\n        setSuggestions([]);\n        if (debounceTimer) {\n            clearTimeout(debounceTimer);\n            setDebounceTimer(null);\n        }\n    }, [\n        debounceTimer\n    ]);\n    // Limpar timer ao desmontar\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            if (debounceTimer) {\n                clearTimeout(debounceTimer);\n            }\n        };\n    }, [\n        debounceTimer\n    ]);\n    return {\n        searchTerm,\n        setSearchTerm,\n        searchResults,\n        suggestions,\n        isSearching,\n        hasSearched,\n        clearSearch\n    };\n}\n// Função de busca com fuzzy matching\nfunction searchModels(models, searchTerm) {\n    let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n    const { fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n    if (!searchTerm.trim()) {\n        return [];\n    }\n    const term = searchTerm.toLowerCase();\n    const results = [];\n    for (const model of models){\n        let score = 0;\n        const matchedFields = [];\n        let highlightedName = model.name;\n        let highlightedDescription = model.description || \"\";\n        // Busca no nome (peso maior)\n        if (model.name.toLowerCase().includes(term)) {\n            score += 10;\n            matchedFields.push(\"name\");\n            highlightedName = highlightText(model.name, term);\n        }\n        // Busca no ID (peso médio)\n        if (model.id.toLowerCase().includes(term)) {\n            score += 7;\n            matchedFields.push(\"id\");\n        }\n        // Busca na descrição (peso menor)\n        if (model.description && model.description.toLowerCase().includes(term)) {\n            score += 3;\n            matchedFields.push(\"description\");\n            highlightedDescription = highlightText(model.description, term);\n        }\n        // Boost para favoritos\n        if (boostFavorites && model.isFavorite) {\n            score *= 1.5;\n        }\n        // Boost para modelos gratuitos se buscar por \"free\" ou \"grátis\"\n        if ((term.includes(\"free\") || term.includes(\"gr\\xe1tis\")) && isModelFree(model)) {\n            score += 5;\n        }\n        // Boost para modelos caros se buscar por \"expensive\" ou \"caro\"\n        if ((term.includes(\"expensive\") || term.includes(\"caro\")) && getTotalPrice(model) > 0.00002) {\n            score += 5;\n        }\n        if (score > 0) {\n            results.push({\n                model,\n                score,\n                matchedFields,\n                highlightedName,\n                highlightedDescription\n            });\n        }\n    }\n    // Ordenar por score e limitar resultados\n    return results.sort((a, b)=>b.score - a.score).slice(0, maxResults);\n}\n// Função auxiliar para destacar texto\nfunction highlightText(text, term) {\n    const regex = new RegExp(\"(\".concat(term, \")\"), \"gi\");\n    return text.replace(regex, '<mark class=\"bg-yellow-300 text-black px-1 rounded\">$1</mark>');\n}\n// Função auxiliar para verificar se modelo é gratuito\nfunction isModelFree(model) {\n    const promptPrice = parseFloat(model.pricing.prompt);\n    const completionPrice = parseFloat(model.pricing.completion);\n    return promptPrice === 0 && completionPrice === 0;\n}\n// Função auxiliar para obter preço total\nfunction getTotalPrice(model) {\n    const promptPrice = parseFloat(model.pricing.prompt);\n    const completionPrice = parseFloat(model.pricing.completion);\n    return promptPrice + completionPrice;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\n"));

/***/ })

});