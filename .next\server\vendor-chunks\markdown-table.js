"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/markdown-table";
exports.ids = ["vendor-chunks/markdown-table"];
exports.modules = {

/***/ "(ssr)/./node_modules/markdown-table/index.js":
/*!**********************************************!*\
  !*** ./node_modules/markdown-table/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   markdownTable: () => (/* binding */ markdownTable)\n/* harmony export */ });\n// To do: next major: remove.\n/**\n * @typedef {Options} MarkdownTableOptions\n *   Configuration.\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [alignDelimiters=true]\n *   Whether to align the delimiters (default: `true`);\n *   they are aligned by default:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   Pass `false` to make them staggered:\n *\n *   ```markdown\n *   | Alpha | B |\n *   | - | - |\n *   | C | Delta |\n *   ```\n * @property {ReadonlyArray<string | null | undefined> | string | null | undefined} [align]\n *   How to align columns (default: `''`);\n *   one style for all columns or styles for their respective columns;\n *   each style is either `'l'` (left), `'r'` (right), or `'c'` (center);\n *   other values are treated as `''`, which doesn’t place the colon in the\n *   alignment row but does align left;\n *   *only the lowercased first character is used, so `Right` is fine.*\n * @property {boolean | null | undefined} [delimiterEnd=true]\n *   Whether to end each row with the delimiter (default: `true`).\n *\n *   > 👉 **Note**: please don’t use this: it could create fragile structures\n *   > that aren’t understandable to some markdown parsers.\n *\n *   When `true`, there are ending delimiters:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there are no ending delimiters:\n *\n *   ```markdown\n *   | Alpha | B\n *   | ----- | -----\n *   | C     | Delta\n *   ```\n * @property {boolean | null | undefined} [delimiterStart=true]\n *   Whether to begin each row with the delimiter (default: `true`).\n *\n *   > 👉 **Note**: please don’t use this: it could create fragile structures\n *   > that aren’t understandable to some markdown parsers.\n *\n *   When `true`, there are starting delimiters:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there are no starting delimiters:\n *\n *   ```markdown\n *   Alpha | B     |\n *   ----- | ----- |\n *   C     | Delta |\n *   ```\n * @property {boolean | null | undefined} [padding=true]\n *   Whether to add a space of padding between delimiters and cells\n *   (default: `true`).\n *\n *   When `true`, there is padding:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there is no padding:\n *\n *   ```markdown\n *   |Alpha|B    |\n *   |-----|-----|\n *   |C    |Delta|\n *   ```\n * @property {((value: string) => number) | null | undefined} [stringLength]\n *   Function to detect the length of table cell content (optional);\n *   this is used when aligning the delimiters (`|`) between table cells;\n *   full-width characters and emoji mess up delimiter alignment when viewing\n *   the markdown source;\n *   to fix this, you can pass this function,\n *   which receives the cell content and returns its “visible” size;\n *   note that what is and isn’t visible depends on where the text is displayed.\n *\n *   Without such a function, the following:\n *\n *   ```js\n *   markdownTable([\n *     ['Alpha', 'Bravo'],\n *     ['中文', 'Charlie'],\n *     ['👩‍❤️‍👩', 'Delta']\n *   ])\n *   ```\n *\n *   Yields:\n *\n *   ```markdown\n *   | Alpha | Bravo |\n *   | - | - |\n *   | 中文 | Charlie |\n *   | 👩‍❤️‍👩 | Delta |\n *   ```\n *\n *   With [`string-width`](https://github.com/sindresorhus/string-width):\n *\n *   ```js\n *   import stringWidth from 'string-width'\n *\n *   markdownTable(\n *     [\n *       ['Alpha', 'Bravo'],\n *       ['中文', 'Charlie'],\n *       ['👩‍❤️‍👩', 'Delta']\n *     ],\n *     {stringLength: stringWidth}\n *   )\n *   ```\n *\n *   Yields:\n *\n *   ```markdown\n *   | Alpha | Bravo   |\n *   | ----- | ------- |\n *   | 中文  | Charlie |\n *   | 👩‍❤️‍👩    | Delta   |\n *   ```\n */\n\n/**\n * @param {string} value\n *   Cell value.\n * @returns {number}\n *   Cell size.\n */\nfunction defaultStringLength(value) {\n  return value.length\n}\n\n/**\n * Generate a markdown\n * ([GFM](https://docs.github.com/en/github/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables))\n * table.\n *\n * @param {ReadonlyArray<ReadonlyArray<string | null | undefined>>} table\n *   Table data (matrix of strings).\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Result.\n */\nfunction markdownTable(table, options) {\n  const settings = options || {}\n  // To do: next major: change to spread.\n  const align = (settings.align || []).concat()\n  const stringLength = settings.stringLength || defaultStringLength\n  /** @type {Array<number>} Character codes as symbols for alignment per column. */\n  const alignments = []\n  /** @type {Array<Array<string>>} Cells per row. */\n  const cellMatrix = []\n  /** @type {Array<Array<number>>} Sizes of each cell per row. */\n  const sizeMatrix = []\n  /** @type {Array<number>} */\n  const longestCellByColumn = []\n  let mostCellsPerRow = 0\n  let rowIndex = -1\n\n  // This is a superfluous loop if we don’t align delimiters, but otherwise we’d\n  // do superfluous work when aligning, so optimize for aligning.\n  while (++rowIndex < table.length) {\n    /** @type {Array<string>} */\n    const row = []\n    /** @type {Array<number>} */\n    const sizes = []\n    let columnIndex = -1\n\n    if (table[rowIndex].length > mostCellsPerRow) {\n      mostCellsPerRow = table[rowIndex].length\n    }\n\n    while (++columnIndex < table[rowIndex].length) {\n      const cell = serialize(table[rowIndex][columnIndex])\n\n      if (settings.alignDelimiters !== false) {\n        const size = stringLength(cell)\n        sizes[columnIndex] = size\n\n        if (\n          longestCellByColumn[columnIndex] === undefined ||\n          size > longestCellByColumn[columnIndex]\n        ) {\n          longestCellByColumn[columnIndex] = size\n        }\n      }\n\n      row.push(cell)\n    }\n\n    cellMatrix[rowIndex] = row\n    sizeMatrix[rowIndex] = sizes\n  }\n\n  // Figure out which alignments to use.\n  let columnIndex = -1\n\n  if (typeof align === 'object' && 'length' in align) {\n    while (++columnIndex < mostCellsPerRow) {\n      alignments[columnIndex] = toAlignment(align[columnIndex])\n    }\n  } else {\n    const code = toAlignment(align)\n\n    while (++columnIndex < mostCellsPerRow) {\n      alignments[columnIndex] = code\n    }\n  }\n\n  // Inject the alignment row.\n  columnIndex = -1\n  /** @type {Array<string>} */\n  const row = []\n  /** @type {Array<number>} */\n  const sizes = []\n\n  while (++columnIndex < mostCellsPerRow) {\n    const code = alignments[columnIndex]\n    let before = ''\n    let after = ''\n\n    if (code === 99 /* `c` */) {\n      before = ':'\n      after = ':'\n    } else if (code === 108 /* `l` */) {\n      before = ':'\n    } else if (code === 114 /* `r` */) {\n      after = ':'\n    }\n\n    // There *must* be at least one hyphen-minus in each alignment cell.\n    let size =\n      settings.alignDelimiters === false\n        ? 1\n        : Math.max(\n            1,\n            longestCellByColumn[columnIndex] - before.length - after.length\n          )\n\n    const cell = before + '-'.repeat(size) + after\n\n    if (settings.alignDelimiters !== false) {\n      size = before.length + size + after.length\n\n      if (size > longestCellByColumn[columnIndex]) {\n        longestCellByColumn[columnIndex] = size\n      }\n\n      sizes[columnIndex] = size\n    }\n\n    row[columnIndex] = cell\n  }\n\n  // Inject the alignment row.\n  cellMatrix.splice(1, 0, row)\n  sizeMatrix.splice(1, 0, sizes)\n\n  rowIndex = -1\n  /** @type {Array<string>} */\n  const lines = []\n\n  while (++rowIndex < cellMatrix.length) {\n    const row = cellMatrix[rowIndex]\n    const sizes = sizeMatrix[rowIndex]\n    columnIndex = -1\n    /** @type {Array<string>} */\n    const line = []\n\n    while (++columnIndex < mostCellsPerRow) {\n      const cell = row[columnIndex] || ''\n      let before = ''\n      let after = ''\n\n      if (settings.alignDelimiters !== false) {\n        const size =\n          longestCellByColumn[columnIndex] - (sizes[columnIndex] || 0)\n        const code = alignments[columnIndex]\n\n        if (code === 114 /* `r` */) {\n          before = ' '.repeat(size)\n        } else if (code === 99 /* `c` */) {\n          if (size % 2) {\n            before = ' '.repeat(size / 2 + 0.5)\n            after = ' '.repeat(size / 2 - 0.5)\n          } else {\n            before = ' '.repeat(size / 2)\n            after = before\n          }\n        } else {\n          after = ' '.repeat(size)\n        }\n      }\n\n      if (settings.delimiterStart !== false && !columnIndex) {\n        line.push('|')\n      }\n\n      if (\n        settings.padding !== false &&\n        // Don’t add the opening space if we’re not aligning and the cell is\n        // empty: there will be a closing space.\n        !(settings.alignDelimiters === false && cell === '') &&\n        (settings.delimiterStart !== false || columnIndex)\n      ) {\n        line.push(' ')\n      }\n\n      if (settings.alignDelimiters !== false) {\n        line.push(before)\n      }\n\n      line.push(cell)\n\n      if (settings.alignDelimiters !== false) {\n        line.push(after)\n      }\n\n      if (settings.padding !== false) {\n        line.push(' ')\n      }\n\n      if (\n        settings.delimiterEnd !== false ||\n        columnIndex !== mostCellsPerRow - 1\n      ) {\n        line.push('|')\n      }\n    }\n\n    lines.push(\n      settings.delimiterEnd === false\n        ? line.join('').replace(/ +$/, '')\n        : line.join('')\n    )\n  }\n\n  return lines.join('\\n')\n}\n\n/**\n * @param {string | null | undefined} [value]\n *   Value to serialize.\n * @returns {string}\n *   Result.\n */\nfunction serialize(value) {\n  return value === null || value === undefined ? '' : String(value)\n}\n\n/**\n * @param {string | null | undefined} value\n *   Value.\n * @returns {number}\n *   Alignment.\n */\nfunction toAlignment(value) {\n  const code = typeof value === 'string' ? value.codePointAt(0) : 0\n\n  return code === 67 /* `C` */ || code === 99 /* `c` */\n    ? 99 /* `c` */\n    : code === 76 /* `L` */ || code === 108 /* `l` */\n      ? 108 /* `l` */\n      : code === 82 /* `R` */ || code === 114 /* `r` */\n        ? 114 /* `r` */\n        : 0\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/markdown-table/index.js\n");

/***/ })

};
;