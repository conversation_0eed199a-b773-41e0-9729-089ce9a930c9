"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/ModelSelectionModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/settingsService */ \"(app-pages-browser)/./src/lib/services/settingsService.ts\");\n/* harmony import */ var _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/openRouterService */ \"(app-pages-browser)/./src/lib/services/openRouterService.ts\");\n/* harmony import */ var _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/deepSeekService */ \"(app-pages-browser)/./src/lib/services/deepSeekService.ts\");\n/* harmony import */ var _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/modelFavoritesService */ \"(app-pages-browser)/./src/lib/services/modelFavoritesService.ts\");\n/* harmony import */ var _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAdvancedSearch */ \"(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\");\n/* harmony import */ var _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/services/advancedFiltersService */ \"(app-pages-browser)/./src/lib/services/advancedFiltersService.ts\");\n/* harmony import */ var _components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/AdvancedSearchInput */ \"(app-pages-browser)/./src/components/AdvancedSearchInput.tsx\");\n/* harmony import */ var _ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ExpensiveModelConfirmationModal */ \"(app-pages-browser)/./src/components/dashboard/ExpensiveModelConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Constantes para cache\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutos\nconst ENDPOINTS_CACHE_DURATION = 10 * 60 * 1000; // 10 minutos\nconst ModelSelectionModal = (param)=>{\n    let { isOpen, onClose, currentModel, onModelSelect } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [endpoints, setEndpoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedEndpoint, setSelectedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteModelIds, setFavoriteModelIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [displayedModelsCount, setDisplayedModelsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(4);\n    const MODELS_PER_PAGE = 4;\n    const [customModelId, setCustomModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showExpensiveModelModal, setShowExpensiveModelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pendingExpensiveModel, setPendingExpensiveModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [smartCategories, setSmartCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteToggling, setFavoriteToggling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [lastLoadedEndpoint, setLastLoadedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelsCache, setModelsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [endpointsCache, setEndpointsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: \"paid\",\n        sortBy: \"newest\",\n        searchTerm: \"\"\n    });\n    // Hook de busca avançada\n    const { searchTerm, setSearchTerm, searchResults, suggestions, isSearching, hasSearched, clearSearch, trackModelSelection } = (0,_hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch)(models, {\n        debounceMs: 300,\n        enableSuggestions: false,\n        cacheResults: true,\n        fuzzyThreshold: 0.6,\n        maxResults: 50,\n        boostFavorites: true\n    });\n    // Load user endpoints apenas se necessário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && isOpen) {\n            // Verificar se temos cache válido\n            if (endpointsCache && Date.now() - endpointsCache.timestamp < ENDPOINTS_CACHE_DURATION) {\n                setEndpoints(endpointsCache.endpoints);\n                // Selecionar endpoint se ainda não tiver um selecionado\n                if (!selectedEndpoint && endpointsCache.endpoints.length > 0) {\n                    const openRouterEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"OpenRouter\");\n                    const deepSeekEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"DeepSeek\");\n                    if (openRouterEndpoint) {\n                        setSelectedEndpoint(openRouterEndpoint);\n                    } else if (deepSeekEndpoint) {\n                        setSelectedEndpoint(deepSeekEndpoint);\n                    } else {\n                        setSelectedEndpoint(endpointsCache.endpoints[0]);\n                    }\n                }\n            } else {\n                loadEndpoints();\n            }\n        }\n    }, [\n        user,\n        isOpen\n    ]);\n    // Estado para controlar se já carregamos favoritos para este endpoint\n    const [favoritesLoadedForEndpoint, setFavoritesLoadedForEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Atualizar favoritos apenas quando necessário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen && selectedEndpoint && selectedEndpoint.name === \"OpenRouter\" && user && models.length > 0) {\n            const endpointKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            // Só carregar se não carregamos ainda para este endpoint\n            if (favoritesLoadedForEndpoint !== endpointKey) {\n                console.log(\"Loading favorites for endpoint:\", endpointKey);\n                setFavoritesLoadedForEndpoint(endpointKey);\n                const timer = setTimeout(()=>{\n                    updateFavoritesFromFirestore();\n                }, 100);\n                return ()=>clearTimeout(timer);\n            }\n        }\n    }, [\n        isOpen,\n        selectedEndpoint,\n        user,\n        models.length,\n        favoritesLoadedForEndpoint\n    ]);\n    // Reset do estado quando o endpoint muda\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEndpoint) {\n            const endpointKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            if (favoritesLoadedForEndpoint && favoritesLoadedForEndpoint !== endpointKey) {\n                setFavoritesLoadedForEndpoint(null);\n            }\n        }\n    }, [\n        selectedEndpoint\n    ]);\n    // Load models when endpoint changes ou não há cache\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            const cachedData = modelsCache.get(cacheKey);\n            // Verificar se temos cache válido para este endpoint\n            if (cachedData && Date.now() - cachedData.timestamp < CACHE_DURATION) {\n                setModels(cachedData.models);\n                setLastLoadedEndpoint(selectedEndpoint.id);\n                // Extrair favoritos do cache\n                const cachedFavorites = new Set(cachedData.models.filter((m)=>m.isFavorite).map((m)=>m.id));\n                setFavoriteModelIds(cachedFavorites);\n            } else {\n                // Só carregar se mudou de endpoint ou não há cache válido\n                if (lastLoadedEndpoint !== selectedEndpoint.id || !cachedData) {\n                    if (selectedEndpoint.name === \"OpenRouter\") {\n                        loadOpenRouterModels();\n                    } else if (selectedEndpoint.name === \"DeepSeek\") {\n                        loadDeepSeekModels();\n                    }\n                }\n            }\n        }\n    }, [\n        selectedEndpoint,\n        lastLoadedEndpoint,\n        modelsCache\n    ]);\n    // Função para atualizar apenas os favoritos sem recarregar todos os modelos\n    const updateFavoritesFromFirestore = async ()=>{\n        if (!selectedEndpoint || !user || selectedEndpoint.name !== \"OpenRouter\") return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const favoriteIds = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.getFavoriteModelIds(username, selectedEndpoint.id);\n            // Só fazer log se houver mudança nos favoritos\n            const currentFavoriteIds = Array.from(favoriteModelIds);\n            const newFavoriteIds = Array.from(favoriteIds);\n            const hasChanged = currentFavoriteIds.length !== newFavoriteIds.length || !currentFavoriteIds.every((id)=>favoriteIds.has(id));\n            if (hasChanged) {\n                console.log(\"Updating favorites from Firestore:\", newFavoriteIds);\n            }\n            // Atualizar o estado dos favoritos\n            setFavoriteModelIds(favoriteIds);\n            // Atualizar os modelos com o novo status de favorito\n            setModels((prevModels)=>{\n                const updatedModels = prevModels.map((model)=>({\n                        ...model,\n                        isFavorite: favoriteIds.has(model.id)\n                    }));\n                // Log apenas se houver mudança real\n                if (hasChanged) {\n                    const favoritedModels = updatedModels.filter((m)=>m.isFavorite);\n                    console.log(\"Models updated with favorites:\", favoritedModels.map((m)=>m.id));\n                }\n                return updatedModels;\n            });\n            // Atualizar o cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>{\n                const currentCache = prev.get(cacheKey);\n                if (currentCache) {\n                    const updatedModels = currentCache.models.map((model)=>({\n                            ...model,\n                            isFavorite: favoriteIds.has(model.id)\n                        }));\n                    return new Map(prev).set(cacheKey, {\n                        models: updatedModels,\n                        timestamp: currentCache.timestamp\n                    });\n                }\n                return prev;\n            });\n        } catch (error) {\n            console.error(\"Error updating favorites from Firestore:\", error);\n        }\n    };\n    // Load smart categories\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSmartCategories(_lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getSmartCategories());\n    }, []);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                return userDoc.data().username || userDoc.id;\n            }\n            return \"unknown\";\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return \"unknown\";\n        }\n    };\n    const loadEndpoints = async ()=>{\n        if (!user) {\n            console.log(\"No user found\");\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const username = await getUsernameFromFirestore();\n            const userEndpoints = await (0,_lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__.getUserAPIEndpoints)(username);\n            // Salvar no cache\n            setEndpointsCache({\n                endpoints: userEndpoints,\n                timestamp: Date.now()\n            });\n            setEndpoints(userEndpoints);\n            // Select first available endpoint by default (OpenRouter or DeepSeek)\n            const openRouterEndpoint = userEndpoints.find((ep)=>ep.name === \"OpenRouter\");\n            const deepSeekEndpoint = userEndpoints.find((ep)=>ep.name === \"DeepSeek\");\n            if (openRouterEndpoint) {\n                setSelectedEndpoint(openRouterEndpoint);\n            } else if (deepSeekEndpoint) {\n                setSelectedEndpoint(deepSeekEndpoint);\n            } else if (userEndpoints.length > 0) {\n                setSelectedEndpoint(userEndpoints[0]);\n            }\n        } catch (error) {\n            console.error(\"Error loading endpoints:\", error);\n            setError(\"Erro ao carregar endpoints: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadOpenRouterModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from OpenRouter\n            const openRouterModels = await _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.fetchModels();\n            // Load favorite model IDs - sempre buscar do Firestore para garantir dados atualizados\n            const username = await getUsernameFromFirestore();\n            const favoriteIds = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.getFavoriteModelIds(username, selectedEndpoint.id);\n            if (favoriteIds.size > 0) {\n                console.log(\"Loaded favorite IDs:\", Array.from(favoriteIds));\n            }\n            // Mark favorite models\n            const modelsWithFavorites = openRouterModels.map((model)=>({\n                    ...model,\n                    isFavorite: favoriteIds.has(model.id)\n                }));\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: modelsWithFavorites,\n                    timestamp: Date.now()\n                }));\n            setModels(modelsWithFavorites);\n            setFavoriteModelIds(favoriteIds);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n            const favoritedCount = modelsWithFavorites.filter((m)=>m.isFavorite).length;\n            if (favoritedCount > 0) {\n                console.log(\"Models loaded with favorites:\", favoritedCount, \"favorites found\");\n            }\n        } catch (error) {\n            console.error(\"Error loading models:\", error);\n            setError(\"Erro ao carregar modelos\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadDeepSeekModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from DeepSeek\n            const deepSeekModels = await _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.fetchModels();\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: deepSeekModels,\n                    timestamp: Date.now()\n                }));\n            setModels(deepSeekModels);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            console.error(\"Error loading DeepSeek models:\", error);\n            setError(\"Erro ao carregar modelos DeepSeek\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Função para obter modelos filtrados\n    const getFilteredModels = ()=>{\n        let filtered = [\n            ...models\n        ];\n        // Primeiro, aplicar filtros de categoria base (paid/free/favorites)\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\") {\n            if (filters.category === \"favorites\") {\n                filtered = [];\n            } else {\n                filtered = _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.filterByCategory(filtered, filters.category);\n            }\n        } else {\n            filtered = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.filterByCategory(filtered, filters.category);\n        }\n        // Se há busca ativa, usar resultados da busca avançada (mas ainda respeitando a categoria base)\n        if (hasSearched && searchTerm.trim()) {\n            const searchResultModels = searchResults.map((result)=>result.model);\n            // Filtrar os resultados de busca para manter apenas os que passam pelo filtro de categoria base\n            filtered = searchResultModels.filter((model)=>filtered.some((f)=>f.id === model.id));\n        } else if (selectedCategory) {\n            // Se há categoria inteligente selecionada, aplicar filtro adicional\n            const categoryFiltered = _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getModelsByCategory(filtered, selectedCategory);\n            filtered = categoryFiltered;\n        }\n        // Aplicar ordenação se não há busca ativa\n        if (!hasSearched || !searchTerm.trim()) {\n            const service = (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" ? _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService : _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService;\n            filtered = service.sortModels(filtered, filters.sortBy);\n        }\n        return filtered;\n    };\n    const filteredModels = getFilteredModels();\n    const handleToggleFavorite = async (model)=>{\n        if (!user || !selectedEndpoint) return;\n        // Prevenir múltiplas chamadas simultâneas para o mesmo modelo\n        if (favoriteToggling.has(model.id)) {\n            console.log(\"Already toggling favorite for model:\", model.id);\n            return;\n        }\n        console.log(\"Toggling favorite: \".concat(model.id, \" (\").concat(model.isFavorite ? \"removing\" : \"adding\", \")\"));\n        // Calcular o novo status otimisticamente\n        const optimisticNewStatus = !model.isFavorite;\n        try {\n            // Marcar como em processo\n            setFavoriteToggling((prev)=>new Set(prev).add(model.id));\n            // ATUALIZAÇÃO OTIMISTA: Atualizar a UI imediatamente\n            const updatedFavoriteIds = new Set(favoriteModelIds);\n            if (optimisticNewStatus) {\n                updatedFavoriteIds.add(model.id);\n            } else {\n                updatedFavoriteIds.delete(model.id);\n            }\n            setFavoriteModelIds(updatedFavoriteIds);\n            // Atualizar o array de modelos imediatamente\n            setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                        ...m,\n                        isFavorite: optimisticNewStatus\n                    } : m));\n            // Atualizar o cache imediatamente\n            if (selectedEndpoint) {\n                const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n                setModelsCache((prev)=>{\n                    const currentCache = prev.get(cacheKey);\n                    if (currentCache) {\n                        const updatedModels = currentCache.models.map((m)=>m.id === model.id ? {\n                                ...m,\n                                isFavorite: optimisticNewStatus\n                            } : m);\n                        return new Map(prev).set(cacheKey, {\n                            models: updatedModels,\n                            timestamp: currentCache.timestamp\n                        });\n                    }\n                    return prev;\n                });\n            }\n            // Agora fazer a operação no Firestore\n            const username = await getUsernameFromFirestore();\n            const actualNewStatus = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.toggleFavorite(username, selectedEndpoint.id, model.id, model.name);\n            // Log apenas se houver diferença\n            if (actualNewStatus !== optimisticNewStatus) {\n                console.log(\"Status mismatch - Optimistic:\", optimisticNewStatus, \"Actual:\", actualNewStatus);\n            }\n            // Se o status real for diferente do otimista, corrigir\n            if (actualNewStatus !== optimisticNewStatus) {\n                console.warn(\"Optimistic update was incorrect, correcting...\");\n                // Corrigir o estado dos favoritos\n                const correctedFavoriteIds = new Set(favoriteModelIds);\n                if (actualNewStatus) {\n                    correctedFavoriteIds.add(model.id);\n                } else {\n                    correctedFavoriteIds.delete(model.id);\n                }\n                setFavoriteModelIds(correctedFavoriteIds);\n                // Corrigir o array de modelos\n                setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                            ...m,\n                            isFavorite: actualNewStatus\n                        } : m));\n                // Corrigir o cache\n                if (selectedEndpoint) {\n                    const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n                    setModelsCache((prev)=>{\n                        const currentCache = prev.get(cacheKey);\n                        if (currentCache) {\n                            const correctedModels = currentCache.models.map((m)=>m.id === model.id ? {\n                                    ...m,\n                                    isFavorite: actualNewStatus\n                                } : m);\n                            return new Map(prev).set(cacheKey, {\n                                models: correctedModels,\n                                timestamp: currentCache.timestamp\n                            });\n                        }\n                        return prev;\n                    });\n                }\n            }\n        } catch (error) {\n            console.error(\"Error toggling favorite:\", error);\n            // Em caso de erro, reverter a atualização otimista\n            const revertedFavoriteIds = new Set(favoriteModelIds);\n            if (!optimisticNewStatus) {\n                revertedFavoriteIds.add(model.id);\n            } else {\n                revertedFavoriteIds.delete(model.id);\n            }\n            setFavoriteModelIds(revertedFavoriteIds);\n            // Reverter o array de modelos\n            setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                        ...m,\n                        isFavorite: !optimisticNewStatus\n                    } : m));\n            // Reverter o cache\n            if (selectedEndpoint) {\n                const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n                setModelsCache((prev)=>{\n                    const currentCache = prev.get(cacheKey);\n                    if (currentCache) {\n                        const revertedModels = currentCache.models.map((m)=>m.id === model.id ? {\n                                ...m,\n                                isFavorite: !optimisticNewStatus\n                            } : m);\n                        return new Map(prev).set(cacheKey, {\n                            models: revertedModels,\n                            timestamp: currentCache.timestamp\n                        });\n                    }\n                    return prev;\n                });\n            }\n        } finally{\n            // Remover do estado de processamento\n            setFavoriteToggling((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(model.id);\n                return newSet;\n            });\n        }\n    };\n    // Function to check if a model is expensive (over $20 per million tokens)\n    const isExpensiveModel = (model)=>{\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\") return false;\n        const totalPrice = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model);\n        return totalPrice > 0.00002; // $20 por 1M tokens = $0.00002 por token\n    };\n    const handleSelectModel = (model)=>{\n        // Rastrear seleção de modelo\n        trackModelSelection(model.id);\n        // Check if it's an expensive OpenRouter model\n        if (isExpensiveModel(model)) {\n            setPendingExpensiveModel(model);\n            setShowExpensiveModelModal(true);\n        } else {\n            onModelSelect(model.id);\n            onClose();\n        }\n    };\n    const handleConfirmExpensiveModel = ()=>{\n        if (pendingExpensiveModel) {\n            // Rastrear seleção de modelo caro\n            trackModelSelection(pendingExpensiveModel.id);\n            onModelSelect(pendingExpensiveModel.id);\n            setShowExpensiveModelModal(false);\n            setPendingExpensiveModel(null);\n            onClose();\n        }\n    };\n    const handleCancelExpensiveModel = ()=>{\n        setShowExpensiveModelModal(false);\n        setPendingExpensiveModel(null);\n    };\n    const handleLoadMoreModels = ()=>{\n        setDisplayedModelsCount((prev)=>prev + MODELS_PER_PAGE);\n    };\n    const handleUseCustomModel = ()=>{\n        if (customModelId.trim()) {\n            onModelSelect(customModelId.trim());\n            onClose();\n        }\n    };\n    // Função para forçar refresh dos modelos\n    const handleRefreshModels = ()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>{\n                const newCache = new Map(prev);\n                newCache.delete(cacheKey);\n                return newCache;\n            });\n            if (selectedEndpoint.name === \"OpenRouter\") {\n                loadOpenRouterModels();\n            } else if (selectedEndpoint.name === \"DeepSeek\") {\n                loadDeepSeekModels();\n            }\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl rounded-2xl border border-blue-600/30 shadow-2xl w-full max-w-4xl max-h-[80vh] overflow-hidden relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none rounded-2xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 622,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-blue-700/30 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-white\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-blue-100\",\n                                                children: \"Selecionar Modelo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"p-2 rounded-xl hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 626,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-blue-200\",\n                                                children: \"Endpoint\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleRefreshModels,\n                                                disabled: loading || !selectedEndpoint,\n                                                className: \"p-2 rounded-lg hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                title: \"Atualizar modelos\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 \".concat(loading ? \"animate-spin\" : \"\"),\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.id) || \"\",\n                                        onChange: (e)=>{\n                                            const endpoint = endpoints.find((ep)=>ep.id === e.target.value);\n                                            setSelectedEndpoint(endpoint || null);\n                                        },\n                                        className: \"w-full bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Selecione um endpoint\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            endpoints.map((endpoint)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: endpoint.id,\n                                                    className: \"bg-blue-900 text-blue-100\",\n                                                    children: endpoint.name\n                                                }, endpoint.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 17\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 625,\n                        columnNumber: 9\n                    }, undefined),\n                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"OpenRouter\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-blue-700/30 space-y-6 relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-blue-900/30 backdrop-blur-sm rounded-xl border border-blue-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-blue-200 mb-3 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-blue-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Modelo Customizado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"openai/gpt-4-1\",\n                                                        value: customModelId,\n                                                        onChange: (e)=>setCustomModelId(e.target.value),\n                                                        onKeyDown: (e)=>e.key === \"Enter\" && handleUseCustomModel(),\n                                                        className: \"flex-1 bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleUseCustomModel,\n                                                        disabled: !customModelId.trim(),\n                                                        className: \"px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 disabled:from-blue-800 disabled:to-blue-800 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 disabled:hover:scale-100\",\n                                                        children: \"Usar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-300/70 mt-3\",\n                                                children: \"Digite o ID completo do modelo (ex: openai/gpt-4, anthropic/claude-3-sonnet)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 707,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 bg-blue-900/30 backdrop-blur-sm rounded-xl p-1 border border-blue-600/20\",\n                                        children: [\n                                            \"paid\",\n                                            \"free\",\n                                            \"favorites\"\n                                        ].map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            category\n                                                        })),\n                                                className: \"flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-all duration-200 \".concat(filters.category === category ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg\" : \"text-blue-300 hover:text-blue-200 hover:bg-blue-800/30\"),\n                                                children: category === \"paid\" ? \"Pagos\" : category === \"free\" ? \"Gr\\xe1tis\" : \"Favoritos\"\n                                            }, category, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            value: searchTerm,\n                                                            onChange: setSearchTerm,\n                                                            suggestions: [],\n                                                            isSearching: isSearching,\n                                                            placeholder: \"Buscar modelos... (ex: 'gpt-4', 'vision', 'cheap')\",\n                                                            showSuggestions: false\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 733,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 732,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: filters.sortBy,\n                                                        onChange: (e)=>setFilters((prev)=>({\n                                                                    ...prev,\n                                                                    sortBy: e.target.value\n                                                                })),\n                                                        className: \"bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\",\n                                                        disabled: hasSearched && searchTerm.trim().length > 0,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"newest\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Mais recentes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 748,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"price_low\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Menor pre\\xe7o\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"price_high\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Maior pre\\xe7o\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"context_high\",\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: \"Maior contexto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 751,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            !hasSearched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedCategory(null),\n                                                        className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 \".concat(!selectedCategory ? \"bg-blue-600 text-white\" : \"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200\"),\n                                                        children: \"Todos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 758,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    smartCategories.slice(0, 6).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setSelectedCategory(category.id),\n                                                            className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-1 \".concat(selectedCategory === category.id ? \"bg-blue-600 text-white\" : \"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200\"),\n                                                            title: category.description,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: category.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 779,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 780,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, category.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 757,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            hasSearched && searchTerm.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300\",\n                                                        children: [\n                                                            filteredModels.length,\n                                                            \" resultado\",\n                                                            filteredModels.length !== 1 ? \"s\" : \"\",\n                                                            ' para \"',\n                                                            searchTerm,\n                                                            '\"'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: clearSearch,\n                                                        className: \"text-blue-400 hover:text-blue-300 transition-colors duration-200\",\n                                                        children: \"Limpar busca\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-scroll p-6 max-h-[32rem] relative z-10\",\n                                children: [\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 808,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-200 text-sm font-medium\",\n                                                    children: \"Carregando modelos...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 807,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 806,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-red-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 819,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 818,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-300 font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 822,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && filteredModels.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-blue-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 830,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-300 font-medium\",\n                                                children: hasSearched && searchTerm.trim() ? 'Nenhum resultado para \"'.concat(searchTerm, '\"') : selectedCategory ? \"Nenhum modelo na categoria selecionada\" : \"Nenhum modelo encontrado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 834,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-blue-400/70 text-sm mt-2 space-y-1\",\n                                                children: hasSearched && searchTerm.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Tente:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 845,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"list-disc list-inside space-y-1 text-left max-w-xs mx-auto\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Verificar a ortografia\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 847,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Usar termos mais gen\\xe9ricos\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 848,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Explorar as categorias\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 849,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"Limpar filtros ativos\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 850,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 846,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Tente ajustar os filtros ou usar as categorias\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 854,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 842,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 828,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: filteredModels.slice(0, displayedModelsCount).map((model)=>{\n                                            // Encontrar o resultado da busca para este modelo (se houver)\n                                            const searchResult = hasSearched ? searchResults.find((r)=>r.model.id === model.id) : null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModelCard, {\n                                                model: model,\n                                                isSelected: currentModel === model.id,\n                                                onSelect: ()=>handleSelectModel(model),\n                                                onToggleFavorite: ()=>handleToggleFavorite(model),\n                                                isToggling: favoriteToggling.has(model.id),\n                                                service: _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService,\n                                                searchTerm: hasSearched ? searchTerm : \"\",\n                                                searchResult: searchResult\n                                            }, model.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 868,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 862,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !loading && !error && filteredModels.length > displayedModelsCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLoadMoreModels,\n                                            className: \"px-6 py-3 bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm border border-blue-600/30 hover:border-blue-500/50 rounded-xl text-blue-200 hover:text-blue-100 transition-all duration-200 flex items-center space-x-2 hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Carregar mais modelos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 890,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M19 9l-7 7-7-7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 891,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 886,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 885,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && filteredModels.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-4 space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-400/70\",\n                                                children: [\n                                                    \"Mostrando \",\n                                                    Math.min(displayedModelsCount, filteredModels.length),\n                                                    \" de \",\n                                                    filteredModels.length,\n                                                    \" modelos\",\n                                                    models.length !== filteredModels.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-blue-300\",\n                                                        children: [\n                                                            \"(\",\n                                                            models.length,\n                                                            \" total)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 904,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 901,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            hasSearched && searchTerm.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-4 text-xs text-blue-400/60\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"\\uD83D\\uDD0D Busca: \",\n                                                            searchTerm\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 913,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    searchResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"⚡ \",\n                                                            searchResults.length,\n                                                            \" resultados\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 915,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 900,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 804,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-slate-700/30 space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-slate-100 mb-2\",\n                                            children: \"Modelos DeepSeek\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 930,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-slate-400\",\n                                            children: \"Escolha entre nossos modelos especializados\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 931,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 929,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 928,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-6\",\n                                children: [\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 942,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-200 text-sm font-medium\",\n                                                    children: \"Carregando modelos...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 943,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 941,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 940,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-red-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 953,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 952,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 951,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-300 font-medium\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 956,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 950,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 949,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && models.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 text-blue-400\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 965,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 964,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 963,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-300 font-medium\",\n                                                children: \"Nenhum modelo encontrado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 968,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-400/70 text-sm mt-1\",\n                                                children: \"Tente ajustar os filtros de busca\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 969,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 962,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !loading && !error && models.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeepSeekModelCard, {\n                                                model: model,\n                                                isSelected: currentModel === model.id,\n                                                onSelect: ()=>handleSelectModel(model)\n                                            }, model.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 976,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 974,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 938,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true),\n                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\" && (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"DeepSeek\" && selectedEndpoint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8 text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-blue-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 993,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 992,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 991,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-300 font-medium\",\n                                children: \"Sele\\xe7\\xe3o de modelos dispon\\xedvel para OpenRouter e DeepSeek\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 996,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-400/70 text-sm mt-1\",\n                                children: \"Selecione um desses endpoints para ver os modelos dispon\\xedveis\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 997,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 990,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 620,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: showExpensiveModelModal,\n                model: pendingExpensiveModel,\n                onConfirm: handleConfirmExpensiveModel,\n                onCancel: handleCancelExpensiveModel\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1003,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 619,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ModelSelectionModal, \"LY0WWITrlEFcGqg+J3+xP5lcQ5w=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch\n    ];\n});\n_c = ModelSelectionModal;\nconst ModelCard = (param)=>{\n    let { model, isSelected, onSelect, onToggleFavorite, isToggling = false, service = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService, searchTerm = \"\", searchResult } = param;\n    const isExpensive = service === _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService && _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model) > 0.00002; // $20 por 1M tokens\n    // Log para debug (apenas quando necessário)\n    // console.log(`ModelCard ${model.id}: isFavorite=${model.isFavorite}, isToggling=${isToggling}`);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-model-id\": model.id,\n        className: \"p-5 rounded-xl border transition-all duration-200 backdrop-blur-sm hover:scale-[1.02] relative \".concat(isSelected ? \"bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20\" : \"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40\"),\n        children: [\n            isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-2 -right-2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-amber-500 to-orange-500 rounded-full p-1.5 shadow-lg border-2 border-slate-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 1053,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1052,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 1051,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1050,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-blue-100 truncate\",\n                                                        children: (searchResult === null || searchResult === void 0 ? void 0 : searchResult.highlightedName) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: searchResult.highlightedName\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 1066,\n                                                            columnNumber: 21\n                                                        }, undefined) : searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                                            text: model.name,\n                                                            highlight: searchTerm\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 1068,\n                                                            columnNumber: 21\n                                                        }, undefined) : model.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 1064,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-amber-500/20 text-amber-300 px-2 py-0.5 rounded-full border border-amber-500/30 font-medium\",\n                                                        children: \"CARO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 1074,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    searchResult && searchResult.matchedFields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-1\",\n                                                        children: searchResult.matchedFields.slice(0, 3).map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs bg-green-500/20 text-green-300 px-1.5 py-0.5 rounded border border-green-500/30\",\n                                                                title: \"Encontrado em: \".concat(field),\n                                                                children: field === \"name\" ? \"\\uD83D\\uDCDD\" : field === \"description\" ? \"\\uD83D\\uDCC4\" : field === \"provider\" ? \"\\uD83C\\uDFE2\" : field === \"tags\" ? \"\\uD83C\\uDFF7️\" : \"\\uD83D\\uDD0D\"\n                                                            }, field, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 1081,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 1079,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1063,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-300/70 truncate mt-1 font-mono\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                                    text: model.id,\n                                                    highlight: searchTerm\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 1093,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1092,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1062,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    service.isFreeModel(model) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 bg-green-600/30 text-green-300 text-xs rounded-full border border-green-500/30 font-medium\",\n                                        children: \"Gr\\xe1tis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1097,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1061,\n                                columnNumber: 11\n                            }, undefined),\n                            model.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-300/80 line-clamp-2\",\n                                    children: (searchResult === null || searchResult === void 0 ? void 0 : searchResult.highlightedDescription) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: searchResult.highlightedDescription\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1108,\n                                        columnNumber: 19\n                                    }, undefined) : searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                        text: model.description,\n                                        highlight: searchTerm\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1110,\n                                        columnNumber: 19\n                                    }, undefined) : model.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 1106,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1105,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Contexto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1120,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: service.formatContextLength(model.context_length)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1121,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1119,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Input\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1124,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: [\n                                                    service.formatPrice(model.pricing.prompt),\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1125,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1123,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Output\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1128,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: [\n                                                    service.formatPrice(model.pricing.completion),\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 1129,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1127,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1118,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1060,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 ml-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggleFavorite,\n                                disabled: isToggling,\n                                className: \"p-2.5 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed \".concat(model.isFavorite ? \"text-yellow-400 hover:text-yellow-300 bg-yellow-500/20 border border-yellow-500/30\" : \"text-blue-300 hover:text-yellow-400 bg-blue-800/30 border border-blue-600/20 hover:bg-yellow-500/20 hover:border-yellow-500/30\"),\n                                title: isToggling ? \"Processando...\" : model.isFavorite ? \"Remover dos favoritos\" : \"Adicionar aos favoritos\",\n                                children: isToggling ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-current\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 1146,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: model.isFavorite ? \"currentColor\" : \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1149,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 1148,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1135,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSelect,\n                                className: \"px-6 py-2.5 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 shadow-lg hover:shadow-blue-500/30\",\n                                children: \"Selecionar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1154,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1134,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1059,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 1041,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ModelCard;\nconst DeepSeekModelCard = (param)=>{\n    let { model, isSelected, onSelect } = param;\n    const getModelIcon = (modelId)=>{\n        if (modelId === \"deepseek-chat\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-blue-400\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 1178,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1177,\n                columnNumber: 9\n            }, undefined);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-8 h-8 text-cyan-400\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 1184,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1183,\n                columnNumber: 9\n            }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative p-6 rounded-2xl border transition-all duration-300 cursor-pointer group backdrop-blur-sm hover:scale-[1.02] \".concat(isSelected ? \"bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20\" : \"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40\"),\n        onClick: onSelect,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-br from-blue-600/10 to-cyan-600/10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1198,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center\",\n                                children: getModelIcon(model.id)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1204,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-blue-100 text-lg\",\n                                        children: model.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1208,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-300/70 mt-1\",\n                                        children: model.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1209,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1207,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1203,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-300/70 font-medium mb-1\",\n                                        children: \"Contexto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1216,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-blue-200\",\n                                        children: _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatContextLength(model.context_length)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1217,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1215,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-300/70 font-medium mb-1\",\n                                        children: \"Input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1222,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-blue-200\",\n                                        children: [\n                                            _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatPrice(model.pricing.prompt),\n                                            \"/1M\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1223,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1221,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-300/70 font-medium mb-1\",\n                                        children: \"Output\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1228,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-semibold text-blue-200\",\n                                        children: [\n                                            _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatPrice(model.pricing.completion),\n                                            \"/1M\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 1229,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 1227,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1214,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            onSelect();\n                        },\n                        className: \"w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 hover:scale-105 \".concat(isSelected ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg shadow-blue-500/30\" : \"bg-blue-800/40 text-blue-200 hover:bg-gradient-to-r hover:from-blue-600 hover:to-cyan-600 hover:text-white border border-blue-600/30\"),\n                        children: isSelected ? \"Selecionado\" : \"Selecionar Modelo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 1236,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 1201,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 1191,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = DeepSeekModelCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ModelSelectionModal);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ModelSelectionModal\");\n$RefreshReg$(_c1, \"ModelCard\");\n$RefreshReg$(_c2, \"DeepSeekModelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9Nb2RlbFNlbGVjdGlvbk1vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ0s7QUFDYjtBQUNtRDtBQUNsQjtBQUNKO0FBQ1k7QUFFZjtBQUNnQztBQUNOO0FBQ1I7QUFTaEYsd0JBQXdCO0FBQ3hCLE1BQU1hLGlCQUFpQixJQUFJLEtBQUssTUFBTSxZQUFZO0FBQ2xELE1BQU1DLDJCQUEyQixLQUFLLEtBQUssTUFBTSxhQUFhO0FBRTlELE1BQU1DLHNCQUFzQjtRQUFDLEVBQUVDLE1BQU0sRUFBRUMsT0FBTyxFQUFFQyxZQUFZLEVBQUVDLGFBQWEsRUFBNEI7O0lBQ3JHLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUdsQiw4REFBT0E7SUFDeEIsTUFBTSxDQUFDbUIsV0FBV0MsYUFBYSxHQUFHdEIsK0NBQVFBLENBQWdCLEVBQUU7SUFDNUQsTUFBTSxDQUFDdUIsa0JBQWtCQyxvQkFBb0IsR0FBR3hCLCtDQUFRQSxDQUFxQjtJQUM3RSxNQUFNLENBQUN5QixRQUFRQyxVQUFVLEdBQUcxQiwrQ0FBUUEsQ0FBWSxFQUFFO0lBQ2xELE1BQU0sQ0FBQzJCLGtCQUFrQkMsb0JBQW9CLEdBQUc1QiwrQ0FBUUEsQ0FBYyxJQUFJNkI7SUFDMUUsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUcvQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNnQyxPQUFPQyxTQUFTLEdBQUdqQywrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDa0Msc0JBQXNCQyx3QkFBd0IsR0FBR25DLCtDQUFRQSxDQUFDO0lBQ2pFLE1BQU1vQyxrQkFBa0I7SUFDeEIsTUFBTSxDQUFDQyxlQUFlQyxpQkFBaUIsR0FBR3RDLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ3VDLHlCQUF5QkMsMkJBQTJCLEdBQUd4QywrQ0FBUUEsQ0FBQztJQUN2RSxNQUFNLENBQUN5Qyx1QkFBdUJDLHlCQUF5QixHQUFHMUMsK0NBQVFBLENBQWlCO0lBQ25GLE1BQU0sQ0FBQzJDLGtCQUFrQkMsb0JBQW9CLEdBQUc1QywrQ0FBUUEsQ0FBZ0I7SUFDeEUsTUFBTSxDQUFDNkMsaUJBQWlCQyxtQkFBbUIsR0FBRzlDLCtDQUFRQSxDQUFrQixFQUFFO0lBQzFFLE1BQU0sQ0FBQytDLGtCQUFrQkMsb0JBQW9CLEdBQUdoRCwrQ0FBUUEsQ0FBYyxJQUFJNkI7SUFDMUUsTUFBTSxDQUFDb0Isb0JBQW9CQyxzQkFBc0IsR0FBR2xELCtDQUFRQSxDQUFnQjtJQUM1RSxNQUFNLENBQUNtRCxhQUFhQyxlQUFlLEdBQUdwRCwrQ0FBUUEsQ0FBd0QsSUFBSXFEO0lBQzFHLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBR3ZELCtDQUFRQSxDQUF5RDtJQUU3RyxNQUFNLENBQUN3RCxTQUFTQyxXQUFXLEdBQUd6RCwrQ0FBUUEsQ0FBZTtRQUNuRDBELFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxZQUFZO0lBQ2Q7SUFFQSx5QkFBeUI7SUFDekIsTUFBTSxFQUNKQSxVQUFVLEVBQ1ZDLGFBQWEsRUFDYkMsYUFBYSxFQUNiQyxXQUFXLEVBQ1hDLFdBQVcsRUFDWEMsV0FBVyxFQUNYQyxXQUFXLEVBQ1hDLG1CQUFtQixFQUNwQixHQUFHM0QsMkVBQWlCQSxDQUFDaUIsUUFBUTtRQUM1QjJDLFlBQVk7UUFDWkMsbUJBQW1CO1FBQ25CQyxjQUFjO1FBQ2RDLGdCQUFnQjtRQUNoQkMsWUFBWTtRQUNaQyxnQkFBZ0I7SUFDbEI7SUFFQSwyQ0FBMkM7SUFDM0N4RSxnREFBU0EsQ0FBQztRQUNSLElBQUltQixRQUFRSixRQUFRO1lBQ2xCLGtDQUFrQztZQUNsQyxJQUFJc0Msa0JBQWtCb0IsS0FBS0MsR0FBRyxLQUFLckIsZUFBZXNCLFNBQVMsR0FBRzlELDBCQUEwQjtnQkFDdEZRLGFBQWFnQyxlQUFlakMsU0FBUztnQkFFckMsd0RBQXdEO2dCQUN4RCxJQUFJLENBQUNFLG9CQUFvQitCLGVBQWVqQyxTQUFTLENBQUN3RCxNQUFNLEdBQUcsR0FBRztvQkFDNUQsTUFBTUMscUJBQXFCeEIsZUFBZWpDLFNBQVMsQ0FBQzBELElBQUksQ0FBQ0MsQ0FBQUEsS0FBTUEsR0FBR0MsSUFBSSxLQUFLO29CQUMzRSxNQUFNQyxtQkFBbUI1QixlQUFlakMsU0FBUyxDQUFDMEQsSUFBSSxDQUFDQyxDQUFBQSxLQUFNQSxHQUFHQyxJQUFJLEtBQUs7b0JBRXpFLElBQUlILG9CQUFvQjt3QkFDdEJ0RCxvQkFBb0JzRDtvQkFDdEIsT0FBTyxJQUFJSSxrQkFBa0I7d0JBQzNCMUQsb0JBQW9CMEQ7b0JBQ3RCLE9BQU87d0JBQ0wxRCxvQkFBb0I4QixlQUFlakMsU0FBUyxDQUFDLEVBQUU7b0JBQ2pEO2dCQUNGO1lBQ0YsT0FBTztnQkFDTDhEO1lBQ0Y7UUFDRjtJQUNGLEdBQUc7UUFBQy9EO1FBQU1KO0tBQU87SUFFakIsc0VBQXNFO0lBQ3RFLE1BQU0sQ0FBQ29FLDRCQUE0QkMsOEJBQThCLEdBQUdyRiwrQ0FBUUEsQ0FBZ0I7SUFFNUYsK0NBQStDO0lBQy9DQyxnREFBU0EsQ0FBQztRQUNSLElBQUllLFVBQVVPLG9CQUFvQkEsaUJBQWlCMEQsSUFBSSxLQUFLLGdCQUFnQjdELFFBQVFLLE9BQU9vRCxNQUFNLEdBQUcsR0FBRztZQUNyRyxNQUFNUyxjQUFjLEdBQTBCL0QsT0FBdkJBLGlCQUFpQmdFLEVBQUUsRUFBQyxLQUF5QixPQUF0QmhFLGlCQUFpQjBELElBQUk7WUFFbkUseURBQXlEO1lBQ3pELElBQUlHLCtCQUErQkUsYUFBYTtnQkFDOUNFLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBbUNIO2dCQUMvQ0QsOEJBQThCQztnQkFFOUIsTUFBTUksUUFBUUMsV0FBVztvQkFDdkJDO2dCQUNGLEdBQUc7Z0JBRUgsT0FBTyxJQUFNQyxhQUFhSDtZQUM1QjtRQUNGO0lBQ0YsR0FBRztRQUFDMUU7UUFBUU87UUFBa0JIO1FBQU1LLE9BQU9vRCxNQUFNO1FBQUVPO0tBQTJCO0lBRTlFLHlDQUF5QztJQUN6Q25GLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSXNCLGtCQUFrQjtZQUNwQixNQUFNK0QsY0FBYyxHQUEwQi9ELE9BQXZCQSxpQkFBaUJnRSxFQUFFLEVBQUMsS0FBeUIsT0FBdEJoRSxpQkFBaUIwRCxJQUFJO1lBQ25FLElBQUlHLDhCQUE4QkEsK0JBQStCRSxhQUFhO2dCQUM1RUQsOEJBQThCO1lBQ2hDO1FBQ0Y7SUFDRixHQUFHO1FBQUM5RDtLQUFpQjtJQUVyQixvREFBb0Q7SUFDcER0QixnREFBU0EsQ0FBQztRQUNSLElBQUlzQixrQkFBa0I7WUFDcEIsTUFBTXVFLFdBQVcsR0FBMEJ2RSxPQUF2QkEsaUJBQWlCZ0UsRUFBRSxFQUFDLEtBQXlCLE9BQXRCaEUsaUJBQWlCMEQsSUFBSTtZQUNoRSxNQUFNYyxhQUFhNUMsWUFBWTZDLEdBQUcsQ0FBQ0Y7WUFFbkMscURBQXFEO1lBQ3JELElBQUlDLGNBQWNyQixLQUFLQyxHQUFHLEtBQUtvQixXQUFXbkIsU0FBUyxHQUFHL0QsZ0JBQWdCO2dCQUNwRWEsVUFBVXFFLFdBQVd0RSxNQUFNO2dCQUMzQnlCLHNCQUFzQjNCLGlCQUFpQmdFLEVBQUU7Z0JBRXpDLDZCQUE2QjtnQkFDN0IsTUFBTVUsa0JBQWtCLElBQUlwRSxJQUMxQmtFLFdBQVd0RSxNQUFNLENBQUN5RSxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLFVBQVUsRUFBRUMsR0FBRyxDQUFDRixDQUFBQSxJQUFLQSxFQUFFWixFQUFFO2dCQUUzRDNELG9CQUFvQnFFO1lBQ3RCLE9BQU87Z0JBQ0wsMERBQTBEO2dCQUMxRCxJQUFJaEQsdUJBQXVCMUIsaUJBQWlCZ0UsRUFBRSxJQUFJLENBQUNRLFlBQVk7b0JBQzdELElBQUl4RSxpQkFBaUIwRCxJQUFJLEtBQUssY0FBYzt3QkFDMUNxQjtvQkFDRixPQUFPLElBQUkvRSxpQkFBaUIwRCxJQUFJLEtBQUssWUFBWTt3QkFDL0NzQjtvQkFDRjtnQkFDRjtZQUNGO1FBQ0Y7SUFDRixHQUFHO1FBQUNoRjtRQUFrQjBCO1FBQW9CRTtLQUFZO0lBRXRELDRFQUE0RTtJQUM1RSxNQUFNeUMsK0JBQStCO1FBQ25DLElBQUksQ0FBQ3JFLG9CQUFvQixDQUFDSCxRQUFRRyxpQkFBaUIwRCxJQUFJLEtBQUssY0FBYztRQUUxRSxJQUFJO1lBQ0YsTUFBTXVCLFdBQVcsTUFBTUM7WUFDdkIsTUFBTUMsY0FBYyxNQUFNbkcsc0ZBQXFCQSxDQUFDb0csbUJBQW1CLENBQUNILFVBQVVqRixpQkFBaUJnRSxFQUFFO1lBRWpHLCtDQUErQztZQUMvQyxNQUFNcUIscUJBQXFCQyxNQUFNQyxJQUFJLENBQUNuRjtZQUN0QyxNQUFNb0YsaUJBQWlCRixNQUFNQyxJQUFJLENBQUNKO1lBQ2xDLE1BQU1NLGFBQWFKLG1CQUFtQi9CLE1BQU0sS0FBS2tDLGVBQWVsQyxNQUFNLElBQ3BELENBQUMrQixtQkFBbUJLLEtBQUssQ0FBQzFCLENBQUFBLEtBQU1tQixZQUFZUSxHQUFHLENBQUMzQjtZQUVsRSxJQUFJeUIsWUFBWTtnQkFDZHhCLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBc0NzQjtZQUNwRDtZQUVBLG1DQUFtQztZQUNuQ25GLG9CQUFvQjhFO1lBRXBCLHFEQUFxRDtZQUNyRGhGLFVBQVV5RixDQUFBQTtnQkFDUixNQUFNQyxnQkFBZ0JELFdBQVdkLEdBQUcsQ0FBQ2dCLENBQUFBLFFBQVU7d0JBQzdDLEdBQUdBLEtBQUs7d0JBQ1JqQixZQUFZTSxZQUFZUSxHQUFHLENBQUNHLE1BQU05QixFQUFFO29CQUN0QztnQkFFQSxvQ0FBb0M7Z0JBQ3BDLElBQUl5QixZQUFZO29CQUNkLE1BQU1NLGtCQUFrQkYsY0FBY2xCLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsVUFBVTtvQkFDOURaLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0M2QixnQkFBZ0JqQixHQUFHLENBQUNGLENBQUFBLElBQUtBLEVBQUVaLEVBQUU7Z0JBQzdFO2dCQUVBLE9BQU82QjtZQUNUO1lBRUEsb0JBQW9CO1lBQ3BCLE1BQU10QixXQUFXLEdBQTBCdkUsT0FBdkJBLGlCQUFpQmdFLEVBQUUsRUFBQyxLQUF5QixPQUF0QmhFLGlCQUFpQjBELElBQUk7WUFDaEU3QixlQUFlbUUsQ0FBQUE7Z0JBQ2IsTUFBTUMsZUFBZUQsS0FBS3ZCLEdBQUcsQ0FBQ0Y7Z0JBQzlCLElBQUkwQixjQUFjO29CQUNoQixNQUFNSixnQkFBZ0JJLGFBQWEvRixNQUFNLENBQUM0RSxHQUFHLENBQUNnQixDQUFBQSxRQUFVOzRCQUN0RCxHQUFHQSxLQUFLOzRCQUNSakIsWUFBWU0sWUFBWVEsR0FBRyxDQUFDRyxNQUFNOUIsRUFBRTt3QkFDdEM7b0JBQ0EsT0FBTyxJQUFJbEMsSUFBSWtFLE1BQU1FLEdBQUcsQ0FBQzNCLFVBQVU7d0JBQ2pDckUsUUFBUTJGO3dCQUNSeEMsV0FBVzRDLGFBQWE1QyxTQUFTO29CQUNuQztnQkFDRjtnQkFDQSxPQUFPMkM7WUFDVDtRQUNGLEVBQUUsT0FBT3ZGLE9BQU87WUFDZHdELFFBQVF4RCxLQUFLLENBQUMsNENBQTRDQTtRQUM1RDtJQUNGO0lBRUEsd0JBQXdCO0lBQ3hCL0IsZ0RBQVNBLENBQUM7UUFDUjZDLG1CQUFtQnJDLHdGQUFzQkEsQ0FBQ2lILGtCQUFrQjtJQUM5RCxHQUFHLEVBQUU7SUFFTCxpREFBaUQ7SUFDakQsTUFBTWpCLDJCQUEyQjtRQUMvQixJQUFJLEVBQUNyRixpQkFBQUEsMkJBQUFBLEtBQU11RyxLQUFLLEdBQUUsT0FBTztRQUV6QixJQUFJO1lBQ0YsTUFBTSxFQUFFQyxVQUFVLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUFFQyxPQUFPLEVBQUUsR0FBRyxNQUFNLDZMQUFPO1lBQzNELE1BQU1DLGNBQWNKLFdBQVd6SCw2Q0FBRUEsRUFBRTtZQUNuQyxNQUFNOEgsSUFBSUosTUFBTUcsYUFBYUYsTUFBTSxTQUFTLE1BQU0xRyxLQUFLdUcsS0FBSztZQUM1RCxNQUFNTyxnQkFBZ0IsTUFBTUgsUUFBUUU7WUFFcEMsSUFBSSxDQUFDQyxjQUFjQyxLQUFLLEVBQUU7Z0JBQ3hCLE1BQU1DLFVBQVVGLGNBQWNHLElBQUksQ0FBQyxFQUFFO2dCQUNyQyxPQUFPRCxRQUFRRSxJQUFJLEdBQUc5QixRQUFRLElBQUk0QixRQUFRN0MsRUFBRTtZQUM5QztZQUVBLE9BQU87UUFDVCxFQUFFLE9BQU92RCxPQUFPO1lBQ2R3RCxRQUFReEQsS0FBSyxDQUFDLDRCQUE0QkE7WUFDMUMsT0FBTztRQUNUO0lBQ0Y7SUFFQSxNQUFNbUQsZ0JBQWdCO1FBQ3BCLElBQUksQ0FBQy9ELE1BQU07WUFDVG9FLFFBQVFDLEdBQUcsQ0FBQztZQUNaO1FBQ0Y7UUFFQTFELFdBQVc7UUFDWEUsU0FBUztRQUVULElBQUk7WUFDRixNQUFNdUUsV0FBVyxNQUFNQztZQUN2QixNQUFNOEIsZ0JBQWdCLE1BQU1uSSxrRkFBbUJBLENBQUNvRztZQUVoRCxrQkFBa0I7WUFDbEJqRCxrQkFBa0I7Z0JBQ2hCbEMsV0FBV2tIO2dCQUNYM0QsV0FBV0YsS0FBS0MsR0FBRztZQUNyQjtZQUVBckQsYUFBYWlIO1lBRWIsc0VBQXNFO1lBQ3RFLE1BQU16RCxxQkFBcUJ5RCxjQUFjeEQsSUFBSSxDQUFDQyxDQUFBQSxLQUFNQSxHQUFHQyxJQUFJLEtBQUs7WUFDaEUsTUFBTUMsbUJBQW1CcUQsY0FBY3hELElBQUksQ0FBQ0MsQ0FBQUEsS0FBTUEsR0FBR0MsSUFBSSxLQUFLO1lBRTlELElBQUlILG9CQUFvQjtnQkFDdEJ0RCxvQkFBb0JzRDtZQUN0QixPQUFPLElBQUlJLGtCQUFrQjtnQkFDM0IxRCxvQkFBb0IwRDtZQUN0QixPQUFPLElBQUlxRCxjQUFjMUQsTUFBTSxHQUFHLEdBQUc7Z0JBQ25DckQsb0JBQW9CK0csYUFBYSxDQUFDLEVBQUU7WUFDdEM7UUFDRixFQUFFLE9BQU92RyxPQUFPO1lBQ2R3RCxRQUFReEQsS0FBSyxDQUFDLDRCQUE0QkE7WUFDMUNDLFNBQVMsaUNBQWlDLE1BQWlCdUcsT0FBTztRQUNwRSxTQUFVO1lBQ1J6RyxXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU11RSx1QkFBdUI7UUFDM0IsSUFBSSxDQUFDL0Usb0JBQW9CLENBQUNILE1BQU07UUFFaENXLFdBQVc7UUFDWEUsU0FBUztRQUVULElBQUk7WUFDRiw4QkFBOEI7WUFDOUIsTUFBTXdHLG1CQUFtQixNQUFNcEksOEVBQWlCQSxDQUFDcUksV0FBVztZQUU1RCx1RkFBdUY7WUFDdkYsTUFBTWxDLFdBQVcsTUFBTUM7WUFDdkIsTUFBTUMsY0FBYyxNQUFNbkcsc0ZBQXFCQSxDQUFDb0csbUJBQW1CLENBQUNILFVBQVVqRixpQkFBaUJnRSxFQUFFO1lBRWpHLElBQUltQixZQUFZaUMsSUFBSSxHQUFHLEdBQUc7Z0JBQ3hCbkQsUUFBUUMsR0FBRyxDQUFDLHdCQUF3Qm9CLE1BQU1DLElBQUksQ0FBQ0o7WUFDakQ7WUFFQSx1QkFBdUI7WUFDdkIsTUFBTWtDLHNCQUFzQkgsaUJBQWlCcEMsR0FBRyxDQUFDZ0IsQ0FBQUEsUUFBVTtvQkFDekQsR0FBR0EsS0FBSztvQkFDUmpCLFlBQVlNLFlBQVlRLEdBQUcsQ0FBQ0csTUFBTTlCLEVBQUU7Z0JBQ3RDO1lBRUEsa0JBQWtCO1lBQ2xCLE1BQU1PLFdBQVcsR0FBMEJ2RSxPQUF2QkEsaUJBQWlCZ0UsRUFBRSxFQUFDLEtBQXlCLE9BQXRCaEUsaUJBQWlCMEQsSUFBSTtZQUNoRTdCLGVBQWVtRSxDQUFBQSxPQUFRLElBQUlsRSxJQUFJa0UsTUFBTUUsR0FBRyxDQUFDM0IsVUFBVTtvQkFDakRyRSxRQUFRbUg7b0JBQ1JoRSxXQUFXRixLQUFLQyxHQUFHO2dCQUNyQjtZQUVBakQsVUFBVWtIO1lBQ1ZoSCxvQkFBb0I4RTtZQUNwQnhELHNCQUFzQjNCLGlCQUFpQmdFLEVBQUU7WUFFekMsTUFBTXNELGlCQUFpQkQsb0JBQW9CMUMsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxVQUFVLEVBQUV2QixNQUFNO1lBQzNFLElBQUlnRSxpQkFBaUIsR0FBRztnQkFDdEJyRCxRQUFRQyxHQUFHLENBQUMsaUNBQWlDb0QsZ0JBQWdCO1lBQy9EO1FBQ0YsRUFBRSxPQUFPN0csT0FBTztZQUNkd0QsUUFBUXhELEtBQUssQ0FBQyx5QkFBeUJBO1lBQ3ZDQyxTQUFTO1FBQ1gsU0FBVTtZQUNSRixXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU13RSxxQkFBcUI7UUFDekIsSUFBSSxDQUFDaEYsb0JBQW9CLENBQUNILE1BQU07UUFFaENXLFdBQVc7UUFDWEUsU0FBUztRQUVULElBQUk7WUFDRiw0QkFBNEI7WUFDNUIsTUFBTTZHLGlCQUFpQixNQUFNeEksMEVBQWVBLENBQUNvSSxXQUFXO1lBRXhELGtCQUFrQjtZQUNsQixNQUFNNUMsV0FBVyxHQUEwQnZFLE9BQXZCQSxpQkFBaUJnRSxFQUFFLEVBQUMsS0FBeUIsT0FBdEJoRSxpQkFBaUIwRCxJQUFJO1lBQ2hFN0IsZUFBZW1FLENBQUFBLE9BQVEsSUFBSWxFLElBQUlrRSxNQUFNRSxHQUFHLENBQUMzQixVQUFVO29CQUNqRHJFLFFBQVFxSDtvQkFDUmxFLFdBQVdGLEtBQUtDLEdBQUc7Z0JBQ3JCO1lBRUFqRCxVQUFVb0g7WUFDVjVGLHNCQUFzQjNCLGlCQUFpQmdFLEVBQUU7UUFDM0MsRUFBRSxPQUFPdkQsT0FBTztZQUNkd0QsUUFBUXhELEtBQUssQ0FBQyxrQ0FBa0NBO1lBQ2hEQyxTQUFTO1FBQ1gsU0FBVTtZQUNSRixXQUFXO1FBQ2I7SUFDRjtJQUVBLHNDQUFzQztJQUN0QyxNQUFNZ0gsb0JBQW9CO1FBQ3hCLElBQUlDLFdBQVc7ZUFBSXZIO1NBQU87UUFFMUIsb0VBQW9FO1FBQ3BFLElBQUlGLENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCMEQsSUFBSSxNQUFLLFlBQVk7WUFDekMsSUFBSXpCLFFBQVFFLFFBQVEsS0FBSyxhQUFhO2dCQUNwQ3NGLFdBQVcsRUFBRTtZQUNmLE9BQU87Z0JBQ0xBLFdBQVcxSSwwRUFBZUEsQ0FBQzJJLGdCQUFnQixDQUFDRCxVQUFVeEYsUUFBUUUsUUFBUTtZQUN4RTtRQUNGLE9BQU87WUFDTHNGLFdBQVczSSw4RUFBaUJBLENBQUM0SSxnQkFBZ0IsQ0FBQ0QsVUFBVXhGLFFBQVFFLFFBQVE7UUFDMUU7UUFFQSxnR0FBZ0c7UUFDaEcsSUFBSU8sZUFBZUwsV0FBV3NGLElBQUksSUFBSTtZQUNwQyxNQUFNQyxxQkFBcUJyRixjQUFjdUMsR0FBRyxDQUFDK0MsQ0FBQUEsU0FBVUEsT0FBTy9CLEtBQUs7WUFDbkUsZ0dBQWdHO1lBQ2hHMkIsV0FBV0csbUJBQW1CakQsTUFBTSxDQUFDbUIsQ0FBQUEsUUFBUzJCLFNBQVNLLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRS9ELEVBQUUsS0FBSzhCLE1BQU05QixFQUFFO1FBQ3BGLE9BQU8sSUFBSTVDLGtCQUFrQjtZQUMzQixvRUFBb0U7WUFDcEUsTUFBTTRHLG1CQUFtQjlJLHdGQUFzQkEsQ0FBQytJLG1CQUFtQixDQUFDUixVQUFVckc7WUFDOUVxRyxXQUFXTztRQUNiO1FBRUEsMENBQTBDO1FBQzFDLElBQUksQ0FBQ3RGLGVBQWUsQ0FBQ0wsV0FBV3NGLElBQUksSUFBSTtZQUN0QyxNQUFNTyxVQUFVbEksQ0FBQUEsNkJBQUFBLHVDQUFBQSxpQkFBa0IwRCxJQUFJLE1BQUssYUFBYTNFLDBFQUFlQSxHQUFHRCw4RUFBaUJBO1lBQzNGMkksV0FBV1MsUUFBUUMsVUFBVSxDQUFDVixVQUFVeEYsUUFBUUcsTUFBTTtRQUN4RDtRQUVBLE9BQU9xRjtJQUNUO0lBRUEsTUFBTVcsaUJBQWlCWjtJQUV2QixNQUFNYSx1QkFBdUIsT0FBT3ZDO1FBQ2xDLElBQUksQ0FBQ2pHLFFBQVEsQ0FBQ0csa0JBQWtCO1FBRWhDLDhEQUE4RDtRQUM5RCxJQUFJd0IsaUJBQWlCbUUsR0FBRyxDQUFDRyxNQUFNOUIsRUFBRSxHQUFHO1lBQ2xDQyxRQUFRQyxHQUFHLENBQUMsd0NBQXdDNEIsTUFBTTlCLEVBQUU7WUFDNUQ7UUFDRjtRQUVBQyxRQUFRQyxHQUFHLENBQUMsc0JBQW1DNEIsT0FBYkEsTUFBTTlCLEVBQUUsRUFBQyxNQUE2QyxPQUF6QzhCLE1BQU1qQixVQUFVLEdBQUcsYUFBYSxVQUFTO1FBRXhGLHlDQUF5QztRQUN6QyxNQUFNeUQsc0JBQXNCLENBQUN4QyxNQUFNakIsVUFBVTtRQUU3QyxJQUFJO1lBQ0YsMEJBQTBCO1lBQzFCcEQsb0JBQW9CdUUsQ0FBQUEsT0FBUSxJQUFJMUYsSUFBSTBGLE1BQU11QyxHQUFHLENBQUN6QyxNQUFNOUIsRUFBRTtZQUV0RCxxREFBcUQ7WUFDckQsTUFBTXdFLHFCQUFxQixJQUFJbEksSUFBSUY7WUFDbkMsSUFBSWtJLHFCQUFxQjtnQkFDdkJFLG1CQUFtQkQsR0FBRyxDQUFDekMsTUFBTTlCLEVBQUU7WUFDakMsT0FBTztnQkFDTHdFLG1CQUFtQkMsTUFBTSxDQUFDM0MsTUFBTTlCLEVBQUU7WUFDcEM7WUFDQTNELG9CQUFvQm1JO1lBRXBCLDZDQUE2QztZQUM3Q3JJLFVBQVV5RixDQUFBQSxhQUNSQSxXQUFXZCxHQUFHLENBQUNGLENBQUFBLElBQ2JBLEVBQUVaLEVBQUUsS0FBSzhCLE1BQU05QixFQUFFLEdBQUc7d0JBQUUsR0FBR1ksQ0FBQzt3QkFBRUMsWUFBWXlEO29CQUFvQixJQUFJMUQ7WUFJcEUsa0NBQWtDO1lBQ2xDLElBQUk1RSxrQkFBa0I7Z0JBQ3BCLE1BQU11RSxXQUFXLEdBQTBCdkUsT0FBdkJBLGlCQUFpQmdFLEVBQUUsRUFBQyxLQUF5QixPQUF0QmhFLGlCQUFpQjBELElBQUk7Z0JBQ2hFN0IsZUFBZW1FLENBQUFBO29CQUNiLE1BQU1DLGVBQWVELEtBQUt2QixHQUFHLENBQUNGO29CQUM5QixJQUFJMEIsY0FBYzt3QkFDaEIsTUFBTUosZ0JBQWdCSSxhQUFhL0YsTUFBTSxDQUFDNEUsR0FBRyxDQUFDRixDQUFBQSxJQUM1Q0EsRUFBRVosRUFBRSxLQUFLOEIsTUFBTTlCLEVBQUUsR0FBRztnQ0FBRSxHQUFHWSxDQUFDO2dDQUFFQyxZQUFZeUQ7NEJBQW9CLElBQUkxRDt3QkFFbEUsT0FBTyxJQUFJOUMsSUFBSWtFLE1BQU1FLEdBQUcsQ0FBQzNCLFVBQVU7NEJBQ2pDckUsUUFBUTJGOzRCQUNSeEMsV0FBVzRDLGFBQWE1QyxTQUFTO3dCQUNuQztvQkFDRjtvQkFDQSxPQUFPMkM7Z0JBQ1Q7WUFDRjtZQUVBLHNDQUFzQztZQUN0QyxNQUFNZixXQUFXLE1BQU1DO1lBQ3ZCLE1BQU13RCxrQkFBa0IsTUFBTTFKLHNGQUFxQkEsQ0FBQzJKLGNBQWMsQ0FDaEUxRCxVQUNBakYsaUJBQWlCZ0UsRUFBRSxFQUNuQjhCLE1BQU05QixFQUFFLEVBQ1I4QixNQUFNcEMsSUFBSTtZQUdaLGlDQUFpQztZQUNqQyxJQUFJZ0Ysb0JBQW9CSixxQkFBcUI7Z0JBQzNDckUsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQ29FLHFCQUFxQixXQUFXSTtZQUMvRTtZQUVBLHVEQUF1RDtZQUN2RCxJQUFJQSxvQkFBb0JKLHFCQUFxQjtnQkFDM0NyRSxRQUFRMkUsSUFBSSxDQUFDO2dCQUViLGtDQUFrQztnQkFDbEMsTUFBTUMsdUJBQXVCLElBQUl2SSxJQUFJRjtnQkFDckMsSUFBSXNJLGlCQUFpQjtvQkFDbkJHLHFCQUFxQk4sR0FBRyxDQUFDekMsTUFBTTlCLEVBQUU7Z0JBQ25DLE9BQU87b0JBQ0w2RSxxQkFBcUJKLE1BQU0sQ0FBQzNDLE1BQU05QixFQUFFO2dCQUN0QztnQkFDQTNELG9CQUFvQndJO2dCQUVwQiw4QkFBOEI7Z0JBQzlCMUksVUFBVXlGLENBQUFBLGFBQ1JBLFdBQVdkLEdBQUcsQ0FBQ0YsQ0FBQUEsSUFDYkEsRUFBRVosRUFBRSxLQUFLOEIsTUFBTTlCLEVBQUUsR0FBRzs0QkFBRSxHQUFHWSxDQUFDOzRCQUFFQyxZQUFZNkQ7d0JBQWdCLElBQUk5RDtnQkFJaEUsbUJBQW1CO2dCQUNuQixJQUFJNUUsa0JBQWtCO29CQUNwQixNQUFNdUUsV0FBVyxHQUEwQnZFLE9BQXZCQSxpQkFBaUJnRSxFQUFFLEVBQUMsS0FBeUIsT0FBdEJoRSxpQkFBaUIwRCxJQUFJO29CQUNoRTdCLGVBQWVtRSxDQUFBQTt3QkFDYixNQUFNQyxlQUFlRCxLQUFLdkIsR0FBRyxDQUFDRjt3QkFDOUIsSUFBSTBCLGNBQWM7NEJBQ2hCLE1BQU02QyxrQkFBa0I3QyxhQUFhL0YsTUFBTSxDQUFDNEUsR0FBRyxDQUFDRixDQUFBQSxJQUM5Q0EsRUFBRVosRUFBRSxLQUFLOEIsTUFBTTlCLEVBQUUsR0FBRztvQ0FBRSxHQUFHWSxDQUFDO29DQUFFQyxZQUFZNkQ7Z0NBQWdCLElBQUk5RDs0QkFFOUQsT0FBTyxJQUFJOUMsSUFBSWtFLE1BQU1FLEdBQUcsQ0FBQzNCLFVBQVU7Z0NBQ2pDckUsUUFBUTRJO2dDQUNSekYsV0FBVzRDLGFBQWE1QyxTQUFTOzRCQUNuQzt3QkFDRjt3QkFDQSxPQUFPMkM7b0JBQ1Q7Z0JBQ0Y7WUFDRjtRQUNGLEVBQUUsT0FBT3ZGLE9BQU87WUFDZHdELFFBQVF4RCxLQUFLLENBQUMsNEJBQTRCQTtZQUUxQyxtREFBbUQ7WUFDbkQsTUFBTXNJLHNCQUFzQixJQUFJekksSUFBSUY7WUFDcEMsSUFBSSxDQUFDa0kscUJBQXFCO2dCQUN4QlMsb0JBQW9CUixHQUFHLENBQUN6QyxNQUFNOUIsRUFBRTtZQUNsQyxPQUFPO2dCQUNMK0Usb0JBQW9CTixNQUFNLENBQUMzQyxNQUFNOUIsRUFBRTtZQUNyQztZQUNBM0Qsb0JBQW9CMEk7WUFFcEIsOEJBQThCO1lBQzlCNUksVUFBVXlGLENBQUFBLGFBQ1JBLFdBQVdkLEdBQUcsQ0FBQ0YsQ0FBQUEsSUFDYkEsRUFBRVosRUFBRSxLQUFLOEIsTUFBTTlCLEVBQUUsR0FBRzt3QkFBRSxHQUFHWSxDQUFDO3dCQUFFQyxZQUFZLENBQUN5RDtvQkFBb0IsSUFBSTFEO1lBSXJFLG1CQUFtQjtZQUNuQixJQUFJNUUsa0JBQWtCO2dCQUNwQixNQUFNdUUsV0FBVyxHQUEwQnZFLE9BQXZCQSxpQkFBaUJnRSxFQUFFLEVBQUMsS0FBeUIsT0FBdEJoRSxpQkFBaUIwRCxJQUFJO2dCQUNoRTdCLGVBQWVtRSxDQUFBQTtvQkFDYixNQUFNQyxlQUFlRCxLQUFLdkIsR0FBRyxDQUFDRjtvQkFDOUIsSUFBSTBCLGNBQWM7d0JBQ2hCLE1BQU0rQyxpQkFBaUIvQyxhQUFhL0YsTUFBTSxDQUFDNEUsR0FBRyxDQUFDRixDQUFBQSxJQUM3Q0EsRUFBRVosRUFBRSxLQUFLOEIsTUFBTTlCLEVBQUUsR0FBRztnQ0FBRSxHQUFHWSxDQUFDO2dDQUFFQyxZQUFZLENBQUN5RDs0QkFBb0IsSUFBSTFEO3dCQUVuRSxPQUFPLElBQUk5QyxJQUFJa0UsTUFBTUUsR0FBRyxDQUFDM0IsVUFBVTs0QkFDakNyRSxRQUFROEk7NEJBQ1IzRixXQUFXNEMsYUFBYTVDLFNBQVM7d0JBQ25DO29CQUNGO29CQUNBLE9BQU8yQztnQkFDVDtZQUNGO1FBQ0YsU0FBVTtZQUNSLHFDQUFxQztZQUNyQ3ZFLG9CQUFvQnVFLENBQUFBO2dCQUNsQixNQUFNaUQsU0FBUyxJQUFJM0ksSUFBSTBGO2dCQUN2QmlELE9BQU9SLE1BQU0sQ0FBQzNDLE1BQU05QixFQUFFO2dCQUN0QixPQUFPaUY7WUFDVDtRQUNGO0lBQ0Y7SUFFQSwwRUFBMEU7SUFDMUUsTUFBTUMsbUJBQW1CLENBQUNwRDtRQUN4QixJQUFJOUYsQ0FBQUEsNkJBQUFBLHVDQUFBQSxpQkFBa0IwRCxJQUFJLE1BQUssY0FBYyxPQUFPO1FBQ3BELE1BQU15RixhQUFhckssOEVBQWlCQSxDQUFDc0ssYUFBYSxDQUFDdEQ7UUFDbkQsT0FBT3FELGFBQWEsU0FBUyx5Q0FBeUM7SUFDeEU7SUFFQSxNQUFNRSxvQkFBb0IsQ0FBQ3ZEO1FBQ3pCLDZCQUE2QjtRQUM3QmxELG9CQUFvQmtELE1BQU05QixFQUFFO1FBRTVCLDhDQUE4QztRQUM5QyxJQUFJa0YsaUJBQWlCcEQsUUFBUTtZQUMzQjNFLHlCQUF5QjJFO1lBQ3pCN0UsMkJBQTJCO1FBQzdCLE9BQU87WUFDTHJCLGNBQWNrRyxNQUFNOUIsRUFBRTtZQUN0QnRFO1FBQ0Y7SUFDRjtJQUVBLE1BQU00Siw4QkFBOEI7UUFDbEMsSUFBSXBJLHVCQUF1QjtZQUN6QixrQ0FBa0M7WUFDbEMwQixvQkFBb0IxQixzQkFBc0I4QyxFQUFFO1lBQzVDcEUsY0FBY3NCLHNCQUFzQjhDLEVBQUU7WUFDdEMvQywyQkFBMkI7WUFDM0JFLHlCQUF5QjtZQUN6QnpCO1FBQ0Y7SUFDRjtJQUVBLE1BQU02Siw2QkFBNkI7UUFDakN0SSwyQkFBMkI7UUFDM0JFLHlCQUF5QjtJQUMzQjtJQUVBLE1BQU1xSSx1QkFBdUI7UUFDM0I1SSx3QkFBd0JvRixDQUFBQSxPQUFRQSxPQUFPbkY7SUFDekM7SUFFQSxNQUFNNEksdUJBQXVCO1FBQzNCLElBQUkzSSxjQUFjNkcsSUFBSSxJQUFJO1lBQ3hCL0gsY0FBY2tCLGNBQWM2RyxJQUFJO1lBQ2hDakk7UUFDRjtJQUNGO0lBRUEseUNBQXlDO0lBQ3pDLE1BQU1nSyxzQkFBc0I7UUFDMUIsSUFBSTFKLGtCQUFrQjtZQUNwQixNQUFNdUUsV0FBVyxHQUEwQnZFLE9BQXZCQSxpQkFBaUJnRSxFQUFFLEVBQUMsS0FBeUIsT0FBdEJoRSxpQkFBaUIwRCxJQUFJO1lBQ2hFN0IsZUFBZW1FLENBQUFBO2dCQUNiLE1BQU0yRCxXQUFXLElBQUk3SCxJQUFJa0U7Z0JBQ3pCMkQsU0FBU2xCLE1BQU0sQ0FBQ2xFO2dCQUNoQixPQUFPb0Y7WUFDVDtZQUdBLElBQUkzSixpQkFBaUIwRCxJQUFJLEtBQUssY0FBYztnQkFDMUNxQjtZQUNGLE9BQU8sSUFBSS9FLGlCQUFpQjBELElBQUksS0FBSyxZQUFZO2dCQUMvQ3NCO1lBQ0Y7UUFDRjtJQUNGO0lBTUEsSUFBSSxDQUFDdkYsUUFBUSxPQUFPO0lBRXBCLHFCQUNFLDhEQUFDbUs7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FHZiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNDO29EQUFJRCxXQUFVO29EQUFxQkUsTUFBSztvREFBT0MsUUFBTztvREFBZUMsU0FBUTs4REFDNUUsNEVBQUNDO3dEQUFLQyxlQUFjO3dEQUFRQyxnQkFBZTt3REFBUUMsYUFBYTt3REFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OzswREFHekUsOERBQUNDO2dEQUFHVixXQUFVOzBEQUFzQzs7Ozs7Ozs7Ozs7O2tEQUV0RCw4REFBQ1c7d0NBQ0NDLFNBQVMvSzt3Q0FDVG1LLFdBQVU7a0RBRVYsNEVBQUNDOzRDQUFJRCxXQUFVOzRDQUFVRSxNQUFLOzRDQUFPQyxRQUFPOzRDQUFlQyxTQUFRO3NEQUNqRSw0RUFBQ0M7Z0RBQUtDLGVBQWM7Z0RBQVFDLGdCQUFlO2dEQUFRQyxhQUFhO2dEQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU0zRSw4REFBQ1Y7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNhO2dEQUFNYixXQUFVOzBEQUEwQzs7Ozs7OzBEQUMzRCw4REFBQ1c7Z0RBQ0NDLFNBQVNmO2dEQUNUaUIsVUFBVXBLLFdBQVcsQ0FBQ1A7Z0RBQ3RCNkosV0FBVTtnREFDVmUsT0FBTTswREFFTiw0RUFBQ2Q7b0RBQUlELFdBQVcsV0FBeUMsT0FBOUJ0SixVQUFVLGlCQUFpQjtvREFBTXdKLE1BQUs7b0RBQU9DLFFBQU87b0RBQWVDLFNBQVE7OERBQ3BHLDRFQUFDQzt3REFBS0MsZUFBYzt3REFBUUMsZ0JBQWU7d0RBQVFDLGFBQWE7d0RBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSTNFLDhEQUFDTzt3Q0FDQ0MsT0FBTzlLLENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCZ0UsRUFBRSxLQUFJO3dDQUMvQitHLFVBQVUsQ0FBQ0M7NENBQ1QsTUFBTUMsV0FBV25MLFVBQVUwRCxJQUFJLENBQUNDLENBQUFBLEtBQU1BLEdBQUdPLEVBQUUsS0FBS2dILEVBQUVFLE1BQU0sQ0FBQ0osS0FBSzs0Q0FDOUQ3SyxvQkFBb0JnTCxZQUFZO3dDQUNsQzt3Q0FDQXBCLFdBQVU7OzBEQUVWLDhEQUFDc0I7Z0RBQU9MLE9BQU07MERBQUc7Ozs7Ozs0Q0FDaEJoTCxVQUFVZ0YsR0FBRyxDQUFDbUcsQ0FBQUEseUJBQ2IsOERBQUNFO29EQUF5QkwsT0FBT0csU0FBU2pILEVBQUU7b0RBQUU2RixXQUFVOzhEQUNyRG9CLFNBQVN2SCxJQUFJO21EQURIdUgsU0FBU2pILEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVEvQmhFLENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCMEQsSUFBSSxNQUFLLDhCQUMxQjs7MENBRUUsOERBQUNrRztnQ0FBSUMsV0FBVTs7a0RBRWIsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ3VCO2dEQUFHdkIsV0FBVTs7a0VBQ1osOERBQUNDO3dEQUFJRCxXQUFVO3dEQUF3QkUsTUFBSzt3REFBT0MsUUFBTzt3REFBZUMsU0FBUTtrRUFDL0UsNEVBQUNDOzREQUFLQyxlQUFjOzREQUFRQyxnQkFBZTs0REFBUUMsYUFBYTs0REFBR0MsR0FBRTs7Ozs7Ozs7Ozs7a0VBRXZFLDhEQUFDZTtrRUFBSzs7Ozs7Ozs7Ozs7OzBEQUVSLDhEQUFDekI7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDeUI7d0RBQ0NDLE1BQUs7d0RBQ0xDLGFBQVk7d0RBQ1pWLE9BQU9oSzt3REFDUGlLLFVBQVUsQ0FBQ0MsSUFBTWpLLGlCQUFpQmlLLEVBQUVFLE1BQU0sQ0FBQ0osS0FBSzt3REFDaERXLFdBQVcsQ0FBQ1QsSUFBTUEsRUFBRVUsR0FBRyxLQUFLLFdBQVdqQzt3REFDdkNJLFdBQVU7Ozs7OztrRUFFWiw4REFBQ1c7d0RBQ0NDLFNBQVNoQjt3REFDVGtCLFVBQVUsQ0FBQzdKLGNBQWM2RyxJQUFJO3dEQUM3QmtDLFdBQVU7a0VBQ1g7Ozs7Ozs7Ozs7OzswREFJSCw4REFBQzhCO2dEQUFFOUIsV0FBVTswREFBZ0M7Ozs7Ozs7Ozs7OztrREFNL0MsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNaOzRDQUFFOzRDQUFROzRDQUFRO3lDQUFZLENBQXFCL0UsR0FBRyxDQUFDM0MsQ0FBQUEseUJBQ3RELDhEQUFDcUk7Z0RBRUNDLFNBQVMsSUFBTXZJLFdBQVc4RCxDQUFBQSxPQUFTOzREQUFFLEdBQUdBLElBQUk7NERBQUU3RDt3REFBUztnREFDdkQwSCxXQUFXLCtFQUlWLE9BSEM1SCxRQUFRRSxRQUFRLEtBQUtBLFdBQ2pCLG9FQUNBOzBEQUdMQSxhQUFhLFNBQVMsVUFBVUEsYUFBYSxTQUFTLGNBQVc7K0NBUjdEQTs7Ozs7Ozs7OztrREFjWCw4REFBQ3lIO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQzFLLHdFQUFtQkE7NERBQ2xCMkwsT0FBT3pJOzREQUNQMEksVUFBVXpJOzREQUNWRSxhQUFhLEVBQUU7NERBQ2ZDLGFBQWFBOzREQUNiK0ksYUFBWTs0REFDWkksaUJBQWlCOzs7Ozs7Ozs7OztrRUFHckIsOERBQUNmO3dEQUNDQyxPQUFPN0ksUUFBUUcsTUFBTTt3REFDckIySSxVQUFVLENBQUNDLElBQU05SSxXQUFXOEQsQ0FBQUEsT0FBUztvRUFBRSxHQUFHQSxJQUFJO29FQUFFNUQsUUFBUTRJLEVBQUVFLE1BQU0sQ0FBQ0osS0FBSztnRUFBZ0I7d0RBQ3RGakIsV0FBVTt3REFDVmMsVUFBVWpJLGVBQWVMLFdBQVdzRixJQUFJLEdBQUdyRSxNQUFNLEdBQUc7OzBFQUVwRCw4REFBQzZIO2dFQUFPTCxPQUFNO2dFQUFTakIsV0FBVTswRUFBNEI7Ozs7OzswRUFDN0QsOERBQUNzQjtnRUFBT0wsT0FBTTtnRUFBWWpCLFdBQVU7MEVBQTRCOzs7Ozs7MEVBQ2hFLDhEQUFDc0I7Z0VBQU9MLE9BQU07Z0VBQWFqQixXQUFVOzBFQUE0Qjs7Ozs7OzBFQUNqRSw4REFBQ3NCO2dFQUFPTCxPQUFNO2dFQUFlakIsV0FBVTswRUFBNEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0Q0FLdEUsQ0FBQ25ILDZCQUNBLDhEQUFDa0g7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDVzt3REFDQ0MsU0FBUyxJQUFNcEosb0JBQW9CO3dEQUNuQ3dJLFdBQVcsMEVBSVYsT0FIQyxDQUFDekksbUJBQ0csMkJBQ0E7a0VBRVA7Ozs7OztvREFHQUUsZ0JBQWdCdUssS0FBSyxDQUFDLEdBQUcsR0FBRy9HLEdBQUcsQ0FBQzNDLENBQUFBLHlCQUMvQiw4REFBQ3FJOzREQUVDQyxTQUFTLElBQU1wSixvQkFBb0JjLFNBQVM2QixFQUFFOzREQUM5QzZGLFdBQVcsc0dBSVYsT0FIQ3pJLHFCQUFxQmUsU0FBUzZCLEVBQUUsR0FDNUIsMkJBQ0E7NERBRU40RyxPQUFPekksU0FBUzJKLFdBQVc7OzhFQUUzQiw4REFBQ1Q7OEVBQU1sSixTQUFTNEosSUFBSTs7Ozs7OzhFQUNwQiw4REFBQ1Y7OEVBQU1sSixTQUFTdUIsSUFBSTs7Ozs7OzsyREFWZnZCLFNBQVM2QixFQUFFOzs7Ozs7Ozs7Ozs0Q0FpQnZCdEIsZUFBZUwsV0FBV3NGLElBQUksb0JBQzdCLDhEQUFDaUM7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDd0I7d0RBQUt4QixXQUFVOzs0REFDYnpCLGVBQWU5RSxNQUFNOzREQUFDOzREQUFXOEUsZUFBZTlFLE1BQU0sS0FBSyxJQUFJLE1BQU07NERBQUc7NERBQVFqQjs0REFBVzs7Ozs7OztrRUFFOUYsOERBQUNtSTt3REFDQ0MsU0FBUzlIO3dEQUNUa0gsV0FBVTtrRUFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVNULDhEQUFDRDtnQ0FBSUMsV0FBVTs7b0NBQ1p0Six5QkFDQyw4REFBQ3FKO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzs7Ozs7OERBQ2YsOERBQUN3QjtvREFBS3hCLFdBQVU7OERBQW9DOzs7Ozs7Ozs7Ozs7Ozs7OztvQ0FLekRwSix1QkFDQyw4REFBQ21KO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDQzt3REFBSUQsV0FBVTt3REFBdUJFLE1BQUs7d0RBQU9DLFFBQU87d0RBQWVDLFNBQVE7a0VBQzlFLDRFQUFDQzs0REFBS0MsZUFBYzs0REFBUUMsZ0JBQWU7NERBQVFDLGFBQWE7NERBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OERBR3pFLDhEQUFDZTtvREFBS3hCLFdBQVU7OERBQTRCcEo7Ozs7Ozs7Ozs7Ozs7Ozs7O29DQUtqRCxDQUFDRixXQUFXLENBQUNFLFNBQVMySCxlQUFlOUUsTUFBTSxLQUFLLG1CQUMvQyw4REFBQ3NHO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNDO29EQUFJRCxXQUFVO29EQUF3QkUsTUFBSztvREFBT0MsUUFBTztvREFBZUMsU0FBUTs4REFDL0UsNEVBQUNDO3dEQUFLQyxlQUFjO3dEQUFRQyxnQkFBZTt3REFBUUMsYUFBYTt3REFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OzswREFHekUsOERBQUNxQjtnREFBRTlCLFdBQVU7MERBQ1ZuSCxlQUFlTCxXQUFXc0YsSUFBSSxLQUMzQiwwQkFBcUMsT0FBWHRGLFlBQVcsT0FDckNqQixtQkFDQywyQ0FDRDs7Ozs7OzBEQUdOLDhEQUFDd0k7Z0RBQUlDLFdBQVU7MERBQ1puSCxlQUFlTCxXQUFXc0YsSUFBSSxtQkFDN0I7O3NFQUNFLDhEQUFDZ0U7c0VBQUU7Ozs7OztzRUFDSCw4REFBQ0s7NERBQUduQyxXQUFVOzs4RUFDWiw4REFBQ29DOzhFQUFHOzs7Ozs7OEVBQ0osOERBQUNBOzhFQUFHOzs7Ozs7OEVBQ0osOERBQUNBOzhFQUFHOzs7Ozs7OEVBQ0osOERBQUNBOzhFQUFHOzs7Ozs7Ozs7Ozs7O2lGQUlSLDhEQUFDTjs4REFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBUVgsOERBQUMvQjt3Q0FBSUMsV0FBVTtrREFDWnpCLGVBQWV5RCxLQUFLLENBQUMsR0FBR2xMLHNCQUFzQm1FLEdBQUcsQ0FBQ2dCLENBQUFBOzRDQUNqRCw4REFBOEQ7NENBQzlELE1BQU1vRyxlQUFleEosY0FBY0gsY0FBY2lCLElBQUksQ0FBQzJJLENBQUFBLElBQUtBLEVBQUVyRyxLQUFLLENBQUM5QixFQUFFLEtBQUs4QixNQUFNOUIsRUFBRSxJQUFJOzRDQUV0RixxQkFDRSw4REFBQ29JO2dEQUVDdEcsT0FBT0E7Z0RBQ1B1RyxZQUFZMU0saUJBQWlCbUcsTUFBTTlCLEVBQUU7Z0RBQ3JDc0ksVUFBVSxJQUFNakQsa0JBQWtCdkQ7Z0RBQ2xDeUcsa0JBQWtCLElBQU1sRSxxQkFBcUJ2QztnREFDN0MwRyxZQUFZaEwsaUJBQWlCbUUsR0FBRyxDQUFDRyxNQUFNOUIsRUFBRTtnREFDekNrRSxTQUFTcEosOEVBQWlCQTtnREFDMUJ1RCxZQUFZSyxjQUFjTCxhQUFhO2dEQUN2QzZKLGNBQWNBOytDQVJUcEcsTUFBTTlCLEVBQUU7Ozs7O3dDQVduQjs7Ozs7O29DQUlELENBQUN6RCxXQUFXLENBQUNFLFNBQVMySCxlQUFlOUUsTUFBTSxHQUFHM0Msc0NBQzdDLDhEQUFDaUo7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNXOzRDQUNDQyxTQUFTakI7NENBQ1RLLFdBQVU7OzhEQUVWLDhEQUFDd0I7OERBQUs7Ozs7Ozs4REFDTiw4REFBQ3ZCO29EQUFJRCxXQUFVO29EQUFVRSxNQUFLO29EQUFPQyxRQUFPO29EQUFlQyxTQUFROzhEQUNqRSw0RUFBQ0M7d0RBQUtDLGVBQWM7d0RBQVFDLGdCQUFlO3dEQUFRQyxhQUFhO3dEQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29DQU81RSxDQUFDL0osV0FBVyxDQUFDRSxTQUFTMkgsZUFBZTlFLE1BQU0sR0FBRyxtQkFDN0MsOERBQUNzRzt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztvREFBMkI7b0RBQzdCNEMsS0FBS0MsR0FBRyxDQUFDL0wsc0JBQXNCeUgsZUFBZTlFLE1BQU07b0RBQUU7b0RBQUs4RSxlQUFlOUUsTUFBTTtvREFBQztvREFDM0ZwRCxPQUFPb0QsTUFBTSxLQUFLOEUsZUFBZTlFLE1BQU0sa0JBQ3RDLDhEQUFDK0g7d0RBQUt4QixXQUFVOzs0REFBcUI7NERBQ2pDM0osT0FBT29ELE1BQU07NERBQUM7Ozs7Ozs7Ozs7Ozs7NENBTXJCWixlQUFlTCxXQUFXc0YsSUFBSSxvQkFDN0IsOERBQUNpQztnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUN3Qjs7NERBQUs7NERBQVdoSjs7Ozs7OztvREFDaEJFLGNBQWNlLE1BQU0sR0FBRyxtQkFDdEIsOERBQUMrSDs7NERBQUs7NERBQUc5SSxjQUFjZSxNQUFNOzREQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBVTdDdEQsQ0FBQUEsNkJBQUFBLHVDQUFBQSxpQkFBa0IwRCxJQUFJLE1BQUssNEJBQzFCOzswQ0FFRSw4REFBQ2tHO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUM4Qzs0Q0FBRzlDLFdBQVU7c0RBQTBDOzs7Ozs7c0RBQ3hELDhEQUFDOEI7NENBQUU5QixXQUFVO3NEQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTzFDLDhEQUFDRDtnQ0FBSUMsV0FBVTs7b0NBQ1p0Six5QkFDQyw4REFBQ3FKO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzs7Ozs7OERBQ2YsOERBQUN3QjtvREFBS3hCLFdBQVU7OERBQW9DOzs7Ozs7Ozs7Ozs7Ozs7OztvQ0FLekRwSix1QkFDQyw4REFBQ21KO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDQzt3REFBSUQsV0FBVTt3REFBdUJFLE1BQUs7d0RBQU9DLFFBQU87d0RBQWVDLFNBQVE7a0VBQzlFLDRFQUFDQzs0REFBS0MsZUFBYzs0REFBUUMsZ0JBQWU7NERBQVFDLGFBQWE7NERBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OERBR3pFLDhEQUFDZTtvREFBS3hCLFdBQVU7OERBQTRCcEo7Ozs7Ozs7Ozs7Ozs7Ozs7O29DQUtqRCxDQUFDRixXQUFXLENBQUNFLFNBQVNQLE9BQU9vRCxNQUFNLEtBQUssbUJBQ3ZDLDhEQUFDc0c7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ0M7b0RBQUlELFdBQVU7b0RBQXdCRSxNQUFLO29EQUFPQyxRQUFPO29EQUFlQyxTQUFROzhEQUMvRSw0RUFBQ0M7d0RBQUtDLGVBQWM7d0RBQVFDLGdCQUFlO3dEQUFRQyxhQUFhO3dEQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzBEQUd6RSw4REFBQ3FCO2dEQUFFOUIsV0FBVTswREFBNEI7Ozs7OzswREFDekMsOERBQUM4QjtnREFBRTlCLFdBQVU7MERBQWdDOzs7Ozs7Ozs7Ozs7b0NBSWhELENBQUN0SixXQUFXLENBQUNFLFNBQVNQLE9BQU9vRCxNQUFNLEdBQUcsbUJBQ3JDLDhEQUFDc0c7d0NBQUlDLFdBQVU7a0RBQ1ozSixPQUFPNEUsR0FBRyxDQUFDZ0IsQ0FBQUEsc0JBQ1YsOERBQUM4RztnREFFQzlHLE9BQU9BO2dEQUNQdUcsWUFBWTFNLGlCQUFpQm1HLE1BQU05QixFQUFFO2dEQUNyQ3NJLFVBQVUsSUFBTWpELGtCQUFrQnZEOytDQUg3QkEsTUFBTTlCLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFZMUJoRSxDQUFBQSw2QkFBQUEsdUNBQUFBLGlCQUFrQjBELElBQUksTUFBSyxnQkFBZ0IxRCxDQUFBQSw2QkFBQUEsdUNBQUFBLGlCQUFrQjBELElBQUksTUFBSyxjQUFjMUQsa0NBQ25GLDhEQUFDNEo7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0M7b0NBQUlELFdBQVU7b0NBQXdCRSxNQUFLO29DQUFPQyxRQUFPO29DQUFlQyxTQUFROzhDQUMvRSw0RUFBQ0M7d0NBQUtDLGVBQWM7d0NBQVFDLGdCQUFlO3dDQUFRQyxhQUFhO3dDQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzBDQUd6RSw4REFBQ3FCO2dDQUFFOUIsV0FBVTswQ0FBNEI7Ozs7OzswQ0FDekMsOERBQUM4QjtnQ0FBRTlCLFdBQVU7MENBQWdDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTW5ELDhEQUFDeEsseUVBQStCQTtnQkFDOUJJLFFBQVF1QjtnQkFDUjhFLE9BQU81RTtnQkFDUDJMLFdBQVd2RDtnQkFDWHdELFVBQVV2RDs7Ozs7Ozs7Ozs7O0FBSWxCO0dBeDlCTS9KOztRQUNhYiwwREFBT0E7UUFtQ3BCTSx1RUFBaUJBOzs7S0FwQ2pCTztBQXMrQk4sTUFBTTRNLFlBQVk7UUFBQyxFQUNqQnRHLEtBQUssRUFDTHVHLFVBQVUsRUFDVkMsUUFBUSxFQUNSQyxnQkFBZ0IsRUFDaEJDLGFBQWEsS0FBSyxFQUNsQnRFLFVBQVVwSiw4RUFBaUIsRUFDM0J1RCxhQUFhLEVBQUUsRUFDZjZKLFlBQVksRUFDRztJQUNmLE1BQU1hLGNBQWM3RSxZQUFZcEosOEVBQWlCQSxJQUFJQSw4RUFBaUJBLENBQUNzSyxhQUFhLENBQUN0RCxTQUFTLFNBQVMsb0JBQW9CO0lBRTNILDRDQUE0QztJQUM1QyxrR0FBa0c7SUFFbEcscUJBQ0UsOERBQUM4RDtRQUNDb0QsaUJBQWVsSCxNQUFNOUIsRUFBRTtRQUN2QjZGLFdBQVcsa0dBSVYsT0FIQ3dDLGFBQ0ksc0dBQ0E7O1lBR0xVLDZCQUNDLDhEQUFDbkQ7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTt3QkFBcUJFLE1BQUs7d0JBQU9DLFFBQU87d0JBQWVDLFNBQVE7a0NBQzVFLDRFQUFDQzs0QkFBS0MsZUFBYzs0QkFBUUMsZ0JBQWU7NEJBQVFDLGFBQWE7NEJBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNN0UsOERBQUNWO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDOEM7d0RBQUc5QyxXQUFVO2tFQUNYcUMsQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjZSxlQUFlLGtCQUM1Qiw4REFBQzVCOzREQUFLNkIseUJBQXlCO2dFQUFFQyxRQUFRakIsYUFBYWUsZUFBZTs0REFBQzs7Ozs7d0VBQ3BFNUssMkJBQ0YsOERBQUNqRCw2RUFBZUE7NERBQUNnTyxNQUFNdEgsTUFBTXBDLElBQUk7NERBQUUySixXQUFXaEw7Ozs7O3dFQUU5Q3lELE1BQU1wQyxJQUFJOzs7Ozs7b0RBR2JxSiw2QkFDQyw4REFBQzFCO3dEQUFLeEIsV0FBVTtrRUFBeUc7Ozs7OztvREFJMUhxQyxnQkFBZ0JBLGFBQWFvQixhQUFhLENBQUNoSyxNQUFNLEdBQUcsbUJBQ25ELDhEQUFDc0c7d0RBQUlDLFdBQVU7a0VBQ1pxQyxhQUFhb0IsYUFBYSxDQUFDekIsS0FBSyxDQUFDLEdBQUcsR0FBRy9HLEdBQUcsQ0FBQ3lJLENBQUFBLHNCQUMxQyw4REFBQ2xDO2dFQUVDeEIsV0FBVTtnRUFDVmUsT0FBTyxrQkFBd0IsT0FBTjJDOzBFQUV4QkEsVUFBVSxTQUFTLGlCQUFPQSxVQUFVLGdCQUFnQixpQkFBT0EsVUFBVSxhQUFhLGlCQUFPQSxVQUFVLFNBQVMsa0JBQVE7K0RBSmhIQTs7Ozs7Ozs7Ozs7Ozs7OzswREFVZiw4REFBQzVCO2dEQUFFOUIsV0FBVTswREFDWCw0RUFBQ3pLLDZFQUFlQTtvREFBQ2dPLE1BQU10SCxNQUFNOUIsRUFBRTtvREFBRXFKLFdBQVdoTDs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBRy9DNkYsUUFBUXNGLFdBQVcsQ0FBQzFILHdCQUNuQiw4REFBQ3VGO3dDQUFLeEIsV0FBVTtrREFBdUc7Ozs7Ozs7Ozs7Ozs0QkFPMUgvRCxNQUFNZ0csV0FBVyxrQkFDaEIsOERBQUNsQztnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQzhCO29DQUFFOUIsV0FBVTs4Q0FDVnFDLENBQUFBLHlCQUFBQSxtQ0FBQUEsYUFBY3VCLHNCQUFzQixrQkFDbkMsOERBQUNwQzt3Q0FBSzZCLHlCQUF5Qjs0Q0FBRUMsUUFBUWpCLGFBQWF1QixzQkFBc0I7d0NBQUM7Ozs7O29EQUMzRXBMLDJCQUNGLDhEQUFDakQsNkVBQWVBO3dDQUFDZ08sTUFBTXRILE1BQU1nRyxXQUFXO3dDQUFFdUIsV0FBV2hMOzs7OztvREFFckR5RCxNQUFNZ0csV0FBVzs7Ozs7Ozs7Ozs7MENBTXpCLDhEQUFDbEM7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUN3QjtnREFBS3hCLFdBQVU7MERBQWtEOzs7Ozs7MERBQ2xFLDhEQUFDd0I7Z0RBQUt4QixXQUFVOzBEQUErQjNCLFFBQVF3RixtQkFBbUIsQ0FBQzVILE1BQU02SCxjQUFjOzs7Ozs7Ozs7Ozs7a0RBRWpHLDhEQUFDL0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDd0I7Z0RBQUt4QixXQUFVOzBEQUFrRDs7Ozs7OzBEQUNsRSw4REFBQ3dCO2dEQUFLeEIsV0FBVTs7b0RBQStCM0IsUUFBUTBGLFdBQVcsQ0FBQzlILE1BQU0rSCxPQUFPLENBQUNDLE1BQU07b0RBQUU7Ozs7Ozs7Ozs7Ozs7a0RBRTNGLDhEQUFDbEU7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDd0I7Z0RBQUt4QixXQUFVOzBEQUFrRDs7Ozs7OzBEQUNsRSw4REFBQ3dCO2dEQUFLeEIsV0FBVTs7b0RBQStCM0IsUUFBUTBGLFdBQVcsQ0FBQzlILE1BQU0rSCxPQUFPLENBQUNFLFVBQVU7b0RBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS25HLDhEQUFDbkU7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDVztnQ0FDQ0MsU0FBUzhCO2dDQUNUNUIsVUFBVTZCO2dDQUNWM0MsV0FBVyxnSEFJVixPQUhDL0QsTUFBTWpCLFVBQVUsR0FDWix1RkFDQTtnQ0FFTitGLE9BQU80QixhQUFhLG1CQUFvQjFHLE1BQU1qQixVQUFVLEdBQUcsMEJBQTBCOzBDQUVwRjJILDJCQUNDLDhEQUFDNUM7b0NBQUlDLFdBQVU7Ozs7OzhEQUVmLDhEQUFDQztvQ0FBSUQsV0FBVTtvQ0FBVUUsTUFBTWpFLE1BQU1qQixVQUFVLEdBQUcsaUJBQWlCO29DQUFRbUYsUUFBTztvQ0FBZUMsU0FBUTs4Q0FDdkcsNEVBQUNDO3dDQUFLQyxlQUFjO3dDQUFRQyxnQkFBZTt3Q0FBUUMsYUFBYTt3Q0FBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLM0UsOERBQUNFO2dDQUNDQyxTQUFTNkI7Z0NBQ1R6QyxXQUFVOzBDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPWDtNQTNJTXVDO0FBb0pOLE1BQU1RLG9CQUFvQjtRQUFDLEVBQUU5RyxLQUFLLEVBQUV1RyxVQUFVLEVBQUVDLFFBQVEsRUFBMEI7SUFDaEYsTUFBTTBCLGVBQWUsQ0FBQ0M7UUFDcEIsSUFBSUEsWUFBWSxpQkFBaUI7WUFDL0IscUJBQ0UsOERBQUNuRTtnQkFBSUQsV0FBVTtnQkFBd0JFLE1BQUs7Z0JBQWVFLFNBQVE7MEJBQ2pFLDRFQUFDQztvQkFBS0ksR0FBRTs7Ozs7Ozs7Ozs7UUFHZCxPQUFPO1lBQ0wscUJBQ0UsOERBQUNSO2dCQUFJRCxXQUFVO2dCQUF3QkUsTUFBSztnQkFBZUUsU0FBUTswQkFDakUsNEVBQUNDO29CQUFLSSxHQUFFOzs7Ozs7Ozs7OztRQUdkO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ1Y7UUFBSUMsV0FBVyx3SEFJZixPQUhDd0MsYUFDSSxzR0FDQTtRQUVONUIsU0FBUzZCOzswQkFFUCw4REFBQzFDO2dCQUFJQyxXQUFVOzs7Ozs7MEJBR2YsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDWm1FLGFBQWFsSSxNQUFNOUIsRUFBRTs7Ozs7OzBDQUV4Qiw4REFBQzRGO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzhDO3dDQUFHOUMsV0FBVTtrREFBdUMvRCxNQUFNcEMsSUFBSTs7Ozs7O2tEQUMvRCw4REFBQ2lJO3dDQUFFOUIsV0FBVTtrREFBaUMvRCxNQUFNZ0csV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtuRSw4REFBQ2xDO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFBNEM7Ozs7OztrREFDM0QsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNaOUssMEVBQWVBLENBQUMyTyxtQkFBbUIsQ0FBQzVILE1BQU02SCxjQUFjOzs7Ozs7Ozs7Ozs7MENBRzdELDhEQUFDL0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFBNEM7Ozs7OztrREFDM0QsOERBQUNEO3dDQUFJQyxXQUFVOzs0Q0FDWjlLLDBFQUFlQSxDQUFDNk8sV0FBVyxDQUFDOUgsTUFBTStILE9BQU8sQ0FBQ0MsTUFBTTs0Q0FBRTs7Ozs7Ozs7Ozs7OzswQ0FHdkQsOERBQUNsRTtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUE0Qzs7Ozs7O2tEQUMzRCw4REFBQ0Q7d0NBQUlDLFdBQVU7OzRDQUNaOUssMEVBQWVBLENBQUM2TyxXQUFXLENBQUM5SCxNQUFNK0gsT0FBTyxDQUFDRSxVQUFVOzRDQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU03RCw4REFBQ3ZEO3dCQUNDQyxTQUFTLENBQUNPOzRCQUNSQSxFQUFFa0QsZUFBZTs0QkFDakI1Qjt3QkFDRjt3QkFDQXpDLFdBQVcsdUZBSVYsT0FIQ3dDLGFBQ0ksdUZBQ0E7a0NBR0xBLGFBQWEsZ0JBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLeEM7TUEvRU1PO0FBaUZOLCtEQUFlcE4sbUJBQW1CQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9Nb2RlbFNlbGVjdGlvbk1vZGFsLnRzeD9hYWQ2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICdAL2NvbnRleHRzL0F1dGhDb250ZXh0JztcbmltcG9ydCB7IGRiIH0gZnJvbSAnQC9saWIvZmlyZWJhc2UnO1xuaW1wb3J0IHsgZ2V0VXNlckFQSUVuZHBvaW50cywgdHlwZSBBUElFbmRwb2ludCB9IGZyb20gJ0AvbGliL3NlcnZpY2VzL3NldHRpbmdzU2VydmljZSc7XG5pbXBvcnQgeyBvcGVuUm91dGVyU2VydmljZSB9IGZyb20gJ0AvbGliL3NlcnZpY2VzL29wZW5Sb3V0ZXJTZXJ2aWNlJztcbmltcG9ydCB7IGRlZXBTZWVrU2VydmljZSB9IGZyb20gJ0AvbGliL3NlcnZpY2VzL2RlZXBTZWVrU2VydmljZSc7XG5pbXBvcnQgeyBtb2RlbEZhdm9yaXRlc1NlcnZpY2UgfSBmcm9tICdAL2xpYi9zZXJ2aWNlcy9tb2RlbEZhdm9yaXRlc1NlcnZpY2UnO1xuaW1wb3J0IHsgQUlNb2RlbCwgTW9kZWxDYXRlZ29yeSwgTW9kZWxTb3J0QnksIE1vZGVsRmlsdGVycyB9IGZyb20gJ0AvbGliL3R5cGVzL2NoYXQnO1xuaW1wb3J0IHsgdXNlQWR2YW5jZWRTZWFyY2ggfSBmcm9tICdAL2hvb2tzL3VzZUFkdmFuY2VkU2VhcmNoJztcbmltcG9ydCB7IGFkdmFuY2VkRmlsdGVyc1NlcnZpY2UsIFNtYXJ0Q2F0ZWdvcnkgfSBmcm9tICdAL2xpYi9zZXJ2aWNlcy9hZHZhbmNlZEZpbHRlcnNTZXJ2aWNlJztcbmltcG9ydCBBZHZhbmNlZFNlYXJjaElucHV0LCB7IEhpZ2hsaWdodGVkVGV4dCB9IGZyb20gJ0AvY29tcG9uZW50cy9BZHZhbmNlZFNlYXJjaElucHV0JztcbmltcG9ydCBFeHBlbnNpdmVNb2RlbENvbmZpcm1hdGlvbk1vZGFsIGZyb20gJy4vRXhwZW5zaXZlTW9kZWxDb25maXJtYXRpb25Nb2RhbCc7XG5cbmludGVyZmFjZSBNb2RlbFNlbGVjdGlvbk1vZGFsUHJvcHMge1xuICBpc09wZW46IGJvb2xlYW47XG4gIG9uQ2xvc2U6ICgpID0+IHZvaWQ7XG4gIGN1cnJlbnRNb2RlbDogc3RyaW5nO1xuICBvbk1vZGVsU2VsZWN0OiAobW9kZWxJZDogc3RyaW5nKSA9PiB2b2lkO1xufVxuXG4vLyBDb25zdGFudGVzIHBhcmEgY2FjaGVcbmNvbnN0IENBQ0hFX0RVUkFUSU9OID0gNSAqIDYwICogMTAwMDsgLy8gNSBtaW51dG9zXG5jb25zdCBFTkRQT0lOVFNfQ0FDSEVfRFVSQVRJT04gPSAxMCAqIDYwICogMTAwMDsgLy8gMTAgbWludXRvc1xuXG5jb25zdCBNb2RlbFNlbGVjdGlvbk1vZGFsID0gKHsgaXNPcGVuLCBvbkNsb3NlLCBjdXJyZW50TW9kZWwsIG9uTW9kZWxTZWxlY3QgfTogTW9kZWxTZWxlY3Rpb25Nb2RhbFByb3BzKSA9PiB7XG4gIGNvbnN0IHsgdXNlciB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCBbZW5kcG9pbnRzLCBzZXRFbmRwb2ludHNdID0gdXNlU3RhdGU8QVBJRW5kcG9pbnRbXT4oW10pO1xuICBjb25zdCBbc2VsZWN0ZWRFbmRwb2ludCwgc2V0U2VsZWN0ZWRFbmRwb2ludF0gPSB1c2VTdGF0ZTxBUElFbmRwb2ludCB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbW9kZWxzLCBzZXRNb2RlbHNdID0gdXNlU3RhdGU8QUlNb2RlbFtdPihbXSk7XG4gIGNvbnN0IFtmYXZvcml0ZU1vZGVsSWRzLCBzZXRGYXZvcml0ZU1vZGVsSWRzXSA9IHVzZVN0YXRlPFNldDxzdHJpbmc+PihuZXcgU2V0KCkpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtkaXNwbGF5ZWRNb2RlbHNDb3VudCwgc2V0RGlzcGxheWVkTW9kZWxzQ291bnRdID0gdXNlU3RhdGUoNCk7XG4gIGNvbnN0IE1PREVMU19QRVJfUEFHRSA9IDQ7XG4gIGNvbnN0IFtjdXN0b21Nb2RlbElkLCBzZXRDdXN0b21Nb2RlbElkXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3Nob3dFeHBlbnNpdmVNb2RlbE1vZGFsLCBzZXRTaG93RXhwZW5zaXZlTW9kZWxNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtwZW5kaW5nRXhwZW5zaXZlTW9kZWwsIHNldFBlbmRpbmdFeHBlbnNpdmVNb2RlbF0gPSB1c2VTdGF0ZTxBSU1vZGVsIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzZWxlY3RlZENhdGVnb3J5LCBzZXRTZWxlY3RlZENhdGVnb3J5XSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc21hcnRDYXRlZ29yaWVzLCBzZXRTbWFydENhdGVnb3JpZXNdID0gdXNlU3RhdGU8U21hcnRDYXRlZ29yeVtdPihbXSk7XG4gIGNvbnN0IFtmYXZvcml0ZVRvZ2dsaW5nLCBzZXRGYXZvcml0ZVRvZ2dsaW5nXSA9IHVzZVN0YXRlPFNldDxzdHJpbmc+PihuZXcgU2V0KCkpO1xuICBjb25zdCBbbGFzdExvYWRlZEVuZHBvaW50LCBzZXRMYXN0TG9hZGVkRW5kcG9pbnRdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFttb2RlbHNDYWNoZSwgc2V0TW9kZWxzQ2FjaGVdID0gdXNlU3RhdGU8TWFwPHN0cmluZywgeyBtb2RlbHM6IEFJTW9kZWxbXSwgdGltZXN0YW1wOiBudW1iZXIgfT4+KG5ldyBNYXAoKSk7XG4gIGNvbnN0IFtlbmRwb2ludHNDYWNoZSwgc2V0RW5kcG9pbnRzQ2FjaGVdID0gdXNlU3RhdGU8eyBlbmRwb2ludHM6IEFQSUVuZHBvaW50W10sIHRpbWVzdGFtcDogbnVtYmVyIH0gfCBudWxsPihudWxsKTtcblxuICBjb25zdCBbZmlsdGVycywgc2V0RmlsdGVyc10gPSB1c2VTdGF0ZTxNb2RlbEZpbHRlcnM+KHtcbiAgICBjYXRlZ29yeTogJ3BhaWQnLFxuICAgIHNvcnRCeTogJ25ld2VzdCcsXG4gICAgc2VhcmNoVGVybTogJydcbiAgfSk7XG5cbiAgLy8gSG9vayBkZSBidXNjYSBhdmFuw6dhZGFcbiAgY29uc3Qge1xuICAgIHNlYXJjaFRlcm0sXG4gICAgc2V0U2VhcmNoVGVybSxcbiAgICBzZWFyY2hSZXN1bHRzLFxuICAgIHN1Z2dlc3Rpb25zLFxuICAgIGlzU2VhcmNoaW5nLFxuICAgIGhhc1NlYXJjaGVkLFxuICAgIGNsZWFyU2VhcmNoLFxuICAgIHRyYWNrTW9kZWxTZWxlY3Rpb25cbiAgfSA9IHVzZUFkdmFuY2VkU2VhcmNoKG1vZGVscywge1xuICAgIGRlYm91bmNlTXM6IDMwMCxcbiAgICBlbmFibGVTdWdnZXN0aW9uczogZmFsc2UsXG4gICAgY2FjaGVSZXN1bHRzOiB0cnVlLFxuICAgIGZ1enp5VGhyZXNob2xkOiAwLjYsXG4gICAgbWF4UmVzdWx0czogNTAsXG4gICAgYm9vc3RGYXZvcml0ZXM6IHRydWVcbiAgfSk7XG5cbiAgLy8gTG9hZCB1c2VyIGVuZHBvaW50cyBhcGVuYXMgc2UgbmVjZXNzw6FyaW9cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodXNlciAmJiBpc09wZW4pIHtcbiAgICAgIC8vIFZlcmlmaWNhciBzZSB0ZW1vcyBjYWNoZSB2w6FsaWRvXG4gICAgICBpZiAoZW5kcG9pbnRzQ2FjaGUgJiYgRGF0ZS5ub3coKSAtIGVuZHBvaW50c0NhY2hlLnRpbWVzdGFtcCA8IEVORFBPSU5UU19DQUNIRV9EVVJBVElPTikge1xuICAgICAgICBzZXRFbmRwb2ludHMoZW5kcG9pbnRzQ2FjaGUuZW5kcG9pbnRzKTtcblxuICAgICAgICAvLyBTZWxlY2lvbmFyIGVuZHBvaW50IHNlIGFpbmRhIG7Do28gdGl2ZXIgdW0gc2VsZWNpb25hZG9cbiAgICAgICAgaWYgKCFzZWxlY3RlZEVuZHBvaW50ICYmIGVuZHBvaW50c0NhY2hlLmVuZHBvaW50cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgY29uc3Qgb3BlblJvdXRlckVuZHBvaW50ID0gZW5kcG9pbnRzQ2FjaGUuZW5kcG9pbnRzLmZpbmQoZXAgPT4gZXAubmFtZSA9PT0gJ09wZW5Sb3V0ZXInKTtcbiAgICAgICAgICBjb25zdCBkZWVwU2Vla0VuZHBvaW50ID0gZW5kcG9pbnRzQ2FjaGUuZW5kcG9pbnRzLmZpbmQoZXAgPT4gZXAubmFtZSA9PT0gJ0RlZXBTZWVrJyk7XG5cbiAgICAgICAgICBpZiAob3BlblJvdXRlckVuZHBvaW50KSB7XG4gICAgICAgICAgICBzZXRTZWxlY3RlZEVuZHBvaW50KG9wZW5Sb3V0ZXJFbmRwb2ludCk7XG4gICAgICAgICAgfSBlbHNlIGlmIChkZWVwU2Vla0VuZHBvaW50KSB7XG4gICAgICAgICAgICBzZXRTZWxlY3RlZEVuZHBvaW50KGRlZXBTZWVrRW5kcG9pbnQpO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBzZXRTZWxlY3RlZEVuZHBvaW50KGVuZHBvaW50c0NhY2hlLmVuZHBvaW50c1swXSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBsb2FkRW5kcG9pbnRzKCk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbdXNlciwgaXNPcGVuXSk7XG5cbiAgLy8gRXN0YWRvIHBhcmEgY29udHJvbGFyIHNlIGrDoSBjYXJyZWdhbW9zIGZhdm9yaXRvcyBwYXJhIGVzdGUgZW5kcG9pbnRcbiAgY29uc3QgW2Zhdm9yaXRlc0xvYWRlZEZvckVuZHBvaW50LCBzZXRGYXZvcml0ZXNMb2FkZWRGb3JFbmRwb2ludF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcblxuICAvLyBBdHVhbGl6YXIgZmF2b3JpdG9zIGFwZW5hcyBxdWFuZG8gbmVjZXNzw6FyaW9cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaXNPcGVuICYmIHNlbGVjdGVkRW5kcG9pbnQgJiYgc2VsZWN0ZWRFbmRwb2ludC5uYW1lID09PSAnT3BlblJvdXRlcicgJiYgdXNlciAmJiBtb2RlbHMubGVuZ3RoID4gMCkge1xuICAgICAgY29uc3QgZW5kcG9pbnRLZXkgPSBgJHtzZWxlY3RlZEVuZHBvaW50LmlkfV8ke3NlbGVjdGVkRW5kcG9pbnQubmFtZX1gO1xuXG4gICAgICAvLyBTw7MgY2FycmVnYXIgc2UgbsOjbyBjYXJyZWdhbW9zIGFpbmRhIHBhcmEgZXN0ZSBlbmRwb2ludFxuICAgICAgaWYgKGZhdm9yaXRlc0xvYWRlZEZvckVuZHBvaW50ICE9PSBlbmRwb2ludEtleSkge1xuICAgICAgICBjb25zb2xlLmxvZygnTG9hZGluZyBmYXZvcml0ZXMgZm9yIGVuZHBvaW50OicsIGVuZHBvaW50S2V5KTtcbiAgICAgICAgc2V0RmF2b3JpdGVzTG9hZGVkRm9yRW5kcG9pbnQoZW5kcG9pbnRLZXkpO1xuXG4gICAgICAgIGNvbnN0IHRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgdXBkYXRlRmF2b3JpdGVzRnJvbUZpcmVzdG9yZSgpO1xuICAgICAgICB9LCAxMDApO1xuXG4gICAgICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZXIpO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW2lzT3Blbiwgc2VsZWN0ZWRFbmRwb2ludCwgdXNlciwgbW9kZWxzLmxlbmd0aCwgZmF2b3JpdGVzTG9hZGVkRm9yRW5kcG9pbnRdKTtcblxuICAvLyBSZXNldCBkbyBlc3RhZG8gcXVhbmRvIG8gZW5kcG9pbnQgbXVkYVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzZWxlY3RlZEVuZHBvaW50KSB7XG4gICAgICBjb25zdCBlbmRwb2ludEtleSA9IGAke3NlbGVjdGVkRW5kcG9pbnQuaWR9XyR7c2VsZWN0ZWRFbmRwb2ludC5uYW1lfWA7XG4gICAgICBpZiAoZmF2b3JpdGVzTG9hZGVkRm9yRW5kcG9pbnQgJiYgZmF2b3JpdGVzTG9hZGVkRm9yRW5kcG9pbnQgIT09IGVuZHBvaW50S2V5KSB7XG4gICAgICAgIHNldEZhdm9yaXRlc0xvYWRlZEZvckVuZHBvaW50KG51bGwpO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW3NlbGVjdGVkRW5kcG9pbnRdKTtcblxuICAvLyBMb2FkIG1vZGVscyB3aGVuIGVuZHBvaW50IGNoYW5nZXMgb3UgbsOjbyBow6EgY2FjaGVcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoc2VsZWN0ZWRFbmRwb2ludCkge1xuICAgICAgY29uc3QgY2FjaGVLZXkgPSBgJHtzZWxlY3RlZEVuZHBvaW50LmlkfV8ke3NlbGVjdGVkRW5kcG9pbnQubmFtZX1gO1xuICAgICAgY29uc3QgY2FjaGVkRGF0YSA9IG1vZGVsc0NhY2hlLmdldChjYWNoZUtleSk7XG5cbiAgICAgIC8vIFZlcmlmaWNhciBzZSB0ZW1vcyBjYWNoZSB2w6FsaWRvIHBhcmEgZXN0ZSBlbmRwb2ludFxuICAgICAgaWYgKGNhY2hlZERhdGEgJiYgRGF0ZS5ub3coKSAtIGNhY2hlZERhdGEudGltZXN0YW1wIDwgQ0FDSEVfRFVSQVRJT04pIHtcbiAgICAgICAgc2V0TW9kZWxzKGNhY2hlZERhdGEubW9kZWxzKTtcbiAgICAgICAgc2V0TGFzdExvYWRlZEVuZHBvaW50KHNlbGVjdGVkRW5kcG9pbnQuaWQpO1xuXG4gICAgICAgIC8vIEV4dHJhaXIgZmF2b3JpdG9zIGRvIGNhY2hlXG4gICAgICAgIGNvbnN0IGNhY2hlZEZhdm9yaXRlcyA9IG5ldyBTZXQoXG4gICAgICAgICAgY2FjaGVkRGF0YS5tb2RlbHMuZmlsdGVyKG0gPT4gbS5pc0Zhdm9yaXRlKS5tYXAobSA9PiBtLmlkKVxuICAgICAgICApO1xuICAgICAgICBzZXRGYXZvcml0ZU1vZGVsSWRzKGNhY2hlZEZhdm9yaXRlcyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBTw7MgY2FycmVnYXIgc2UgbXVkb3UgZGUgZW5kcG9pbnQgb3UgbsOjbyBow6EgY2FjaGUgdsOhbGlkb1xuICAgICAgICBpZiAobGFzdExvYWRlZEVuZHBvaW50ICE9PSBzZWxlY3RlZEVuZHBvaW50LmlkIHx8ICFjYWNoZWREYXRhKSB7XG4gICAgICAgICAgaWYgKHNlbGVjdGVkRW5kcG9pbnQubmFtZSA9PT0gJ09wZW5Sb3V0ZXInKSB7XG4gICAgICAgICAgICBsb2FkT3BlblJvdXRlck1vZGVscygpO1xuICAgICAgICAgIH0gZWxzZSBpZiAoc2VsZWN0ZWRFbmRwb2ludC5uYW1lID09PSAnRGVlcFNlZWsnKSB7XG4gICAgICAgICAgICBsb2FkRGVlcFNlZWtNb2RlbHMoKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH0sIFtzZWxlY3RlZEVuZHBvaW50LCBsYXN0TG9hZGVkRW5kcG9pbnQsIG1vZGVsc0NhY2hlXSk7XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBhdHVhbGl6YXIgYXBlbmFzIG9zIGZhdm9yaXRvcyBzZW0gcmVjYXJyZWdhciB0b2RvcyBvcyBtb2RlbG9zXG4gIGNvbnN0IHVwZGF0ZUZhdm9yaXRlc0Zyb21GaXJlc3RvcmUgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZEVuZHBvaW50IHx8ICF1c2VyIHx8IHNlbGVjdGVkRW5kcG9pbnQubmFtZSAhPT0gJ09wZW5Sb3V0ZXInKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcbiAgICAgIGNvbnN0IGZhdm9yaXRlSWRzID0gYXdhaXQgbW9kZWxGYXZvcml0ZXNTZXJ2aWNlLmdldEZhdm9yaXRlTW9kZWxJZHModXNlcm5hbWUsIHNlbGVjdGVkRW5kcG9pbnQuaWQpO1xuXG4gICAgICAvLyBTw7MgZmF6ZXIgbG9nIHNlIGhvdXZlciBtdWRhbsOnYSBub3MgZmF2b3JpdG9zXG4gICAgICBjb25zdCBjdXJyZW50RmF2b3JpdGVJZHMgPSBBcnJheS5mcm9tKGZhdm9yaXRlTW9kZWxJZHMpO1xuICAgICAgY29uc3QgbmV3RmF2b3JpdGVJZHMgPSBBcnJheS5mcm9tKGZhdm9yaXRlSWRzKTtcbiAgICAgIGNvbnN0IGhhc0NoYW5nZWQgPSBjdXJyZW50RmF2b3JpdGVJZHMubGVuZ3RoICE9PSBuZXdGYXZvcml0ZUlkcy5sZW5ndGggfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICFjdXJyZW50RmF2b3JpdGVJZHMuZXZlcnkoaWQgPT4gZmF2b3JpdGVJZHMuaGFzKGlkKSk7XG5cbiAgICAgIGlmIChoYXNDaGFuZ2VkKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdVcGRhdGluZyBmYXZvcml0ZXMgZnJvbSBGaXJlc3RvcmU6JywgbmV3RmF2b3JpdGVJZHMpO1xuICAgICAgfVxuXG4gICAgICAvLyBBdHVhbGl6YXIgbyBlc3RhZG8gZG9zIGZhdm9yaXRvc1xuICAgICAgc2V0RmF2b3JpdGVNb2RlbElkcyhmYXZvcml0ZUlkcyk7XG5cbiAgICAgIC8vIEF0dWFsaXphciBvcyBtb2RlbG9zIGNvbSBvIG5vdm8gc3RhdHVzIGRlIGZhdm9yaXRvXG4gICAgICBzZXRNb2RlbHMocHJldk1vZGVscyA9PiB7XG4gICAgICAgIGNvbnN0IHVwZGF0ZWRNb2RlbHMgPSBwcmV2TW9kZWxzLm1hcChtb2RlbCA9PiAoe1xuICAgICAgICAgIC4uLm1vZGVsLFxuICAgICAgICAgIGlzRmF2b3JpdGU6IGZhdm9yaXRlSWRzLmhhcyhtb2RlbC5pZClcbiAgICAgICAgfSkpO1xuXG4gICAgICAgIC8vIExvZyBhcGVuYXMgc2UgaG91dmVyIG11ZGFuw6dhIHJlYWxcbiAgICAgICAgaWYgKGhhc0NoYW5nZWQpIHtcbiAgICAgICAgICBjb25zdCBmYXZvcml0ZWRNb2RlbHMgPSB1cGRhdGVkTW9kZWxzLmZpbHRlcihtID0+IG0uaXNGYXZvcml0ZSk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ01vZGVscyB1cGRhdGVkIHdpdGggZmF2b3JpdGVzOicsIGZhdm9yaXRlZE1vZGVscy5tYXAobSA9PiBtLmlkKSk7XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gdXBkYXRlZE1vZGVscztcbiAgICAgIH0pO1xuXG4gICAgICAvLyBBdHVhbGl6YXIgbyBjYWNoZVxuICAgICAgY29uc3QgY2FjaGVLZXkgPSBgJHtzZWxlY3RlZEVuZHBvaW50LmlkfV8ke3NlbGVjdGVkRW5kcG9pbnQubmFtZX1gO1xuICAgICAgc2V0TW9kZWxzQ2FjaGUocHJldiA9PiB7XG4gICAgICAgIGNvbnN0IGN1cnJlbnRDYWNoZSA9IHByZXYuZ2V0KGNhY2hlS2V5KTtcbiAgICAgICAgaWYgKGN1cnJlbnRDYWNoZSkge1xuICAgICAgICAgIGNvbnN0IHVwZGF0ZWRNb2RlbHMgPSBjdXJyZW50Q2FjaGUubW9kZWxzLm1hcChtb2RlbCA9PiAoe1xuICAgICAgICAgICAgLi4ubW9kZWwsXG4gICAgICAgICAgICBpc0Zhdm9yaXRlOiBmYXZvcml0ZUlkcy5oYXMobW9kZWwuaWQpXG4gICAgICAgICAgfSkpO1xuICAgICAgICAgIHJldHVybiBuZXcgTWFwKHByZXYpLnNldChjYWNoZUtleSwge1xuICAgICAgICAgICAgbW9kZWxzOiB1cGRhdGVkTW9kZWxzLFxuICAgICAgICAgICAgdGltZXN0YW1wOiBjdXJyZW50Q2FjaGUudGltZXN0YW1wXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHByZXY7XG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgZmF2b3JpdGVzIGZyb20gRmlyZXN0b3JlOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gTG9hZCBzbWFydCBjYXRlZ29yaWVzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0U21hcnRDYXRlZ29yaWVzKGFkdmFuY2VkRmlsdGVyc1NlcnZpY2UuZ2V0U21hcnRDYXRlZ29yaWVzKCkpO1xuICB9LCBbXSk7XG5cbiAgLy8gRnVuw6fDo28gdXRpbGl0w6FyaWEgcGFyYSBidXNjYXIgdXNlcm5hbWUgY29ycmV0b1xuICBjb25zdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUgPSBhc3luYyAoKTogUHJvbWlzZTxzdHJpbmc+ID0+IHtcbiAgICBpZiAoIXVzZXI/LmVtYWlsKSByZXR1cm4gJ3Vua25vd24nO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgY29sbGVjdGlvbiwgcXVlcnksIHdoZXJlLCBnZXREb2NzIH0gPSBhd2FpdCBpbXBvcnQoJ2ZpcmViYXNlL2ZpcmVzdG9yZScpO1xuICAgICAgY29uc3QgdXN1YXJpb3NSZWYgPSBjb2xsZWN0aW9uKGRiLCAndXN1YXJpb3MnKTtcbiAgICAgIGNvbnN0IHEgPSBxdWVyeSh1c3Vhcmlvc1JlZiwgd2hlcmUoJ2VtYWlsJywgJz09JywgdXNlci5lbWFpbCkpO1xuICAgICAgY29uc3QgcXVlcnlTbmFwc2hvdCA9IGF3YWl0IGdldERvY3MocSk7XG5cbiAgICAgIGlmICghcXVlcnlTbmFwc2hvdC5lbXB0eSkge1xuICAgICAgICBjb25zdCB1c2VyRG9jID0gcXVlcnlTbmFwc2hvdC5kb2NzWzBdO1xuICAgICAgICByZXR1cm4gdXNlckRvYy5kYXRhKCkudXNlcm5hbWUgfHwgdXNlckRvYy5pZDtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuICd1bmtub3duJztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBidXNjYXIgdXNlcm5hbWU6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuICd1bmtub3duJztcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgbG9hZEVuZHBvaW50cyA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXVzZXIpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdObyB1c2VyIGZvdW5kJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcihudWxsKTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB1c2VybmFtZSA9IGF3YWl0IGdldFVzZXJuYW1lRnJvbUZpcmVzdG9yZSgpO1xuICAgICAgY29uc3QgdXNlckVuZHBvaW50cyA9IGF3YWl0IGdldFVzZXJBUElFbmRwb2ludHModXNlcm5hbWUpO1xuXG4gICAgICAvLyBTYWx2YXIgbm8gY2FjaGVcbiAgICAgIHNldEVuZHBvaW50c0NhY2hlKHtcbiAgICAgICAgZW5kcG9pbnRzOiB1c2VyRW5kcG9pbnRzLFxuICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KClcbiAgICAgIH0pO1xuXG4gICAgICBzZXRFbmRwb2ludHModXNlckVuZHBvaW50cyk7XG5cbiAgICAgIC8vIFNlbGVjdCBmaXJzdCBhdmFpbGFibGUgZW5kcG9pbnQgYnkgZGVmYXVsdCAoT3BlblJvdXRlciBvciBEZWVwU2VlaylcbiAgICAgIGNvbnN0IG9wZW5Sb3V0ZXJFbmRwb2ludCA9IHVzZXJFbmRwb2ludHMuZmluZChlcCA9PiBlcC5uYW1lID09PSAnT3BlblJvdXRlcicpO1xuICAgICAgY29uc3QgZGVlcFNlZWtFbmRwb2ludCA9IHVzZXJFbmRwb2ludHMuZmluZChlcCA9PiBlcC5uYW1lID09PSAnRGVlcFNlZWsnKTtcblxuICAgICAgaWYgKG9wZW5Sb3V0ZXJFbmRwb2ludCkge1xuICAgICAgICBzZXRTZWxlY3RlZEVuZHBvaW50KG9wZW5Sb3V0ZXJFbmRwb2ludCk7XG4gICAgICB9IGVsc2UgaWYgKGRlZXBTZWVrRW5kcG9pbnQpIHtcbiAgICAgICAgc2V0U2VsZWN0ZWRFbmRwb2ludChkZWVwU2Vla0VuZHBvaW50KTtcbiAgICAgIH0gZWxzZSBpZiAodXNlckVuZHBvaW50cy5sZW5ndGggPiAwKSB7XG4gICAgICAgIHNldFNlbGVjdGVkRW5kcG9pbnQodXNlckVuZHBvaW50c1swXSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgZW5kcG9pbnRzOicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKCdFcnJvIGFvIGNhcnJlZ2FyIGVuZHBvaW50czogJyArIChlcnJvciBhcyBFcnJvcikubWVzc2FnZSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBsb2FkT3BlblJvdXRlck1vZGVscyA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXNlbGVjdGVkRW5kcG9pbnQgfHwgIXVzZXIpIHJldHVybjtcblxuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IobnVsbCk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gTG9hZCBtb2RlbHMgZnJvbSBPcGVuUm91dGVyXG4gICAgICBjb25zdCBvcGVuUm91dGVyTW9kZWxzID0gYXdhaXQgb3BlblJvdXRlclNlcnZpY2UuZmV0Y2hNb2RlbHMoKTtcblxuICAgICAgLy8gTG9hZCBmYXZvcml0ZSBtb2RlbCBJRHMgLSBzZW1wcmUgYnVzY2FyIGRvIEZpcmVzdG9yZSBwYXJhIGdhcmFudGlyIGRhZG9zIGF0dWFsaXphZG9zXG4gICAgICBjb25zdCB1c2VybmFtZSA9IGF3YWl0IGdldFVzZXJuYW1lRnJvbUZpcmVzdG9yZSgpO1xuICAgICAgY29uc3QgZmF2b3JpdGVJZHMgPSBhd2FpdCBtb2RlbEZhdm9yaXRlc1NlcnZpY2UuZ2V0RmF2b3JpdGVNb2RlbElkcyh1c2VybmFtZSwgc2VsZWN0ZWRFbmRwb2ludC5pZCk7XG5cbiAgICAgIGlmIChmYXZvcml0ZUlkcy5zaXplID4gMCkge1xuICAgICAgICBjb25zb2xlLmxvZygnTG9hZGVkIGZhdm9yaXRlIElEczonLCBBcnJheS5mcm9tKGZhdm9yaXRlSWRzKSk7XG4gICAgICB9XG5cbiAgICAgIC8vIE1hcmsgZmF2b3JpdGUgbW9kZWxzXG4gICAgICBjb25zdCBtb2RlbHNXaXRoRmF2b3JpdGVzID0gb3BlblJvdXRlck1vZGVscy5tYXAobW9kZWwgPT4gKHtcbiAgICAgICAgLi4ubW9kZWwsXG4gICAgICAgIGlzRmF2b3JpdGU6IGZhdm9yaXRlSWRzLmhhcyhtb2RlbC5pZClcbiAgICAgIH0pKTtcblxuICAgICAgLy8gU2FsdmFyIG5vIGNhY2hlXG4gICAgICBjb25zdCBjYWNoZUtleSA9IGAke3NlbGVjdGVkRW5kcG9pbnQuaWR9XyR7c2VsZWN0ZWRFbmRwb2ludC5uYW1lfWA7XG4gICAgICBzZXRNb2RlbHNDYWNoZShwcmV2ID0+IG5ldyBNYXAocHJldikuc2V0KGNhY2hlS2V5LCB7XG4gICAgICAgIG1vZGVsczogbW9kZWxzV2l0aEZhdm9yaXRlcyxcbiAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXG4gICAgICB9KSk7XG5cbiAgICAgIHNldE1vZGVscyhtb2RlbHNXaXRoRmF2b3JpdGVzKTtcbiAgICAgIHNldEZhdm9yaXRlTW9kZWxJZHMoZmF2b3JpdGVJZHMpO1xuICAgICAgc2V0TGFzdExvYWRlZEVuZHBvaW50KHNlbGVjdGVkRW5kcG9pbnQuaWQpO1xuXG4gICAgICBjb25zdCBmYXZvcml0ZWRDb3VudCA9IG1vZGVsc1dpdGhGYXZvcml0ZXMuZmlsdGVyKG0gPT4gbS5pc0Zhdm9yaXRlKS5sZW5ndGg7XG4gICAgICBpZiAoZmF2b3JpdGVkQ291bnQgPiAwKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdNb2RlbHMgbG9hZGVkIHdpdGggZmF2b3JpdGVzOicsIGZhdm9yaXRlZENvdW50LCAnZmF2b3JpdGVzIGZvdW5kJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgbW9kZWxzOicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKCdFcnJvIGFvIGNhcnJlZ2FyIG1vZGVsb3MnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGxvYWREZWVwU2Vla01vZGVscyA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXNlbGVjdGVkRW5kcG9pbnQgfHwgIXVzZXIpIHJldHVybjtcblxuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IobnVsbCk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gTG9hZCBtb2RlbHMgZnJvbSBEZWVwU2Vla1xuICAgICAgY29uc3QgZGVlcFNlZWtNb2RlbHMgPSBhd2FpdCBkZWVwU2Vla1NlcnZpY2UuZmV0Y2hNb2RlbHMoKTtcblxuICAgICAgLy8gU2FsdmFyIG5vIGNhY2hlXG4gICAgICBjb25zdCBjYWNoZUtleSA9IGAke3NlbGVjdGVkRW5kcG9pbnQuaWR9XyR7c2VsZWN0ZWRFbmRwb2ludC5uYW1lfWA7XG4gICAgICBzZXRNb2RlbHNDYWNoZShwcmV2ID0+IG5ldyBNYXAocHJldikuc2V0KGNhY2hlS2V5LCB7XG4gICAgICAgIG1vZGVsczogZGVlcFNlZWtNb2RlbHMsXG4gICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKVxuICAgICAgfSkpO1xuXG4gICAgICBzZXRNb2RlbHMoZGVlcFNlZWtNb2RlbHMpO1xuICAgICAgc2V0TGFzdExvYWRlZEVuZHBvaW50KHNlbGVjdGVkRW5kcG9pbnQuaWQpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIERlZXBTZWVrIG1vZGVsczonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcignRXJybyBhbyBjYXJyZWdhciBtb2RlbG9zIERlZXBTZWVrJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIG9idGVyIG1vZGVsb3MgZmlsdHJhZG9zXG4gIGNvbnN0IGdldEZpbHRlcmVkTW9kZWxzID0gKCkgPT4ge1xuICAgIGxldCBmaWx0ZXJlZCA9IFsuLi5tb2RlbHNdO1xuXG4gICAgLy8gUHJpbWVpcm8sIGFwbGljYXIgZmlsdHJvcyBkZSBjYXRlZ29yaWEgYmFzZSAocGFpZC9mcmVlL2Zhdm9yaXRlcylcbiAgICBpZiAoc2VsZWN0ZWRFbmRwb2ludD8ubmFtZSA9PT0gJ0RlZXBTZWVrJykge1xuICAgICAgaWYgKGZpbHRlcnMuY2F0ZWdvcnkgPT09ICdmYXZvcml0ZXMnKSB7XG4gICAgICAgIGZpbHRlcmVkID0gW107XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBmaWx0ZXJlZCA9IGRlZXBTZWVrU2VydmljZS5maWx0ZXJCeUNhdGVnb3J5KGZpbHRlcmVkLCBmaWx0ZXJzLmNhdGVnb3J5IGFzICdwYWlkJyB8ICdmcmVlJyk7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIGZpbHRlcmVkID0gb3BlblJvdXRlclNlcnZpY2UuZmlsdGVyQnlDYXRlZ29yeShmaWx0ZXJlZCwgZmlsdGVycy5jYXRlZ29yeSk7XG4gICAgfVxuXG4gICAgLy8gU2UgaMOhIGJ1c2NhIGF0aXZhLCB1c2FyIHJlc3VsdGFkb3MgZGEgYnVzY2EgYXZhbsOnYWRhIChtYXMgYWluZGEgcmVzcGVpdGFuZG8gYSBjYXRlZ29yaWEgYmFzZSlcbiAgICBpZiAoaGFzU2VhcmNoZWQgJiYgc2VhcmNoVGVybS50cmltKCkpIHtcbiAgICAgIGNvbnN0IHNlYXJjaFJlc3VsdE1vZGVscyA9IHNlYXJjaFJlc3VsdHMubWFwKHJlc3VsdCA9PiByZXN1bHQubW9kZWwpO1xuICAgICAgLy8gRmlsdHJhciBvcyByZXN1bHRhZG9zIGRlIGJ1c2NhIHBhcmEgbWFudGVyIGFwZW5hcyBvcyBxdWUgcGFzc2FtIHBlbG8gZmlsdHJvIGRlIGNhdGVnb3JpYSBiYXNlXG4gICAgICBmaWx0ZXJlZCA9IHNlYXJjaFJlc3VsdE1vZGVscy5maWx0ZXIobW9kZWwgPT4gZmlsdGVyZWQuc29tZShmID0+IGYuaWQgPT09IG1vZGVsLmlkKSk7XG4gICAgfSBlbHNlIGlmIChzZWxlY3RlZENhdGVnb3J5KSB7XG4gICAgICAvLyBTZSBow6EgY2F0ZWdvcmlhIGludGVsaWdlbnRlIHNlbGVjaW9uYWRhLCBhcGxpY2FyIGZpbHRybyBhZGljaW9uYWxcbiAgICAgIGNvbnN0IGNhdGVnb3J5RmlsdGVyZWQgPSBhZHZhbmNlZEZpbHRlcnNTZXJ2aWNlLmdldE1vZGVsc0J5Q2F0ZWdvcnkoZmlsdGVyZWQsIHNlbGVjdGVkQ2F0ZWdvcnkpO1xuICAgICAgZmlsdGVyZWQgPSBjYXRlZ29yeUZpbHRlcmVkO1xuICAgIH1cblxuICAgIC8vIEFwbGljYXIgb3JkZW5hw6fDo28gc2UgbsOjbyBow6EgYnVzY2EgYXRpdmFcbiAgICBpZiAoIWhhc1NlYXJjaGVkIHx8ICFzZWFyY2hUZXJtLnRyaW0oKSkge1xuICAgICAgY29uc3Qgc2VydmljZSA9IHNlbGVjdGVkRW5kcG9pbnQ/Lm5hbWUgPT09ICdEZWVwU2VlaycgPyBkZWVwU2Vla1NlcnZpY2UgOiBvcGVuUm91dGVyU2VydmljZTtcbiAgICAgIGZpbHRlcmVkID0gc2VydmljZS5zb3J0TW9kZWxzKGZpbHRlcmVkLCBmaWx0ZXJzLnNvcnRCeSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIGZpbHRlcmVkO1xuICB9O1xuXG4gIGNvbnN0IGZpbHRlcmVkTW9kZWxzID0gZ2V0RmlsdGVyZWRNb2RlbHMoKTtcblxuICBjb25zdCBoYW5kbGVUb2dnbGVGYXZvcml0ZSA9IGFzeW5jIChtb2RlbDogQUlNb2RlbCkgPT4ge1xuICAgIGlmICghdXNlciB8fCAhc2VsZWN0ZWRFbmRwb2ludCkgcmV0dXJuO1xuXG4gICAgLy8gUHJldmVuaXIgbcO6bHRpcGxhcyBjaGFtYWRhcyBzaW11bHTDom5lYXMgcGFyYSBvIG1lc21vIG1vZGVsb1xuICAgIGlmIChmYXZvcml0ZVRvZ2dsaW5nLmhhcyhtb2RlbC5pZCkpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdBbHJlYWR5IHRvZ2dsaW5nIGZhdm9yaXRlIGZvciBtb2RlbDonLCBtb2RlbC5pZCk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coYFRvZ2dsaW5nIGZhdm9yaXRlOiAke21vZGVsLmlkfSAoJHttb2RlbC5pc0Zhdm9yaXRlID8gJ3JlbW92aW5nJyA6ICdhZGRpbmcnfSlgKTtcblxuICAgIC8vIENhbGN1bGFyIG8gbm92byBzdGF0dXMgb3RpbWlzdGljYW1lbnRlXG4gICAgY29uc3Qgb3B0aW1pc3RpY05ld1N0YXR1cyA9ICFtb2RlbC5pc0Zhdm9yaXRlO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIE1hcmNhciBjb21vIGVtIHByb2Nlc3NvXG4gICAgICBzZXRGYXZvcml0ZVRvZ2dsaW5nKHByZXYgPT4gbmV3IFNldChwcmV2KS5hZGQobW9kZWwuaWQpKTtcblxuICAgICAgLy8gQVRVQUxJWkHDh8ODTyBPVElNSVNUQTogQXR1YWxpemFyIGEgVUkgaW1lZGlhdGFtZW50ZVxuICAgICAgY29uc3QgdXBkYXRlZEZhdm9yaXRlSWRzID0gbmV3IFNldChmYXZvcml0ZU1vZGVsSWRzKTtcbiAgICAgIGlmIChvcHRpbWlzdGljTmV3U3RhdHVzKSB7XG4gICAgICAgIHVwZGF0ZWRGYXZvcml0ZUlkcy5hZGQobW9kZWwuaWQpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdXBkYXRlZEZhdm9yaXRlSWRzLmRlbGV0ZShtb2RlbC5pZCk7XG4gICAgICB9XG4gICAgICBzZXRGYXZvcml0ZU1vZGVsSWRzKHVwZGF0ZWRGYXZvcml0ZUlkcyk7XG5cbiAgICAgIC8vIEF0dWFsaXphciBvIGFycmF5IGRlIG1vZGVsb3MgaW1lZGlhdGFtZW50ZVxuICAgICAgc2V0TW9kZWxzKHByZXZNb2RlbHMgPT5cbiAgICAgICAgcHJldk1vZGVscy5tYXAobSA9PlxuICAgICAgICAgIG0uaWQgPT09IG1vZGVsLmlkID8geyAuLi5tLCBpc0Zhdm9yaXRlOiBvcHRpbWlzdGljTmV3U3RhdHVzIH0gOiBtXG4gICAgICAgIClcbiAgICAgICk7XG5cbiAgICAgIC8vIEF0dWFsaXphciBvIGNhY2hlIGltZWRpYXRhbWVudGVcbiAgICAgIGlmIChzZWxlY3RlZEVuZHBvaW50KSB7XG4gICAgICAgIGNvbnN0IGNhY2hlS2V5ID0gYCR7c2VsZWN0ZWRFbmRwb2ludC5pZH1fJHtzZWxlY3RlZEVuZHBvaW50Lm5hbWV9YDtcbiAgICAgICAgc2V0TW9kZWxzQ2FjaGUocHJldiA9PiB7XG4gICAgICAgICAgY29uc3QgY3VycmVudENhY2hlID0gcHJldi5nZXQoY2FjaGVLZXkpO1xuICAgICAgICAgIGlmIChjdXJyZW50Q2FjaGUpIHtcbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRNb2RlbHMgPSBjdXJyZW50Q2FjaGUubW9kZWxzLm1hcChtID0+XG4gICAgICAgICAgICAgIG0uaWQgPT09IG1vZGVsLmlkID8geyAuLi5tLCBpc0Zhdm9yaXRlOiBvcHRpbWlzdGljTmV3U3RhdHVzIH0gOiBtXG4gICAgICAgICAgICApO1xuICAgICAgICAgICAgcmV0dXJuIG5ldyBNYXAocHJldikuc2V0KGNhY2hlS2V5LCB7XG4gICAgICAgICAgICAgIG1vZGVsczogdXBkYXRlZE1vZGVscyxcbiAgICAgICAgICAgICAgdGltZXN0YW1wOiBjdXJyZW50Q2FjaGUudGltZXN0YW1wXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9XG4gICAgICAgICAgcmV0dXJuIHByZXY7XG4gICAgICAgIH0pO1xuICAgICAgfVxuXG4gICAgICAvLyBBZ29yYSBmYXplciBhIG9wZXJhw6fDo28gbm8gRmlyZXN0b3JlXG4gICAgICBjb25zdCB1c2VybmFtZSA9IGF3YWl0IGdldFVzZXJuYW1lRnJvbUZpcmVzdG9yZSgpO1xuICAgICAgY29uc3QgYWN0dWFsTmV3U3RhdHVzID0gYXdhaXQgbW9kZWxGYXZvcml0ZXNTZXJ2aWNlLnRvZ2dsZUZhdm9yaXRlKFxuICAgICAgICB1c2VybmFtZSxcbiAgICAgICAgc2VsZWN0ZWRFbmRwb2ludC5pZCxcbiAgICAgICAgbW9kZWwuaWQsXG4gICAgICAgIG1vZGVsLm5hbWVcbiAgICAgICk7XG5cbiAgICAgIC8vIExvZyBhcGVuYXMgc2UgaG91dmVyIGRpZmVyZW7Dp2FcbiAgICAgIGlmIChhY3R1YWxOZXdTdGF0dXMgIT09IG9wdGltaXN0aWNOZXdTdGF0dXMpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ1N0YXR1cyBtaXNtYXRjaCAtIE9wdGltaXN0aWM6Jywgb3B0aW1pc3RpY05ld1N0YXR1cywgJ0FjdHVhbDonLCBhY3R1YWxOZXdTdGF0dXMpO1xuICAgICAgfVxuXG4gICAgICAvLyBTZSBvIHN0YXR1cyByZWFsIGZvciBkaWZlcmVudGUgZG8gb3RpbWlzdGEsIGNvcnJpZ2lyXG4gICAgICBpZiAoYWN0dWFsTmV3U3RhdHVzICE9PSBvcHRpbWlzdGljTmV3U3RhdHVzKSB7XG4gICAgICAgIGNvbnNvbGUud2FybignT3B0aW1pc3RpYyB1cGRhdGUgd2FzIGluY29ycmVjdCwgY29ycmVjdGluZy4uLicpO1xuXG4gICAgICAgIC8vIENvcnJpZ2lyIG8gZXN0YWRvIGRvcyBmYXZvcml0b3NcbiAgICAgICAgY29uc3QgY29ycmVjdGVkRmF2b3JpdGVJZHMgPSBuZXcgU2V0KGZhdm9yaXRlTW9kZWxJZHMpO1xuICAgICAgICBpZiAoYWN0dWFsTmV3U3RhdHVzKSB7XG4gICAgICAgICAgY29ycmVjdGVkRmF2b3JpdGVJZHMuYWRkKG1vZGVsLmlkKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb3JyZWN0ZWRGYXZvcml0ZUlkcy5kZWxldGUobW9kZWwuaWQpO1xuICAgICAgICB9XG4gICAgICAgIHNldEZhdm9yaXRlTW9kZWxJZHMoY29ycmVjdGVkRmF2b3JpdGVJZHMpO1xuXG4gICAgICAgIC8vIENvcnJpZ2lyIG8gYXJyYXkgZGUgbW9kZWxvc1xuICAgICAgICBzZXRNb2RlbHMocHJldk1vZGVscyA9PlxuICAgICAgICAgIHByZXZNb2RlbHMubWFwKG0gPT5cbiAgICAgICAgICAgIG0uaWQgPT09IG1vZGVsLmlkID8geyAuLi5tLCBpc0Zhdm9yaXRlOiBhY3R1YWxOZXdTdGF0dXMgfSA6IG1cbiAgICAgICAgICApXG4gICAgICAgICk7XG5cbiAgICAgICAgLy8gQ29ycmlnaXIgbyBjYWNoZVxuICAgICAgICBpZiAoc2VsZWN0ZWRFbmRwb2ludCkge1xuICAgICAgICAgIGNvbnN0IGNhY2hlS2V5ID0gYCR7c2VsZWN0ZWRFbmRwb2ludC5pZH1fJHtzZWxlY3RlZEVuZHBvaW50Lm5hbWV9YDtcbiAgICAgICAgICBzZXRNb2RlbHNDYWNoZShwcmV2ID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRDYWNoZSA9IHByZXYuZ2V0KGNhY2hlS2V5KTtcbiAgICAgICAgICAgIGlmIChjdXJyZW50Q2FjaGUpIHtcbiAgICAgICAgICAgICAgY29uc3QgY29ycmVjdGVkTW9kZWxzID0gY3VycmVudENhY2hlLm1vZGVscy5tYXAobSA9PlxuICAgICAgICAgICAgICAgIG0uaWQgPT09IG1vZGVsLmlkID8geyAuLi5tLCBpc0Zhdm9yaXRlOiBhY3R1YWxOZXdTdGF0dXMgfSA6IG1cbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgcmV0dXJuIG5ldyBNYXAocHJldikuc2V0KGNhY2hlS2V5LCB7XG4gICAgICAgICAgICAgICAgbW9kZWxzOiBjb3JyZWN0ZWRNb2RlbHMsXG4gICAgICAgICAgICAgICAgdGltZXN0YW1wOiBjdXJyZW50Q2FjaGUudGltZXN0YW1wXG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHByZXY7XG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdG9nZ2xpbmcgZmF2b3JpdGU6JywgZXJyb3IpO1xuXG4gICAgICAvLyBFbSBjYXNvIGRlIGVycm8sIHJldmVydGVyIGEgYXR1YWxpemHDp8OjbyBvdGltaXN0YVxuICAgICAgY29uc3QgcmV2ZXJ0ZWRGYXZvcml0ZUlkcyA9IG5ldyBTZXQoZmF2b3JpdGVNb2RlbElkcyk7XG4gICAgICBpZiAoIW9wdGltaXN0aWNOZXdTdGF0dXMpIHtcbiAgICAgICAgcmV2ZXJ0ZWRGYXZvcml0ZUlkcy5hZGQobW9kZWwuaWQpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcmV2ZXJ0ZWRGYXZvcml0ZUlkcy5kZWxldGUobW9kZWwuaWQpO1xuICAgICAgfVxuICAgICAgc2V0RmF2b3JpdGVNb2RlbElkcyhyZXZlcnRlZEZhdm9yaXRlSWRzKTtcblxuICAgICAgLy8gUmV2ZXJ0ZXIgbyBhcnJheSBkZSBtb2RlbG9zXG4gICAgICBzZXRNb2RlbHMocHJldk1vZGVscyA9PlxuICAgICAgICBwcmV2TW9kZWxzLm1hcChtID0+XG4gICAgICAgICAgbS5pZCA9PT0gbW9kZWwuaWQgPyB7IC4uLm0sIGlzRmF2b3JpdGU6ICFvcHRpbWlzdGljTmV3U3RhdHVzIH0gOiBtXG4gICAgICAgIClcbiAgICAgICk7XG5cbiAgICAgIC8vIFJldmVydGVyIG8gY2FjaGVcbiAgICAgIGlmIChzZWxlY3RlZEVuZHBvaW50KSB7XG4gICAgICAgIGNvbnN0IGNhY2hlS2V5ID0gYCR7c2VsZWN0ZWRFbmRwb2ludC5pZH1fJHtzZWxlY3RlZEVuZHBvaW50Lm5hbWV9YDtcbiAgICAgICAgc2V0TW9kZWxzQ2FjaGUocHJldiA9PiB7XG4gICAgICAgICAgY29uc3QgY3VycmVudENhY2hlID0gcHJldi5nZXQoY2FjaGVLZXkpO1xuICAgICAgICAgIGlmIChjdXJyZW50Q2FjaGUpIHtcbiAgICAgICAgICAgIGNvbnN0IHJldmVydGVkTW9kZWxzID0gY3VycmVudENhY2hlLm1vZGVscy5tYXAobSA9PlxuICAgICAgICAgICAgICBtLmlkID09PSBtb2RlbC5pZCA/IHsgLi4ubSwgaXNGYXZvcml0ZTogIW9wdGltaXN0aWNOZXdTdGF0dXMgfSA6IG1cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgICByZXR1cm4gbmV3IE1hcChwcmV2KS5zZXQoY2FjaGVLZXksIHtcbiAgICAgICAgICAgICAgbW9kZWxzOiByZXZlcnRlZE1vZGVscyxcbiAgICAgICAgICAgICAgdGltZXN0YW1wOiBjdXJyZW50Q2FjaGUudGltZXN0YW1wXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9XG4gICAgICAgICAgcmV0dXJuIHByZXY7XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0gZmluYWxseSB7XG4gICAgICAvLyBSZW1vdmVyIGRvIGVzdGFkbyBkZSBwcm9jZXNzYW1lbnRvXG4gICAgICBzZXRGYXZvcml0ZVRvZ2dsaW5nKHByZXYgPT4ge1xuICAgICAgICBjb25zdCBuZXdTZXQgPSBuZXcgU2V0KHByZXYpO1xuICAgICAgICBuZXdTZXQuZGVsZXRlKG1vZGVsLmlkKTtcbiAgICAgICAgcmV0dXJuIG5ld1NldDtcbiAgICAgIH0pO1xuICAgIH1cbiAgfTtcblxuICAvLyBGdW5jdGlvbiB0byBjaGVjayBpZiBhIG1vZGVsIGlzIGV4cGVuc2l2ZSAob3ZlciAkMjAgcGVyIG1pbGxpb24gdG9rZW5zKVxuICBjb25zdCBpc0V4cGVuc2l2ZU1vZGVsID0gKG1vZGVsOiBBSU1vZGVsKTogYm9vbGVhbiA9PiB7XG4gICAgaWYgKHNlbGVjdGVkRW5kcG9pbnQ/Lm5hbWUgIT09ICdPcGVuUm91dGVyJykgcmV0dXJuIGZhbHNlO1xuICAgIGNvbnN0IHRvdGFsUHJpY2UgPSBvcGVuUm91dGVyU2VydmljZS5nZXRUb3RhbFByaWNlKG1vZGVsKTtcbiAgICByZXR1cm4gdG90YWxQcmljZSA+IDAuMDAwMDI7IC8vICQyMCBwb3IgMU0gdG9rZW5zID0gJDAuMDAwMDIgcG9yIHRva2VuXG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2VsZWN0TW9kZWwgPSAobW9kZWw6IEFJTW9kZWwpID0+IHtcbiAgICAvLyBSYXN0cmVhciBzZWxlw6fDo28gZGUgbW9kZWxvXG4gICAgdHJhY2tNb2RlbFNlbGVjdGlvbihtb2RlbC5pZCk7XG5cbiAgICAvLyBDaGVjayBpZiBpdCdzIGFuIGV4cGVuc2l2ZSBPcGVuUm91dGVyIG1vZGVsXG4gICAgaWYgKGlzRXhwZW5zaXZlTW9kZWwobW9kZWwpKSB7XG4gICAgICBzZXRQZW5kaW5nRXhwZW5zaXZlTW9kZWwobW9kZWwpO1xuICAgICAgc2V0U2hvd0V4cGVuc2l2ZU1vZGVsTW9kYWwodHJ1ZSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIG9uTW9kZWxTZWxlY3QobW9kZWwuaWQpO1xuICAgICAgb25DbG9zZSgpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVDb25maXJtRXhwZW5zaXZlTW9kZWwgPSAoKSA9PiB7XG4gICAgaWYgKHBlbmRpbmdFeHBlbnNpdmVNb2RlbCkge1xuICAgICAgLy8gUmFzdHJlYXIgc2VsZcOnw6NvIGRlIG1vZGVsbyBjYXJvXG4gICAgICB0cmFja01vZGVsU2VsZWN0aW9uKHBlbmRpbmdFeHBlbnNpdmVNb2RlbC5pZCk7XG4gICAgICBvbk1vZGVsU2VsZWN0KHBlbmRpbmdFeHBlbnNpdmVNb2RlbC5pZCk7XG4gICAgICBzZXRTaG93RXhwZW5zaXZlTW9kZWxNb2RhbChmYWxzZSk7XG4gICAgICBzZXRQZW5kaW5nRXhwZW5zaXZlTW9kZWwobnVsbCk7XG4gICAgICBvbkNsb3NlKCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNhbmNlbEV4cGVuc2l2ZU1vZGVsID0gKCkgPT4ge1xuICAgIHNldFNob3dFeHBlbnNpdmVNb2RlbE1vZGFsKGZhbHNlKTtcbiAgICBzZXRQZW5kaW5nRXhwZW5zaXZlTW9kZWwobnVsbCk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlTG9hZE1vcmVNb2RlbHMgPSAoKSA9PiB7XG4gICAgc2V0RGlzcGxheWVkTW9kZWxzQ291bnQocHJldiA9PiBwcmV2ICsgTU9ERUxTX1BFUl9QQUdFKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVVc2VDdXN0b21Nb2RlbCA9ICgpID0+IHtcbiAgICBpZiAoY3VzdG9tTW9kZWxJZC50cmltKCkpIHtcbiAgICAgIG9uTW9kZWxTZWxlY3QoY3VzdG9tTW9kZWxJZC50cmltKCkpO1xuICAgICAgb25DbG9zZSgpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIGZvcsOnYXIgcmVmcmVzaCBkb3MgbW9kZWxvc1xuICBjb25zdCBoYW5kbGVSZWZyZXNoTW9kZWxzID0gKCkgPT4ge1xuICAgIGlmIChzZWxlY3RlZEVuZHBvaW50KSB7XG4gICAgICBjb25zdCBjYWNoZUtleSA9IGAke3NlbGVjdGVkRW5kcG9pbnQuaWR9XyR7c2VsZWN0ZWRFbmRwb2ludC5uYW1lfWA7XG4gICAgICBzZXRNb2RlbHNDYWNoZShwcmV2ID0+IHtcbiAgICAgICAgY29uc3QgbmV3Q2FjaGUgPSBuZXcgTWFwKHByZXYpO1xuICAgICAgICBuZXdDYWNoZS5kZWxldGUoY2FjaGVLZXkpO1xuICAgICAgICByZXR1cm4gbmV3Q2FjaGU7XG4gICAgICB9KTtcblxuXG4gICAgICBpZiAoc2VsZWN0ZWRFbmRwb2ludC5uYW1lID09PSAnT3BlblJvdXRlcicpIHtcbiAgICAgICAgbG9hZE9wZW5Sb3V0ZXJNb2RlbHMoKTtcbiAgICAgIH0gZWxzZSBpZiAoc2VsZWN0ZWRFbmRwb2ludC5uYW1lID09PSAnRGVlcFNlZWsnKSB7XG4gICAgICAgIGxvYWREZWVwU2Vla01vZGVscygpO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuXG5cblxuXG4gIGlmICghaXNPcGVuKSByZXR1cm4gbnVsbDtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjay81MCBiYWNrZHJvcC1ibHVyLXNtIHotNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcC00XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS05NTAvOTUgdmlhLWJsdWUtOTAwLzk1IHRvLWJsdWUtOTUwLzk1IGJhY2tkcm9wLWJsdXIteGwgcm91bmRlZC0yeGwgYm9yZGVyIGJvcmRlci1ibHVlLTYwMC8zMCBzaGFkb3ctMnhsIHctZnVsbCBtYXgtdy00eGwgbWF4LWgtWzgwdmhdIG92ZXJmbG93LWhpZGRlbiByZWxhdGl2ZVwiPlxuICAgICAgICB7LyogRWZlaXRvIGRlIGJyaWxobyBzdXRpbCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MDAvNSB2aWEtdHJhbnNwYXJlbnQgdG8tY3lhbi01MDAvNSBwb2ludGVyLWV2ZW50cy1ub25lIHJvdW5kZWQtMnhsXCI+PC9kaXY+XG5cbiAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgYm9yZGVyLWIgYm9yZGVyLWJsdWUtNzAwLzMwIHJlbGF0aXZlIHotMTBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IHJvdW5kZWQtZnVsbCBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAwIHRvLWN5YW4tNjAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtd2hpdGVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk05Ljc1IDE3TDkgMjBsLTEgMWg4bC0xLTEtLjc1LTNNMyAxM2gxOE01IDE3aDE0YTIgMiAwIDAwMi0yVjVhMiAyIDAgMDAtMi0ySDVhMiAyIDAgMDAtMiAydjEwYTIgMiAwIDAwMiAyelwiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtYmx1ZS0xMDBcIj5TZWxlY2lvbmFyIE1vZGVsbzwvaDI+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIHJvdW5kZWQteGwgaG92ZXI6YmctYmx1ZS04MDAvNDAgdGV4dC1ibHVlLTMwMCBob3Zlcjp0ZXh0LWJsdWUtMjAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBob3ZlcjpzY2FsZS0xMDVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNiAxOEwxOCA2TTYgNmwxMiAxMlwiIC8+XG4gICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRW5kcG9pbnQgU2VsZWN0aW9uICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItM1wiPlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWJsdWUtMjAwXCI+RW5kcG9pbnQ8L2xhYmVsPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlUmVmcmVzaE1vZGVsc31cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZyB8fCAhc2VsZWN0ZWRFbmRwb2ludH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTgwMC80MCB0ZXh0LWJsdWUtMzAwIGhvdmVyOnRleHQtYmx1ZS0yMDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGhvdmVyOnNjYWxlLTEwNSBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFwiXG4gICAgICAgICAgICAgICAgdGl0bGU9XCJBdHVhbGl6YXIgbW9kZWxvc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT17YHctNCBoLTQgJHtsb2FkaW5nID8gJ2FuaW1hdGUtc3BpbicgOiAnJ31gfSBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk00IDR2NWguNTgybTE1LjM1NiAyQTguMDAxIDguMDAxIDAgMDA0LjU4MiA5bTAgMEg5bTExIDExdi01aC0uNTgxbTAgMGE4LjAwMyA4LjAwMyAwIDAxLTE1LjM1Ny0ybTE1LjM1NyAySDE1XCIgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkRW5kcG9pbnQ/LmlkIHx8ICcnfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBlbmRwb2ludCA9IGVuZHBvaW50cy5maW5kKGVwID0+IGVwLmlkID09PSBlLnRhcmdldC52YWx1ZSk7XG4gICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRFbmRwb2ludChlbmRwb2ludCB8fCBudWxsKTtcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWJsdWUtOTAwLzQwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci1ibHVlLTYwMC8zMCByb3VuZGVkLXhsIHB4LTQgcHktMyB0ZXh0LWJsdWUtMTAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDAvNTAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPlNlbGVjaW9uZSB1bSBlbmRwb2ludDwvb3B0aW9uPlxuICAgICAgICAgICAgICB7ZW5kcG9pbnRzLm1hcChlbmRwb2ludCA9PiAoXG4gICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2VuZHBvaW50LmlkfSB2YWx1ZT17ZW5kcG9pbnQuaWR9IGNsYXNzTmFtZT1cImJnLWJsdWUtOTAwIHRleHQtYmx1ZS0xMDBcIj5cbiAgICAgICAgICAgICAgICAgIHtlbmRwb2ludC5uYW1lfVxuICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7c2VsZWN0ZWRFbmRwb2ludD8ubmFtZSA9PT0gJ09wZW5Sb3V0ZXInICYmIChcbiAgICAgICAgICA8PlxuICAgICAgICAgICAgey8qIEZpbHRlcnMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBib3JkZXItYiBib3JkZXItYmx1ZS03MDAvMzAgc3BhY2UteS02IHJlbGF0aXZlIHotMTBcIj5cbiAgICAgICAgICAgICAgey8qIEN1c3RvbSBNb2RlbCBTZWN0aW9uICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBiZy1ibHVlLTkwMC8zMCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQteGwgYm9yZGVyIGJvcmRlci1ibHVlLTYwMC8zMFwiPlxuICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS0yMDAgbWItMyBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWJsdWUtNDAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMiA2VjRtMCAyYTIgMiAwIDEwMCA0bTAtNGEyIDIgMCAxMTAgNG0tNiA4YTIgMiAwIDEwMC00bTAgNGEyIDIgMCAxMDAgNG0wLTR2Mm0wLTZWNG02IDZ2MTBtNi0yYTIgMiAwIDEwMC00bTAgNGEyIDIgMCAxMDAgNG0wLTR2Mm0wLTZWNFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuPk1vZGVsbyBDdXN0b21pemFkbzwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwib3BlbmFpL2dwdC00LTFcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y3VzdG9tTW9kZWxJZH1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDdXN0b21Nb2RlbElkKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgb25LZXlEb3duPXsoZSkgPT4gZS5rZXkgPT09ICdFbnRlcicgJiYgaGFuZGxlVXNlQ3VzdG9tTW9kZWwoKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGJnLWJsdWUtODAwLzQwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci1ibHVlLTYwMC8zMCByb3VuZGVkLXhsIHB4LTQgcHktMyB0ZXh0LWJsdWUtMTAwIHBsYWNlaG9sZGVyOnRleHQtYmx1ZS0zMDAvNjAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMC81MCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlVXNlQ3VzdG9tTW9kZWx9XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshY3VzdG9tTW9kZWxJZC50cmltKCl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTYgcHktMyBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tY3lhbi02MDAgaG92ZXI6ZnJvbS1ibHVlLTUwMCBob3Zlcjp0by1jeWFuLTUwMCBkaXNhYmxlZDpmcm9tLWJsdWUtODAwIGRpc2FibGVkOnRvLWJsdWUtODAwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCB0ZXh0LXdoaXRlIHJvdW5kZWQteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGZvbnQtbWVkaXVtIGhvdmVyOnNjYWxlLTEwNSBkaXNhYmxlZDpob3ZlcjpzY2FsZS0xMDBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICBVc2FyXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS0zMDAvNzAgbXQtM1wiPlxuICAgICAgICAgICAgICAgICAgRGlnaXRlIG8gSUQgY29tcGxldG8gZG8gbW9kZWxvIChleDogb3BlbmFpL2dwdC00LCBhbnRocm9waWMvY2xhdWRlLTMtc29ubmV0KVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIENhdGVnb3J5IFRhYnMgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTEgYmctYmx1ZS05MDAvMzAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLXhsIHAtMSBib3JkZXIgYm9yZGVyLWJsdWUtNjAwLzIwXCI+XG4gICAgICAgICAgICAgICAgeyhbJ3BhaWQnLCAnZnJlZScsICdmYXZvcml0ZXMnXSBhcyBNb2RlbENhdGVnb3J5W10pLm1hcChjYXRlZ29yeSA9PiAoXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIGtleT17Y2F0ZWdvcnl9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEZpbHRlcnMocHJldiA9PiAoeyAuLi5wcmV2LCBjYXRlZ29yeSB9KSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXgtMSBweS0zIHB4LTQgcm91bmRlZC1sZyB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xuICAgICAgICAgICAgICAgICAgICAgIGZpbHRlcnMuY2F0ZWdvcnkgPT09IGNhdGVnb3J5XG4gICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tY3lhbi02MDAgdGV4dC13aGl0ZSBzaGFkb3ctbGcnXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWJsdWUtMzAwIGhvdmVyOnRleHQtYmx1ZS0yMDAgaG92ZXI6YmctYmx1ZS04MDAvMzAnXG4gICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7Y2F0ZWdvcnkgPT09ICdwYWlkJyA/ICdQYWdvcycgOiBjYXRlZ29yeSA9PT0gJ2ZyZWUnID8gJ0dyw6F0aXMnIDogJ0Zhdm9yaXRvcyd9XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIEFkdmFuY2VkIFNlYXJjaCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8QWR2YW5jZWRTZWFyY2hJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWFyY2hUZXJtfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtzZXRTZWFyY2hUZXJtfVxuICAgICAgICAgICAgICAgICAgICAgIHN1Z2dlc3Rpb25zPXtbXX1cbiAgICAgICAgICAgICAgICAgICAgICBpc1NlYXJjaGluZz17aXNTZWFyY2hpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJCdXNjYXIgbW9kZWxvcy4uLiAoZXg6ICdncHQtNCcsICd2aXNpb24nLCAnY2hlYXAnKVwiXG4gICAgICAgICAgICAgICAgICAgICAgc2hvd1N1Z2dlc3Rpb25zPXtmYWxzZX1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmlsdGVycy5zb3J0Qnl9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmlsdGVycyhwcmV2ID0+ICh7IC4uLnByZXYsIHNvcnRCeTogZS50YXJnZXQudmFsdWUgYXMgTW9kZWxTb3J0QnkgfSkpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTgwMC80MCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItYmx1ZS02MDAvMzAgcm91bmRlZC14bCBweC00IHB5LTMgdGV4dC1ibHVlLTEwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwLzUwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtoYXNTZWFyY2hlZCAmJiBzZWFyY2hUZXJtLnRyaW0oKS5sZW5ndGggPiAwfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwibmV3ZXN0XCIgY2xhc3NOYW1lPVwiYmctYmx1ZS05MDAgdGV4dC1ibHVlLTEwMFwiPk1haXMgcmVjZW50ZXM8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInByaWNlX2xvd1wiIGNsYXNzTmFtZT1cImJnLWJsdWUtOTAwIHRleHQtYmx1ZS0xMDBcIj5NZW5vciBwcmXDp288L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInByaWNlX2hpZ2hcIiBjbGFzc05hbWU9XCJiZy1ibHVlLTkwMCB0ZXh0LWJsdWUtMTAwXCI+TWFpb3IgcHJlw6dvPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJjb250ZXh0X2hpZ2hcIiBjbGFzc05hbWU9XCJiZy1ibHVlLTkwMCB0ZXh0LWJsdWUtMTAwXCI+TWFpb3IgY29udGV4dG88L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIFNtYXJ0IENhdGVnb3JpZXMgKi99XG4gICAgICAgICAgICAgICAgeyFoYXNTZWFyY2hlZCAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZENhdGVnb3J5KG51bGwpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTMgcHktMS41IHJvdW5kZWQtbGcgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICFzZWxlY3RlZENhdGVnb3J5XG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWJsdWUtNjAwIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLWJsdWUtODAwLzQwIHRleHQtYmx1ZS0zMDAgaG92ZXI6YmctYmx1ZS03MDAvNTAgaG92ZXI6dGV4dC1ibHVlLTIwMCdcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIFRvZG9zXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICB7c21hcnRDYXRlZ29yaWVzLnNsaWNlKDAsIDYpLm1hcChjYXRlZ29yeSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtjYXRlZ29yeS5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkQ2F0ZWdvcnkoY2F0ZWdvcnkuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtMyBweS0xLjUgcm91bmRlZC1sZyB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTEgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRDYXRlZ29yeSA9PT0gY2F0ZWdvcnkuaWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLWJsdWUtODAwLzQwIHRleHQtYmx1ZS0zMDAgaG92ZXI6YmctYmx1ZS03MDAvNTAgaG92ZXI6dGV4dC1ibHVlLTIwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9e2NhdGVnb3J5LmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntjYXRlZ29yeS5pY29ufTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntjYXRlZ29yeS5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgey8qIFNlYXJjaCBSZXN1bHRzIEluZm8gKi99XG4gICAgICAgICAgICAgICAge2hhc1NlYXJjaGVkICYmIHNlYXJjaFRlcm0udHJpbSgpICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ibHVlLTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtmaWx0ZXJlZE1vZGVscy5sZW5ndGh9IHJlc3VsdGFkb3tmaWx0ZXJlZE1vZGVscy5sZW5ndGggIT09IDEgPyAncycgOiAnJ30gcGFyYSBcIntzZWFyY2hUZXJtfVwiXG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2NsZWFyU2VhcmNofVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS00MDAgaG92ZXI6dGV4dC1ibHVlLTMwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgTGltcGFyIGJ1c2NhXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIE1vZGVscyBMaXN0ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3cteS1zY3JvbGwgcC02IG1heC1oLVszMnJlbV0gcmVsYXRpdmUgei0xMFwiPlxuICAgICAgICAgICAgICB7bG9hZGluZyAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIHB5LThcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIGJnLWJsdWUtOTAwLzUwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci1ibHVlLTYwMC8zMCByb3VuZGVkLXhsIHB4LTQgcHktM1wiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC02IHctNiBib3JkZXItdC0yIGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNDAwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS0yMDAgdGV4dC1zbSBmb250LW1lZGl1bVwiPkNhcnJlZ2FuZG8gbW9kZWxvcy4uLjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtOTAwLzMwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci1yZWQtNjAwLzQwIHJvdW5kZWQteGwgcC00IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggcm91bmRlZC1mdWxsIGJnLXJlZC01MDAvMjAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1yZWQtNDAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTIgOXYybTAgNGguMDFtLTYuOTM4IDRoMTMuODU2YzEuNTQgMCAyLjUwMi0xLjY2NyAxLjczMi0yLjVMMTMuNzMyIDRjLS43Ny0uODMzLTEuOTY0LS44MzMtMi43MzIgMEwzLjczMiAxNi41Yy0uNzcuODMzLjE5MiAyLjUgMS43MzIgMi41elwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC0zMDAgZm9udC1tZWRpdW1cIj57ZXJyb3J9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgeyFsb2FkaW5nICYmICFlcnJvciAmJiBmaWx0ZXJlZE1vZGVscy5sZW5ndGggPT09IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgbXgtYXV0byByb3VuZGVkLWZ1bGwgYmctYmx1ZS05MDAvMzAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC1ibHVlLTQwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0yMSAyMWwtNi02bTItNWE3IDcgMCAxMS0xNCAwIDcgNyAwIDAxMTQgMHpcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTMwMCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICB7aGFzU2VhcmNoZWQgJiYgc2VhcmNoVGVybS50cmltKClcbiAgICAgICAgICAgICAgICAgICAgICA/IGBOZW5odW0gcmVzdWx0YWRvIHBhcmEgXCIke3NlYXJjaFRlcm19XCJgXG4gICAgICAgICAgICAgICAgICAgICAgOiBzZWxlY3RlZENhdGVnb3J5XG4gICAgICAgICAgICAgICAgICAgICAgPyBgTmVuaHVtIG1vZGVsbyBuYSBjYXRlZ29yaWEgc2VsZWNpb25hZGFgXG4gICAgICAgICAgICAgICAgICAgICAgOiAnTmVuaHVtIG1vZGVsbyBlbmNvbnRyYWRvJ1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtYmx1ZS00MDAvNzAgdGV4dC1zbSBtdC0yIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgICAgICB7aGFzU2VhcmNoZWQgJiYgc2VhcmNoVGVybS50cmltKCkgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwPlRlbnRlOjwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJsaXN0LWRpc2MgbGlzdC1pbnNpZGUgc3BhY2UteS0xIHRleHQtbGVmdCBtYXgtdy14cyBteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxsaT5WZXJpZmljYXIgYSBvcnRvZ3JhZmlhPC9saT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpPlVzYXIgdGVybW9zIG1haXMgZ2Vuw6lyaWNvczwvbGk+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxsaT5FeHBsb3JhciBhcyBjYXRlZ29yaWFzPC9saT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpPkxpbXBhciBmaWx0cm9zIGF0aXZvczwvbGk+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgIDxwPlRlbnRlIGFqdXN0YXIgb3MgZmlsdHJvcyBvdSB1c2FyIGFzIGNhdGVnb3JpYXM8L3A+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuXG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICB7ZmlsdGVyZWRNb2RlbHMuc2xpY2UoMCwgZGlzcGxheWVkTW9kZWxzQ291bnQpLm1hcChtb2RlbCA9PiB7XG4gICAgICAgICAgICAgICAgICAvLyBFbmNvbnRyYXIgbyByZXN1bHRhZG8gZGEgYnVzY2EgcGFyYSBlc3RlIG1vZGVsbyAoc2UgaG91dmVyKVxuICAgICAgICAgICAgICAgICAgY29uc3Qgc2VhcmNoUmVzdWx0ID0gaGFzU2VhcmNoZWQgPyBzZWFyY2hSZXN1bHRzLmZpbmQociA9PiByLm1vZGVsLmlkID09PSBtb2RlbC5pZCkgOiBudWxsO1xuXG4gICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICA8TW9kZWxDYXJkXG4gICAgICAgICAgICAgICAgICAgICAga2V5PXttb2RlbC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICBtb2RlbD17bW9kZWx9XG4gICAgICAgICAgICAgICAgICAgICAgaXNTZWxlY3RlZD17Y3VycmVudE1vZGVsID09PSBtb2RlbC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICBvblNlbGVjdD17KCkgPT4gaGFuZGxlU2VsZWN0TW9kZWwobW9kZWwpfVxuICAgICAgICAgICAgICAgICAgICAgIG9uVG9nZ2xlRmF2b3JpdGU9eygpID0+IGhhbmRsZVRvZ2dsZUZhdm9yaXRlKG1vZGVsKX1cbiAgICAgICAgICAgICAgICAgICAgICBpc1RvZ2dsaW5nPXtmYXZvcml0ZVRvZ2dsaW5nLmhhcyhtb2RlbC5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgc2VydmljZT17b3BlblJvdXRlclNlcnZpY2V9XG4gICAgICAgICAgICAgICAgICAgICAgc2VhcmNoVGVybT17aGFzU2VhcmNoZWQgPyBzZWFyY2hUZXJtIDogJyd9XG4gICAgICAgICAgICAgICAgICAgICAgc2VhcmNoUmVzdWx0PXtzZWFyY2hSZXN1bHR9XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogTG9hZCBNb3JlIEJ1dHRvbiAqL31cbiAgICAgICAgICAgICAgeyFsb2FkaW5nICYmICFlcnJvciAmJiBmaWx0ZXJlZE1vZGVscy5sZW5ndGggPiBkaXNwbGF5ZWRNb2RlbHNDb3VudCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIG10LTZcIj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTG9hZE1vcmVNb2RlbHN9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTYgcHktMyBiZy1ibHVlLTgwMC80MCBob3ZlcjpiZy1ibHVlLTcwMC81MCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItYmx1ZS02MDAvMzAgaG92ZXI6Ym9yZGVyLWJsdWUtNTAwLzUwIHJvdW5kZWQteGwgdGV4dC1ibHVlLTIwMCBob3Zlcjp0ZXh0LWJsdWUtMTAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgaG92ZXI6c2NhbGUtMTA1XCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+Q2FycmVnYXIgbWFpcyBtb2RlbG9zPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTkgOWwtNyA3LTctN1wiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgey8qIE1vZGVscyBjb3VudCBpbmZvICovfVxuICAgICAgICAgICAgICB7IWxvYWRpbmcgJiYgIWVycm9yICYmIGZpbHRlcmVkTW9kZWxzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbXQtNCBzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNDAwLzcwXCI+XG4gICAgICAgICAgICAgICAgICAgIE1vc3RyYW5kbyB7TWF0aC5taW4oZGlzcGxheWVkTW9kZWxzQ291bnQsIGZpbHRlcmVkTW9kZWxzLmxlbmd0aCl9IGRlIHtmaWx0ZXJlZE1vZGVscy5sZW5ndGh9IG1vZGVsb3NcbiAgICAgICAgICAgICAgICAgICAge21vZGVscy5sZW5ndGggIT09IGZpbHRlcmVkTW9kZWxzLmxlbmd0aCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMiB0ZXh0LWJsdWUtMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAoe21vZGVscy5sZW5ndGh9IHRvdGFsKVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogU2VhcmNoIHBlcmZvcm1hbmNlIGluZGljYXRvciAqL31cbiAgICAgICAgICAgICAgICAgIHtoYXNTZWFyY2hlZCAmJiBzZWFyY2hUZXJtLnRyaW0oKSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC00IHRleHQteHMgdGV4dC1ibHVlLTQwMC82MFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPvCflI0gQnVzY2E6IHtzZWFyY2hUZXJtfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICB7c2VhcmNoUmVzdWx0cy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuKaoSB7c2VhcmNoUmVzdWx0cy5sZW5ndGh9IHJlc3VsdGFkb3M8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC8+XG4gICAgICAgICl9XG5cbiAgICAgICAge3NlbGVjdGVkRW5kcG9pbnQ/Lm5hbWUgPT09ICdEZWVwU2VlaycgJiYgKFxuICAgICAgICAgIDw+XG4gICAgICAgICAgICB7LyogRGVlcFNlZWsgSGVhZGVyIGFuZCBGaWx0ZXJzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgYm9yZGVyLWIgYm9yZGVyLXNsYXRlLTcwMC8zMCBzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtc2xhdGUtMTAwIG1iLTJcIj5Nb2RlbG9zIERlZXBTZWVrPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc2xhdGUtNDAwXCI+RXNjb2xoYSBlbnRyZSBub3Nzb3MgbW9kZWxvcyBlc3BlY2lhbGl6YWRvczwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cblxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBEZWVwU2VlayBNb2RlbHMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy15LWF1dG8gcC02XCI+XG4gICAgICAgICAgICAgIHtsb2FkaW5nICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgYmctYmx1ZS05MDAvNTAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLWJsdWUtNjAwLzMwIHJvdW5kZWQteGwgcHgtNCBweS0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTYgdy02IGJvcmRlci10LTIgYm9yZGVyLWItMiBib3JkZXItYmx1ZS00MDBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ibHVlLTIwMCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+Q2FycmVnYW5kbyBtb2RlbG9zLi4uPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAge2Vycm9yICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC05MDAvMzAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLXJlZC02MDAvNDAgcm91bmRlZC14bCBwLTQgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCByb3VuZGVkLWZ1bGwgYmctcmVkLTUwMC8yMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXJlZC00MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMiA5djJtMCA0aC4wMW0tNi45MzggNGgxMy44NTZjMS41NCAwIDIuNTAyLTEuNjY3IDEuNzMyLTIuNUwxMy43MzIgNGMtLjc3LS44MzMtMS45NjQtLjgzMy0yLjczMiAwTDMuNzMyIDE2LjVjLS43Ny44MzMuMTkyIDIuNSAxLjczMiAyLjV6XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTMwMCBmb250LW1lZGl1bVwiPntlcnJvcn08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICB7IWxvYWRpbmcgJiYgIWVycm9yICYmIG1vZGVscy5sZW5ndGggPT09IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgbXgtYXV0byByb3VuZGVkLWZ1bGwgYmctYmx1ZS05MDAvMzAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC1ibHVlLTQwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0yMSAyMWwtNi02bTItNWE3IDcgMCAxMS0xNCAwIDcgNyAwIDAxMTQgMHpcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTMwMCBmb250LW1lZGl1bVwiPk5lbmh1bSBtb2RlbG8gZW5jb250cmFkbzwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmx1ZS00MDAvNzAgdGV4dC1zbSBtdC0xXCI+VGVudGUgYWp1c3RhciBvcyBmaWx0cm9zIGRlIGJ1c2NhPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIHshbG9hZGluZyAmJiAhZXJyb3IgJiYgbW9kZWxzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICAgICAgICAgICAge21vZGVscy5tYXAobW9kZWwgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8RGVlcFNlZWtNb2RlbENhcmRcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e21vZGVsLmlkfVxuICAgICAgICAgICAgICAgICAgICAgIG1vZGVsPXttb2RlbH1cbiAgICAgICAgICAgICAgICAgICAgICBpc1NlbGVjdGVkPXtjdXJyZW50TW9kZWwgPT09IG1vZGVsLmlkfVxuICAgICAgICAgICAgICAgICAgICAgIG9uU2VsZWN0PXsoKSA9PiBoYW5kbGVTZWxlY3RNb2RlbChtb2RlbCl9XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvPlxuICAgICAgICApfVxuXG4gICAgICAgIHtzZWxlY3RlZEVuZHBvaW50Py5uYW1lICE9PSAnT3BlblJvdXRlcicgJiYgc2VsZWN0ZWRFbmRwb2ludD8ubmFtZSAhPT0gJ0RlZXBTZWVrJyAmJiBzZWxlY3RlZEVuZHBvaW50ICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtOCB0ZXh0LWNlbnRlciByZWxhdGl2ZSB6LTEwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBteC1hdXRvIHJvdW5kZWQtZnVsbCBiZy1ibHVlLTkwMC8zMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtYi00XCI+XG4gICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LWJsdWUtNDAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEzIDE2aC0xdi00aC0xbTEtNGguMDFNMjEgMTJhOSA5IDAgMTEtMTggMCA5IDkgMCAwMTE4IDB6XCIgLz5cbiAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmx1ZS0zMDAgZm9udC1tZWRpdW1cIj5TZWxlw6fDo28gZGUgbW9kZWxvcyBkaXNwb27DrXZlbCBwYXJhIE9wZW5Sb3V0ZXIgZSBEZWVwU2VlazwvcD5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmx1ZS00MDAvNzAgdGV4dC1zbSBtdC0xXCI+U2VsZWNpb25lIHVtIGRlc3NlcyBlbmRwb2ludHMgcGFyYSB2ZXIgb3MgbW9kZWxvcyBkaXNwb27DrXZlaXM8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEV4cGVuc2l2ZSBNb2RlbCBDb25maXJtYXRpb24gTW9kYWwgKi99XG4gICAgICA8RXhwZW5zaXZlTW9kZWxDb25maXJtYXRpb25Nb2RhbFxuICAgICAgICBpc09wZW49e3Nob3dFeHBlbnNpdmVNb2RlbE1vZGFsfVxuICAgICAgICBtb2RlbD17cGVuZGluZ0V4cGVuc2l2ZU1vZGVsfVxuICAgICAgICBvbkNvbmZpcm09e2hhbmRsZUNvbmZpcm1FeHBlbnNpdmVNb2RlbH1cbiAgICAgICAgb25DYW5jZWw9e2hhbmRsZUNhbmNlbEV4cGVuc2l2ZU1vZGVsfVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbi8vIE1vZGVsIENhcmQgQ29tcG9uZW50XG5pbnRlcmZhY2UgTW9kZWxDYXJkUHJvcHMge1xuICBtb2RlbDogQUlNb2RlbDtcbiAgaXNTZWxlY3RlZDogYm9vbGVhbjtcbiAgb25TZWxlY3Q6ICgpID0+IHZvaWQ7XG4gIG9uVG9nZ2xlRmF2b3JpdGU6ICgpID0+IHZvaWQ7XG4gIGlzVG9nZ2xpbmc/OiBib29sZWFuO1xuICBzZXJ2aWNlPzogdHlwZW9mIG9wZW5Sb3V0ZXJTZXJ2aWNlIHwgdHlwZW9mIGRlZXBTZWVrU2VydmljZTtcbiAgc2VhcmNoVGVybT86IHN0cmluZztcbiAgc2VhcmNoUmVzdWx0PzogeyBtb2RlbDogQUlNb2RlbDsgc2NvcmU6IG51bWJlcjsgbWF0Y2hlZEZpZWxkczogc3RyaW5nW107IGhpZ2hsaWdodGVkTmFtZT86IHN0cmluZzsgaGlnaGxpZ2h0ZWREZXNjcmlwdGlvbj86IHN0cmluZzsgfSB8IG51bGw7XG59XG5cbmNvbnN0IE1vZGVsQ2FyZCA9ICh7XG4gIG1vZGVsLFxuICBpc1NlbGVjdGVkLFxuICBvblNlbGVjdCxcbiAgb25Ub2dnbGVGYXZvcml0ZSxcbiAgaXNUb2dnbGluZyA9IGZhbHNlLFxuICBzZXJ2aWNlID0gb3BlblJvdXRlclNlcnZpY2UsXG4gIHNlYXJjaFRlcm0gPSAnJyxcbiAgc2VhcmNoUmVzdWx0XG59OiBNb2RlbENhcmRQcm9wcykgPT4ge1xuICBjb25zdCBpc0V4cGVuc2l2ZSA9IHNlcnZpY2UgPT09IG9wZW5Sb3V0ZXJTZXJ2aWNlICYmIG9wZW5Sb3V0ZXJTZXJ2aWNlLmdldFRvdGFsUHJpY2UobW9kZWwpID4gMC4wMDAwMjsgLy8gJDIwIHBvciAxTSB0b2tlbnNcblxuICAvLyBMb2cgcGFyYSBkZWJ1ZyAoYXBlbmFzIHF1YW5kbyBuZWNlc3PDoXJpbylcbiAgLy8gY29uc29sZS5sb2coYE1vZGVsQ2FyZCAke21vZGVsLmlkfTogaXNGYXZvcml0ZT0ke21vZGVsLmlzRmF2b3JpdGV9LCBpc1RvZ2dsaW5nPSR7aXNUb2dnbGluZ31gKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGRhdGEtbW9kZWwtaWQ9e21vZGVsLmlkfVxuICAgICAgY2xhc3NOYW1lPXtgcC01IHJvdW5kZWQteGwgYm9yZGVyIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBiYWNrZHJvcC1ibHVyLXNtIGhvdmVyOnNjYWxlLVsxLjAyXSByZWxhdGl2ZSAke1xuICAgICAgICBpc1NlbGVjdGVkXG4gICAgICAgICAgPyAnYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTYwMC8zMCB0by1jeWFuLTYwMC8yMCBib3JkZXItYmx1ZS01MDAvNTAgc2hhZG93LWxnIHNoYWRvdy1ibHVlLTUwMC8yMCdcbiAgICAgICAgICA6ICdiZy1ibHVlLTkwMC8zMCBib3JkZXItYmx1ZS02MDAvMzAgaG92ZXI6YmctYmx1ZS04MDAvNDAgaG92ZXI6Ym9yZGVyLWJsdWUtNTAwLzQwJ1xuICAgICAgfWB9PlxuICAgICAgey8qIEV4cGVuc2l2ZSBNb2RlbCBXYXJuaW5nIEJhZGdlICovfVxuICAgICAge2lzRXhwZW5zaXZlICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTIgLXJpZ2h0LTIgei0xMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWFtYmVyLTUwMCB0by1vcmFuZ2UtNTAwIHJvdW5kZWQtZnVsbCBwLTEuNSBzaGFkb3ctbGcgYm9yZGVyLTIgYm9yZGVyLXNsYXRlLTgwMFwiPlxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtd2hpdGVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDl2Mm0wIDRoLjAxbS02LjkzOCA0aDEzLjg1NmMxLjU0IDAgMi41MDItMS42NjcgMS43MzItMi41TDEzLjczMiA0Yy0uNzctLjgzMy0xLjk2NC0uODMzLTIuNzMyIDBMNC4wODIgMTYuNWMtLjc3LjgzMy4xOTIgMi41IDEuNzMyIDIuNXpcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy0wXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgbWItM1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtYmx1ZS0xMDAgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgIHtzZWFyY2hSZXN1bHQ/LmhpZ2hsaWdodGVkTmFtZSA/IChcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3sgX19odG1sOiBzZWFyY2hSZXN1bHQuaGlnaGxpZ2h0ZWROYW1lIH19IC8+XG4gICAgICAgICAgICAgICAgICApIDogc2VhcmNoVGVybSA/IChcbiAgICAgICAgICAgICAgICAgICAgPEhpZ2hsaWdodGVkVGV4dCB0ZXh0PXttb2RlbC5uYW1lfSBoaWdobGlnaHQ9e3NlYXJjaFRlcm19IC8+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICBtb2RlbC5uYW1lXG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAge2lzRXhwZW5zaXZlICYmIChcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgYmctYW1iZXItNTAwLzIwIHRleHQtYW1iZXItMzAwIHB4LTIgcHktMC41IHJvdW5kZWQtZnVsbCBib3JkZXIgYm9yZGVyLWFtYmVyLTUwMC8zMCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICBDQVJPXG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICB7c2VhcmNoUmVzdWx0ICYmIHNlYXJjaFJlc3VsdC5tYXRjaGVkRmllbGRzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICB7c2VhcmNoUmVzdWx0Lm1hdGNoZWRGaWVsZHMuc2xpY2UoMCwgMykubWFwKGZpZWxkID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhblxuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtmaWVsZH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgYmctZ3JlZW4tNTAwLzIwIHRleHQtZ3JlZW4tMzAwIHB4LTEuNSBweS0wLjUgcm91bmRlZCBib3JkZXIgYm9yZGVyLWdyZWVuLTUwMC8zMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT17YEVuY29udHJhZG8gZW06ICR7ZmllbGR9YH1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICB7ZmllbGQgPT09ICduYW1lJyA/ICfwn5OdJyA6IGZpZWxkID09PSAnZGVzY3JpcHRpb24nID8gJ/Cfk4QnIDogZmllbGQgPT09ICdwcm92aWRlcicgPyAn8J+PoicgOiBmaWVsZCA9PT0gJ3RhZ3MnID8gJ/Cfj7fvuI8nIDogJ/CflI0nfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsdWUtMzAwLzcwIHRydW5jYXRlIG10LTEgZm9udC1tb25vXCI+XG4gICAgICAgICAgICAgICAgPEhpZ2hsaWdodGVkVGV4dCB0ZXh0PXttb2RlbC5pZH0gaGlnaGxpZ2h0PXtzZWFyY2hUZXJtfSAvPlxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHtzZXJ2aWNlLmlzRnJlZU1vZGVsKG1vZGVsKSAmJiAoXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInB4LTMgcHktMSBiZy1ncmVlbi02MDAvMzAgdGV4dC1ncmVlbi0zMDAgdGV4dC14cyByb3VuZGVkLWZ1bGwgYm9yZGVyIGJvcmRlci1ncmVlbi01MDAvMzAgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICBHcsOhdGlzXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRGVzY3JpcHRpb24gd2l0aCBoaWdobGlnaHRpbmcgKi99XG4gICAgICAgICAge21vZGVsLmRlc2NyaXB0aW9uICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMyBtYi0zXCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTMwMC84MCBsaW5lLWNsYW1wLTJcIj5cbiAgICAgICAgICAgICAgICB7c2VhcmNoUmVzdWx0Py5oaWdobGlnaHRlZERlc2NyaXB0aW9uID8gKFxuICAgICAgICAgICAgICAgICAgPHNwYW4gZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3sgX19odG1sOiBzZWFyY2hSZXN1bHQuaGlnaGxpZ2h0ZWREZXNjcmlwdGlvbiB9fSAvPlxuICAgICAgICAgICAgICAgICkgOiBzZWFyY2hUZXJtID8gKFxuICAgICAgICAgICAgICAgICAgPEhpZ2hsaWdodGVkVGV4dCB0ZXh0PXttb2RlbC5kZXNjcmlwdGlvbn0gaGlnaGxpZ2h0PXtzZWFyY2hUZXJtfSAvPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICBtb2RlbC5kZXNjcmlwdGlvblxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTMgZ2FwLTQgdGV4dC1zbVwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTgwMC8zMCByb3VuZGVkLWxnIHAtMyBib3JkZXIgYm9yZGVyLWJsdWUtNjAwLzIwXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJsb2NrIHRleHQteHMgdGV4dC1ibHVlLTMwMC83MCBmb250LW1lZGl1bSBtYi0xXCI+Q29udGV4dG88L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS0yMDAgZm9udC1zZW1pYm9sZFwiPntzZXJ2aWNlLmZvcm1hdENvbnRleHRMZW5ndGgobW9kZWwuY29udGV4dF9sZW5ndGgpfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTgwMC8zMCByb3VuZGVkLWxnIHAtMyBib3JkZXIgYm9yZGVyLWJsdWUtNjAwLzIwXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJsb2NrIHRleHQteHMgdGV4dC1ibHVlLTMwMC83MCBmb250LW1lZGl1bSBtYi0xXCI+SW5wdXQ8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS0yMDAgZm9udC1zZW1pYm9sZFwiPntzZXJ2aWNlLmZvcm1hdFByaWNlKG1vZGVsLnByaWNpbmcucHJvbXB0KX0vMU08L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS04MDAvMzAgcm91bmRlZC1sZyBwLTMgYm9yZGVyIGJvcmRlci1ibHVlLTYwMC8yMFwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXhzIHRleHQtYmx1ZS0zMDAvNzAgZm9udC1tZWRpdW0gbWItMVwiPk91dHB1dDwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ibHVlLTIwMCBmb250LXNlbWlib2xkXCI+e3NlcnZpY2UuZm9ybWF0UHJpY2UobW9kZWwucHJpY2luZy5jb21wbGV0aW9uKX0vMU08L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbWwtNFwiPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e29uVG9nZ2xlRmF2b3JpdGV9XG4gICAgICAgICAgICBkaXNhYmxlZD17aXNUb2dnbGluZ31cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtMi41IHJvdW5kZWQteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGhvdmVyOnNjYWxlLTEwNSBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCAke1xuICAgICAgICAgICAgICBtb2RlbC5pc0Zhdm9yaXRlXG4gICAgICAgICAgICAgICAgPyAndGV4dC15ZWxsb3ctNDAwIGhvdmVyOnRleHQteWVsbG93LTMwMCBiZy15ZWxsb3ctNTAwLzIwIGJvcmRlciBib3JkZXIteWVsbG93LTUwMC8zMCdcbiAgICAgICAgICAgICAgICA6ICd0ZXh0LWJsdWUtMzAwIGhvdmVyOnRleHQteWVsbG93LTQwMCBiZy1ibHVlLTgwMC8zMCBib3JkZXIgYm9yZGVyLWJsdWUtNjAwLzIwIGhvdmVyOmJnLXllbGxvdy01MDAvMjAgaG92ZXI6Ym9yZGVyLXllbGxvdy01MDAvMzAnXG4gICAgICAgICAgICB9YH1cbiAgICAgICAgICAgIHRpdGxlPXtpc1RvZ2dsaW5nID8gJ1Byb2Nlc3NhbmRvLi4uJyA6IChtb2RlbC5pc0Zhdm9yaXRlID8gJ1JlbW92ZXIgZG9zIGZhdm9yaXRvcycgOiAnQWRpY2lvbmFyIGFvcyBmYXZvcml0b3MnKX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICB7aXNUb2dnbGluZyA/IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNSB3LTUgYm9yZGVyLXQtMiBib3JkZXItYi0yIGJvcmRlci1jdXJyZW50XCI+PC9kaXY+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTVcIiBmaWxsPXttb2RlbC5pc0Zhdm9yaXRlID8gJ2N1cnJlbnRDb2xvcicgOiAnbm9uZSd9IHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTEuMDQ5IDIuOTI3Yy4zLS45MjEgMS42MDMtLjkyMSAxLjkwMiAwbDEuNTE5IDQuNjc0YTEgMSAwIDAwLjk1LjY5aDQuOTE1Yy45NjkgMCAxLjM3MSAxLjI0LjU4OCAxLjgxbC0zLjk3NiAyLjg4OGExIDEgMCAwMC0uMzYzIDEuMTE4bDEuNTE4IDQuNjc0Yy4zLjkyMi0uNzU1IDEuNjg4LTEuNTM4IDEuMTE4bC0zLjk3Ni0yLjg4OGExIDEgMCAwMC0xLjE3NiAwbC0zLjk3NiAyLjg4OGMtLjc4My41Ny0xLjgzOC0uMTk3LTEuNTM4LTEuMTE4bDEuNTE4LTQuNjc0YTEgMSAwIDAwLS4zNjMtMS4xMThsLTMuOTc2LTIuODg4Yy0uNzg0LS41Ny0uMzgtMS44MS41ODgtMS44MWg0LjkxNGExIDEgMCAwMC45NTEtLjY5bDEuNTE5LTQuNjc0elwiIC8+XG4gICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e29uU2VsZWN0fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNiBweS0yLjUgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLWN5YW4tNjAwIGhvdmVyOmZyb20tYmx1ZS01MDAgaG92ZXI6dG8tY3lhbi01MDAgdGV4dC13aGl0ZSByb3VuZGVkLXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBmb250LW1lZGl1bSBob3ZlcjpzY2FsZS0xMDUgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy1ibHVlLTUwMC8zMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgU2VsZWNpb25hclxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuLy8gRGVlcFNlZWsgTW9kZWwgQ2FyZCBDb21wb25lbnRcbmludGVyZmFjZSBEZWVwU2Vla01vZGVsQ2FyZFByb3BzIHtcbiAgbW9kZWw6IEFJTW9kZWw7XG4gIGlzU2VsZWN0ZWQ6IGJvb2xlYW47XG4gIG9uU2VsZWN0OiAoKSA9PiB2b2lkO1xufVxuXG5jb25zdCBEZWVwU2Vla01vZGVsQ2FyZCA9ICh7IG1vZGVsLCBpc1NlbGVjdGVkLCBvblNlbGVjdCB9OiBEZWVwU2Vla01vZGVsQ2FyZFByb3BzKSA9PiB7XG4gIGNvbnN0IGdldE1vZGVsSWNvbiA9IChtb2RlbElkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAobW9kZWxJZCA9PT0gJ2RlZXBzZWVrLWNoYXQnKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC1ibHVlLTQwMFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgPHBhdGggZD1cIk0xMiAyQzYuNDggMiAyIDYuNDggMiAxMnM0LjQ4IDEwIDEwIDEwIDEwLTQuNDggMTAtMTBTMTcuNTIgMiAxMiAyem0tMiAxNWwtNS01IDEuNDEtMS40MUwxMCAxNC4xN2w3LjU5LTcuNTlMMTkgOGwtOSA5elwiLz5cbiAgICAgICAgPC9zdmc+XG4gICAgICApO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC1jeWFuLTQwMFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgPHBhdGggZD1cIk05IDEybDIgMiA0LTRtNiAyYTkgOSAwIDExLTE4IDAgOSA5IDAgMDExOCAwelwiLz5cbiAgICAgICAgPC9zdmc+XG4gICAgICApO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgcmVsYXRpdmUgcC02IHJvdW5kZWQtMnhsIGJvcmRlciB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgY3Vyc29yLXBvaW50ZXIgZ3JvdXAgYmFja2Ryb3AtYmx1ci1zbSBob3ZlcjpzY2FsZS1bMS4wMl0gJHtcbiAgICAgIGlzU2VsZWN0ZWRcbiAgICAgICAgPyAnYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTYwMC8zMCB0by1jeWFuLTYwMC8yMCBib3JkZXItYmx1ZS01MDAvNTAgc2hhZG93LWxnIHNoYWRvdy1ibHVlLTUwMC8yMCdcbiAgICAgICAgOiAnYmctYmx1ZS05MDAvMzAgYm9yZGVyLWJsdWUtNjAwLzMwIGhvdmVyOmJnLWJsdWUtODAwLzQwIGhvdmVyOmJvcmRlci1ibHVlLTUwMC80MCdcbiAgICB9YH1cbiAgICBvbkNsaWNrPXtvblNlbGVjdH0+XG4gICAgICB7LyogQmFja2dyb3VuZCBncmFkaWVudCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCByb3VuZGVkLTJ4bCBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTMwMCBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNjAwLzEwIHRvLWN5YW4tNjAwLzEwXCI+PC9kaXY+XG5cbiAgICAgIHsvKiBDb250ZW50ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwXCI+XG4gICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IG1iLTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiByb3VuZGVkLXhsIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MDAgdG8tY3lhbi02MDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgIHtnZXRNb2RlbEljb24obW9kZWwuaWQpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWJsdWUtMTAwIHRleHQtbGdcIj57bW9kZWwubmFtZX08L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtMzAwLzcwIG10LTFcIj57bW9kZWwuZGVzY3JpcHRpb259PC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogU3RhdHMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMyBnYXAtNCBtYi02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTgwMC8zMCByb3VuZGVkLWxnIHAtMyBib3JkZXIgYm9yZGVyLWJsdWUtNjAwLzIwIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTMwMC83MCBmb250LW1lZGl1bSBtYi0xXCI+Q29udGV4dG88L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtYmx1ZS0yMDBcIj5cbiAgICAgICAgICAgICAge2RlZXBTZWVrU2VydmljZS5mb3JtYXRDb250ZXh0TGVuZ3RoKG1vZGVsLmNvbnRleHRfbGVuZ3RoKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS04MDAvMzAgcm91bmRlZC1sZyBwLTMgYm9yZGVyIGJvcmRlci1ibHVlLTYwMC8yMCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS0zMDAvNzAgZm9udC1tZWRpdW0gbWItMVwiPklucHV0PC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LWJsdWUtMjAwXCI+XG4gICAgICAgICAgICAgIHtkZWVwU2Vla1NlcnZpY2UuZm9ybWF0UHJpY2UobW9kZWwucHJpY2luZy5wcm9tcHQpfS8xTVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTgwMC8zMCByb3VuZGVkLWxnIHAtMyBib3JkZXIgYm9yZGVyLWJsdWUtNjAwLzIwIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTMwMC83MCBmb250LW1lZGl1bSBtYi0xXCI+T3V0cHV0PC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LWJsdWUtMjAwXCI+XG4gICAgICAgICAgICAgIHtkZWVwU2Vla1NlcnZpY2UuZm9ybWF0UHJpY2UobW9kZWwucHJpY2luZy5jb21wbGV0aW9uKX0vMU1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogU2VsZWN0IEJ1dHRvbiAqL31cbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgb25TZWxlY3QoKTtcbiAgICAgICAgICB9fVxuICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBweS0zIHB4LTQgcm91bmRlZC14bCBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2NhbGUtMTA1ICR7XG4gICAgICAgICAgICBpc1NlbGVjdGVkXG4gICAgICAgICAgICAgID8gJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1jeWFuLTYwMCB0ZXh0LXdoaXRlIHNoYWRvdy1sZyBzaGFkb3ctYmx1ZS01MDAvMzAnXG4gICAgICAgICAgICAgIDogJ2JnLWJsdWUtODAwLzQwIHRleHQtYmx1ZS0yMDAgaG92ZXI6YmctZ3JhZGllbnQtdG8tciBob3Zlcjpmcm9tLWJsdWUtNjAwIGhvdmVyOnRvLWN5YW4tNjAwIGhvdmVyOnRleHQtd2hpdGUgYm9yZGVyIGJvcmRlci1ibHVlLTYwMC8zMCdcbiAgICAgICAgICB9YH1cbiAgICAgICAgPlxuICAgICAgICAgIHtpc1NlbGVjdGVkID8gJ1NlbGVjaW9uYWRvJyA6ICdTZWxlY2lvbmFyIE1vZGVsbyd9XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBNb2RlbFNlbGVjdGlvbk1vZGFsO1xuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQXV0aCIsImRiIiwiZ2V0VXNlckFQSUVuZHBvaW50cyIsIm9wZW5Sb3V0ZXJTZXJ2aWNlIiwiZGVlcFNlZWtTZXJ2aWNlIiwibW9kZWxGYXZvcml0ZXNTZXJ2aWNlIiwidXNlQWR2YW5jZWRTZWFyY2giLCJhZHZhbmNlZEZpbHRlcnNTZXJ2aWNlIiwiQWR2YW5jZWRTZWFyY2hJbnB1dCIsIkhpZ2hsaWdodGVkVGV4dCIsIkV4cGVuc2l2ZU1vZGVsQ29uZmlybWF0aW9uTW9kYWwiLCJDQUNIRV9EVVJBVElPTiIsIkVORFBPSU5UU19DQUNIRV9EVVJBVElPTiIsIk1vZGVsU2VsZWN0aW9uTW9kYWwiLCJpc09wZW4iLCJvbkNsb3NlIiwiY3VycmVudE1vZGVsIiwib25Nb2RlbFNlbGVjdCIsInVzZXIiLCJlbmRwb2ludHMiLCJzZXRFbmRwb2ludHMiLCJzZWxlY3RlZEVuZHBvaW50Iiwic2V0U2VsZWN0ZWRFbmRwb2ludCIsIm1vZGVscyIsInNldE1vZGVscyIsImZhdm9yaXRlTW9kZWxJZHMiLCJzZXRGYXZvcml0ZU1vZGVsSWRzIiwiU2V0IiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwiZGlzcGxheWVkTW9kZWxzQ291bnQiLCJzZXREaXNwbGF5ZWRNb2RlbHNDb3VudCIsIk1PREVMU19QRVJfUEFHRSIsImN1c3RvbU1vZGVsSWQiLCJzZXRDdXN0b21Nb2RlbElkIiwic2hvd0V4cGVuc2l2ZU1vZGVsTW9kYWwiLCJzZXRTaG93RXhwZW5zaXZlTW9kZWxNb2RhbCIsInBlbmRpbmdFeHBlbnNpdmVNb2RlbCIsInNldFBlbmRpbmdFeHBlbnNpdmVNb2RlbCIsInNlbGVjdGVkQ2F0ZWdvcnkiLCJzZXRTZWxlY3RlZENhdGVnb3J5Iiwic21hcnRDYXRlZ29yaWVzIiwic2V0U21hcnRDYXRlZ29yaWVzIiwiZmF2b3JpdGVUb2dnbGluZyIsInNldEZhdm9yaXRlVG9nZ2xpbmciLCJsYXN0TG9hZGVkRW5kcG9pbnQiLCJzZXRMYXN0TG9hZGVkRW5kcG9pbnQiLCJtb2RlbHNDYWNoZSIsInNldE1vZGVsc0NhY2hlIiwiTWFwIiwiZW5kcG9pbnRzQ2FjaGUiLCJzZXRFbmRwb2ludHNDYWNoZSIsImZpbHRlcnMiLCJzZXRGaWx0ZXJzIiwiY2F0ZWdvcnkiLCJzb3J0QnkiLCJzZWFyY2hUZXJtIiwic2V0U2VhcmNoVGVybSIsInNlYXJjaFJlc3VsdHMiLCJzdWdnZXN0aW9ucyIsImlzU2VhcmNoaW5nIiwiaGFzU2VhcmNoZWQiLCJjbGVhclNlYXJjaCIsInRyYWNrTW9kZWxTZWxlY3Rpb24iLCJkZWJvdW5jZU1zIiwiZW5hYmxlU3VnZ2VzdGlvbnMiLCJjYWNoZVJlc3VsdHMiLCJmdXp6eVRocmVzaG9sZCIsIm1heFJlc3VsdHMiLCJib29zdEZhdm9yaXRlcyIsIkRhdGUiLCJub3ciLCJ0aW1lc3RhbXAiLCJsZW5ndGgiLCJvcGVuUm91dGVyRW5kcG9pbnQiLCJmaW5kIiwiZXAiLCJuYW1lIiwiZGVlcFNlZWtFbmRwb2ludCIsImxvYWRFbmRwb2ludHMiLCJmYXZvcml0ZXNMb2FkZWRGb3JFbmRwb2ludCIsInNldEZhdm9yaXRlc0xvYWRlZEZvckVuZHBvaW50IiwiZW5kcG9pbnRLZXkiLCJpZCIsImNvbnNvbGUiLCJsb2ciLCJ0aW1lciIsInNldFRpbWVvdXQiLCJ1cGRhdGVGYXZvcml0ZXNGcm9tRmlyZXN0b3JlIiwiY2xlYXJUaW1lb3V0IiwiY2FjaGVLZXkiLCJjYWNoZWREYXRhIiwiZ2V0IiwiY2FjaGVkRmF2b3JpdGVzIiwiZmlsdGVyIiwibSIsImlzRmF2b3JpdGUiLCJtYXAiLCJsb2FkT3BlblJvdXRlck1vZGVscyIsImxvYWREZWVwU2Vla01vZGVscyIsInVzZXJuYW1lIiwiZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlIiwiZmF2b3JpdGVJZHMiLCJnZXRGYXZvcml0ZU1vZGVsSWRzIiwiY3VycmVudEZhdm9yaXRlSWRzIiwiQXJyYXkiLCJmcm9tIiwibmV3RmF2b3JpdGVJZHMiLCJoYXNDaGFuZ2VkIiwiZXZlcnkiLCJoYXMiLCJwcmV2TW9kZWxzIiwidXBkYXRlZE1vZGVscyIsIm1vZGVsIiwiZmF2b3JpdGVkTW9kZWxzIiwicHJldiIsImN1cnJlbnRDYWNoZSIsInNldCIsImdldFNtYXJ0Q2F0ZWdvcmllcyIsImVtYWlsIiwiY29sbGVjdGlvbiIsInF1ZXJ5Iiwid2hlcmUiLCJnZXREb2NzIiwidXN1YXJpb3NSZWYiLCJxIiwicXVlcnlTbmFwc2hvdCIsImVtcHR5IiwidXNlckRvYyIsImRvY3MiLCJkYXRhIiwidXNlckVuZHBvaW50cyIsIm1lc3NhZ2UiLCJvcGVuUm91dGVyTW9kZWxzIiwiZmV0Y2hNb2RlbHMiLCJzaXplIiwibW9kZWxzV2l0aEZhdm9yaXRlcyIsImZhdm9yaXRlZENvdW50IiwiZGVlcFNlZWtNb2RlbHMiLCJnZXRGaWx0ZXJlZE1vZGVscyIsImZpbHRlcmVkIiwiZmlsdGVyQnlDYXRlZ29yeSIsInRyaW0iLCJzZWFyY2hSZXN1bHRNb2RlbHMiLCJyZXN1bHQiLCJzb21lIiwiZiIsImNhdGVnb3J5RmlsdGVyZWQiLCJnZXRNb2RlbHNCeUNhdGVnb3J5Iiwic2VydmljZSIsInNvcnRNb2RlbHMiLCJmaWx0ZXJlZE1vZGVscyIsImhhbmRsZVRvZ2dsZUZhdm9yaXRlIiwib3B0aW1pc3RpY05ld1N0YXR1cyIsImFkZCIsInVwZGF0ZWRGYXZvcml0ZUlkcyIsImRlbGV0ZSIsImFjdHVhbE5ld1N0YXR1cyIsInRvZ2dsZUZhdm9yaXRlIiwid2FybiIsImNvcnJlY3RlZEZhdm9yaXRlSWRzIiwiY29ycmVjdGVkTW9kZWxzIiwicmV2ZXJ0ZWRGYXZvcml0ZUlkcyIsInJldmVydGVkTW9kZWxzIiwibmV3U2V0IiwiaXNFeHBlbnNpdmVNb2RlbCIsInRvdGFsUHJpY2UiLCJnZXRUb3RhbFByaWNlIiwiaGFuZGxlU2VsZWN0TW9kZWwiLCJoYW5kbGVDb25maXJtRXhwZW5zaXZlTW9kZWwiLCJoYW5kbGVDYW5jZWxFeHBlbnNpdmVNb2RlbCIsImhhbmRsZUxvYWRNb3JlTW9kZWxzIiwiaGFuZGxlVXNlQ3VzdG9tTW9kZWwiLCJoYW5kbGVSZWZyZXNoTW9kZWxzIiwibmV3Q2FjaGUiLCJkaXYiLCJjbGFzc05hbWUiLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJoMiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJsYWJlbCIsImRpc2FibGVkIiwidGl0bGUiLCJzZWxlY3QiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsImVuZHBvaW50IiwidGFyZ2V0Iiwib3B0aW9uIiwiaDQiLCJzcGFuIiwiaW5wdXQiLCJ0eXBlIiwicGxhY2Vob2xkZXIiLCJvbktleURvd24iLCJrZXkiLCJwIiwic2hvd1N1Z2dlc3Rpb25zIiwic2xpY2UiLCJkZXNjcmlwdGlvbiIsImljb24iLCJ1bCIsImxpIiwic2VhcmNoUmVzdWx0IiwiciIsIk1vZGVsQ2FyZCIsImlzU2VsZWN0ZWQiLCJvblNlbGVjdCIsIm9uVG9nZ2xlRmF2b3JpdGUiLCJpc1RvZ2dsaW5nIiwiTWF0aCIsIm1pbiIsImgzIiwiRGVlcFNlZWtNb2RlbENhcmQiLCJvbkNvbmZpcm0iLCJvbkNhbmNlbCIsImlzRXhwZW5zaXZlIiwiZGF0YS1tb2RlbC1pZCIsImhpZ2hsaWdodGVkTmFtZSIsImRhbmdlcm91c2x5U2V0SW5uZXJIVE1MIiwiX19odG1sIiwidGV4dCIsImhpZ2hsaWdodCIsIm1hdGNoZWRGaWVsZHMiLCJmaWVsZCIsImlzRnJlZU1vZGVsIiwiaGlnaGxpZ2h0ZWREZXNjcmlwdGlvbiIsImZvcm1hdENvbnRleHRMZW5ndGgiLCJjb250ZXh0X2xlbmd0aCIsImZvcm1hdFByaWNlIiwicHJpY2luZyIsInByb21wdCIsImNvbXBsZXRpb24iLCJnZXRNb2RlbEljb24iLCJtb2RlbElkIiwic3RvcFByb3BhZ2F0aW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\n"));

/***/ })

});