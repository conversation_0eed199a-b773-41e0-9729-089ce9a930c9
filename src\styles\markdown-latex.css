/* ===== RAFTHOR LATEX - TEMA AZUL ELEGANTE ===== */

/* ===== FÓRMULAS INLINE ===== */
.markdown-content .katex {
  font-size: 1.15em !important;
  color: #f1f5f9 !important;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(30, 58, 138, 0.1)) !important;
  padding: 4px 8px !important;
  border-radius: 6px !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1) !important;
  transition: all 0.2s ease !important;
  display: inline-block !important;
  margin: 0 2px !important;
}

.markdown-content .katex:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(30, 58, 138, 0.15)) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2) !important;
  transform: translateY(-1px) !important;
}

/* ===== FÓRMULAS EM BLOCO - DESIGN ELEGANTE ===== */
.markdown-content .katex-display {
  margin: 2.5rem auto !important;
  padding: 2rem !important;
  background: linear-gradient(135deg,
    rgba(13, 28, 74, 0.8),
    rgba(27, 46, 118, 0.6),
    rgba(59, 130, 246, 0.1)
  ) !important;
  border: 2px solid rgba(59, 130, 246, 0.3) !important;
  border-radius: 16px !important;
  overflow-x: auto !important;
  position: relative !important;
  backdrop-filter: blur(12px) !important;
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  max-width: 90% !important;
  text-align: center !important;
  transition: all 0.3s ease !important;
}

.markdown-content .katex-display:hover {
  border-color: rgba(59, 130, 246, 0.5) !important;
  box-shadow:
    0 12px 40px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  transform: translateY(-4px) !important;
}

/* Efeito decorativo nas bordas */
.markdown-content .katex-display::before {
  content: '' !important;
  position: absolute !important;
  top: -2px !important;
  left: -2px !important;
  right: -2px !important;
  bottom: -2px !important;
  background: linear-gradient(45deg, #3b82f6, #60a5fa, #93c5fd, #3b82f6) !important;
  border-radius: 18px !important;
  z-index: -1 !important;
  opacity: 0.6 !important;
  animation: borderGlow 3s ease-in-out infinite !important;
}

@keyframes borderGlow {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 0.8; }
}

/* ===== ELEMENTOS MATEMÁTICOS - PALETA AZUL RAFTHOR ===== */

/* Base das fórmulas */
.markdown-content .katex .base {
  color: #f8fafc !important;
}

/* Operadores matemáticos (+, -, =, <, >, etc.) */
.markdown-content .katex .mbin,
.markdown-content .katex .mrel {
  color: #60a5fa !important;
  font-weight: 600 !important;
}

/* Números e variáveis */
.markdown-content .katex .mord {
  color: #e2e8f0 !important;
}

/* Funções matemáticas (sin, cos, log, etc.) */
.markdown-content .katex .mop {
  color: #93c5fd !important;
  font-weight: 600 !important;
}

/* Parênteses e delimitadores */
.markdown-content .katex .mopen,
.markdown-content .katex .mclose {
  color: #3b82f6 !important;
  font-weight: 700 !important;
}

/* Sobrescritos e subscritos */
.markdown-content .katex .msupsub {
  color: #bfdbfe !important;
}

/* ===== CENTRALIZAÇÃO AUTOMÁTICA ===== */
.markdown-content .katex-display > .katex {
  text-align: center !important;
  display: block !important;
  margin: 0 auto !important;
}

/* Container principal centralizado */
.rafthor-markdown .katex-display {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
}

/* ===== ELEMENTOS ESPECIAIS - TEMA AZUL ===== */

/* HTML renderizado */
.markdown-content .katex .katex-html {
  color: #f1f5f9 !important;
}

/* Frações - linha divisória azul */
.markdown-content .katex .frac-line {
  border-bottom-color: #3b82f6 !important;
  border-bottom-width: 1.5px !important;
}

/* Raízes quadradas */
.markdown-content .katex .sqrt > .root {
  color: #60a5fa !important;
  font-weight: 600 !important;
}

/* Integrais e somatórios */
.markdown-content .katex .op-symbol {
  color: #3b82f6 !important;
  font-weight: 700 !important;
  font-size: 1.1em !important;
}

/* Texto em fórmulas */
.markdown-content .katex .text {
  color: #cbd5e1 !important;
  font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto !important;
}

/* Acentos e decorações */
.markdown-content .katex .accent > .accent-body {
  color: #60a5fa !important;
}

/* Vetores */
.markdown-content .katex .overrightarrow > .arrow {
  color: #3b82f6 !important;
}

/* Matrizes */
.markdown-content .katex .arraycolsep {
  width: 0.6em !important;
}

/* Sistemas de equações */
.markdown-content .katex .cases > .arraycolsep {
  width: 0.3em !important;
}

/* Limites */
.markdown-content .katex .op-limits > .vlist-t {
  color: #93c5fd !important;
}

/* ===== RESPONSIVIDADE E OTIMIZAÇÕES ===== */

/* Dispositivos móveis */
@media (max-width: 768px) {
  .markdown-content .katex-display {
    font-size: 0.85em !important;
    padding: 1.5rem 1rem !important;
    margin: 2rem auto !important;
    max-width: 95% !important;
    border-radius: 12px !important;
  }

  .markdown-content .katex {
    font-size: 1em !important;
    padding: 3px 6px !important;
  }

  .markdown-content .katex-display::before {
    border-radius: 14px !important;
  }
}

/* Tablets */
@media (max-width: 1024px) {
  .markdown-content .katex-display {
    max-width: 85% !important;
    padding: 1.75rem !important;
  }
}

/* ===== FONTES E TIPOGRAFIA ===== */
.markdown-content .katex .mathdefault {
  font-family: KaTeX_Main, "Times New Roman", serif !important;
}

.markdown-content .katex .katex-mathml {
  color: #f8fafc !important;
}

/* ===== ESPAÇAMENTO E ALINHAMENTO ===== */
.markdown-content .katex .mspace {
  margin: 0 0.25em !important;
}

/* ===== ACESSIBILIDADE ===== */
.markdown-content .katex[title] {
  cursor: help !important;
  transition: all 0.2s ease !important;
}

.markdown-content .katex[title]:hover {
  opacity: 0.8 !important;
}

/* ===== OTIMIZAÇÕES PARA ALTA RESOLUÇÃO ===== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .markdown-content .katex {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }
}

/* ===== ANIMAÇÕES SUAVES ===== */
.markdown-content .katex * {
  transition: color 0.2s ease !important;
}

/* ===== SCROLL HORIZONTAL ELEGANTE ===== */
.markdown-content .katex-display::-webkit-scrollbar {
  height: 8px !important;
}

.markdown-content .katex-display::-webkit-scrollbar-track {
  background: rgba(59, 130, 246, 0.1) !important;
  border-radius: 4px !important;
}

.markdown-content .katex-display::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #60a5fa) !important;
  border-radius: 4px !important;
}

.markdown-content .katex-display::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #60a5fa, #93c5fd) !important;
}
