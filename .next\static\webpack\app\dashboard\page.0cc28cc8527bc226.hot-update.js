"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useAdvancedSearch.ts":
/*!****************************************!*\
  !*** ./src/hooks/useAdvancedSearch.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAdvancedSearch: function() { return /* binding */ useAdvancedSearch; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useAdvancedSearch(models) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { debounceMs = 300, enableSuggestions = true, cacheResults = true, fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false, userId = null, trackAnalytics = true } = options;\n    const [searchTerm, setSearchTermState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [hasSearched, setHasSearched] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [debounceTimer, setDebounceTimer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Cache para resultados de busca\n    const [searchCache] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Map());\n    // Função de busca principal\n    const performSearch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (term)=>{\n        if (!term.trim()) {\n            setSearchResults([]);\n            setHasSearched(false);\n            return;\n        }\n        setIsSearching(true);\n        try {\n            // Verificar cache se habilitado\n            if (cacheResults && searchCache.has(term)) {\n                setSearchResults(searchCache.get(term));\n                setHasSearched(true);\n                setIsSearching(false);\n                return;\n            }\n            // Realizar busca\n            const results = searchModels(models, term, {\n                fuzzyThreshold,\n                maxResults,\n                boostFavorites\n            });\n            // Salvar no cache se habilitado\n            if (cacheResults) {\n                searchCache.set(term, results);\n            }\n            setSearchResults(results);\n            setHasSearched(true);\n            // Rastrear termo de busca se analytics estiver habilitado\n            if (trackAnalytics && userId) {\n                await searchAnalyticsService.trackSearchTerm(userId, term);\n            }\n        } catch (error) {\n            console.error(\"Error performing search:\", error);\n            setSearchResults([]);\n        } finally{\n            setIsSearching(false);\n        }\n    }, [\n        models,\n        fuzzyThreshold,\n        maxResults,\n        boostFavorites,\n        cacheResults,\n        searchCache,\n        trackAnalytics,\n        userId\n    ]);\n    // Função para definir termo de busca com debounce\n    const setSearchTerm = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((term)=>{\n        setSearchTermState(term);\n        // Limpar timer anterior\n        if (debounceTimer) {\n            clearTimeout(debounceTimer);\n        }\n        // Configurar novo timer\n        const timer = setTimeout(()=>{\n            performSearch(term);\n        }, debounceMs);\n        setDebounceTimer(timer);\n    }, [\n        debounceTimer,\n        debounceMs,\n        performSearch\n    ]);\n    // Carregar sugestões quando o termo muda\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (enableSuggestions && searchTerm.length > 0 && userId) {\n            searchAnalyticsService.getSearchSuggestions(userId, searchTerm, 5).then(setSuggestions).catch((error)=>{\n                console.error(\"Error loading suggestions:\", error);\n                setSuggestions([]);\n            });\n        } else {\n            setSuggestions([]);\n        }\n    }, [\n        searchTerm,\n        enableSuggestions,\n        userId\n    ]);\n    // Limpar busca\n    const clearSearch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setSearchTermState(\"\");\n        setSearchResults([]);\n        setHasSearched(false);\n        setSuggestions([]);\n        if (debounceTimer) {\n            clearTimeout(debounceTimer);\n            setDebounceTimer(null);\n        }\n    }, [\n        debounceTimer\n    ]);\n    // Rastrear seleção de modelo\n    const trackModelSelection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (modelId)=>{\n        if (trackAnalytics && userId) {\n            const model = models.find((m)=>m.id === modelId);\n            if (model) {\n                await searchAnalyticsService.trackModelSelection(userId, modelId, model.name, hasSearched ? searchTerm : undefined);\n            }\n        }\n    }, [\n        trackAnalytics,\n        userId,\n        models,\n        hasSearched,\n        searchTerm\n    ]);\n    // Limpar timer ao desmontar\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            if (debounceTimer) {\n                clearTimeout(debounceTimer);\n            }\n        };\n    }, [\n        debounceTimer\n    ]);\n    return {\n        searchTerm,\n        setSearchTerm,\n        searchResults,\n        suggestions,\n        isSearching,\n        hasSearched,\n        clearSearch,\n        trackModelSelection\n    };\n}\n// Função de busca com fuzzy matching\nfunction searchModels(models, searchTerm) {\n    let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n    const { fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n    if (!searchTerm.trim()) {\n        return [];\n    }\n    const term = searchTerm.toLowerCase();\n    const results = [];\n    for (const model of models){\n        let score = 0;\n        const matchedFields = [];\n        let highlightedName = model.name;\n        let highlightedDescription = model.description || \"\";\n        // Busca no nome (peso maior)\n        if (model.name.toLowerCase().includes(term)) {\n            score += 10;\n            matchedFields.push(\"name\");\n            highlightedName = highlightText(model.name, term);\n        }\n        // Busca no ID (peso médio)\n        if (model.id.toLowerCase().includes(term)) {\n            score += 7;\n            matchedFields.push(\"id\");\n        }\n        // Busca na descrição (peso menor)\n        if (model.description && model.description.toLowerCase().includes(term)) {\n            score += 3;\n            matchedFields.push(\"description\");\n            highlightedDescription = highlightText(model.description, term);\n        }\n        // Boost para favoritos\n        if (boostFavorites && model.isFavorite) {\n            score *= 1.5;\n        }\n        // Boost para modelos gratuitos se buscar por \"free\" ou \"grátis\"\n        if ((term.includes(\"free\") || term.includes(\"gr\\xe1tis\")) && isModelFree(model)) {\n            score += 5;\n        }\n        // Boost para modelos caros se buscar por \"expensive\" ou \"caro\"\n        if ((term.includes(\"expensive\") || term.includes(\"caro\")) && getTotalPrice(model) > 0.00002) {\n            score += 5;\n        }\n        if (score > 0) {\n            results.push({\n                model,\n                score,\n                matchedFields,\n                highlightedName,\n                highlightedDescription\n            });\n        }\n    }\n    // Ordenar por score e limitar resultados\n    return results.sort((a, b)=>b.score - a.score).slice(0, maxResults);\n}\n// Função auxiliar para destacar texto\nfunction highlightText(text, term) {\n    const regex = new RegExp(\"(\".concat(term, \")\"), \"gi\");\n    return text.replace(regex, '<mark class=\"bg-yellow-300 text-black px-1 rounded\">$1</mark>');\n}\n// Função auxiliar para verificar se modelo é gratuito\nfunction isModelFree(model) {\n    const promptPrice = parseFloat(model.pricing.prompt);\n    const completionPrice = parseFloat(model.pricing.completion);\n    return promptPrice === 0 && completionPrice === 0;\n}\n// Função auxiliar para obter preço total\nfunction getTotalPrice(model) {\n    const promptPrice = parseFloat(model.pricing.prompt);\n    const completionPrice = parseFloat(model.pricing.completion);\n    return promptPrice + completionPrice;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\n"));

/***/ })

});