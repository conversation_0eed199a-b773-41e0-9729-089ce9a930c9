"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/[username]/[chatId]/route";
exports.ids = ["app/api/chat/[username]/[chatId]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Froute&page=%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Froute.ts&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Froute&page=%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Froute.ts&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/node-polyfill-headers */ \"(rsc)/./node_modules/next/dist/server/node-polyfill-headers.js\");\n/* harmony import */ var next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_node_polyfill_headers__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var C_Users_Henri_Desktop_Rafthor_RafthorIA_src_app_api_chat_username_chatId_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/[username]/[chatId]/route.ts */ \"(rsc)/./src/app/api/chat/[username]/[chatId]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_1__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_2__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/[username]/[chatId]/route\",\n        pathname: \"/api/chat/[username]/[chatId]\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/[username]/[chatId]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\api\\\\chat\\\\[username]\\\\[chatId]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Henri_Desktop_Rafthor_RafthorIA_src_app_api_chat_username_chatId_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/chat/[username]/[chatId]/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZjaGF0JTJGJTVCdXNlcm5hbWUlNUQlMkYlNUJjaGF0SWQlNUQlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmNoYXQlMkYlNUJ1c2VybmFtZSU1RCUyRiU1QmNoYXRJZCU1RCUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmNoYXQlMkYlNUJ1c2VybmFtZSU1RCUyRiU1QmNoYXRJZCU1RCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNIZW5yaSU1Q0Rlc2t0b3AlNUNSYWZ0aG9yJTVDUmFmdGhvcklBJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNIZW5yaSU1Q0Rlc2t0b3AlNUNSYWZ0aG9yJTVDUmFmdGhvcklBJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQWdEO0FBQ3NEO0FBQ3ZDO0FBQ2dFO0FBQy9IO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnSEFBbUI7QUFDM0M7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsdUdBQXVHO0FBQy9HO0FBQ2lKOztBQUVqSiIsInNvdXJjZXMiOlsid2VicGFjazovL3JhZnRob3IvPzNjNTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFwibmV4dC9kaXN0L3NlcnZlci9ub2RlLXBvbHlmaWxsLWhlYWRlcnNcIjtcbmltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFVzZXJzXFxcXEhlbnJpXFxcXERlc2t0b3BcXFxcUmFmdGhvclxcXFxSYWZ0aG9ySUFcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcY2hhdFxcXFxbdXNlcm5hbWVdXFxcXFtjaGF0SWRdXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9jaGF0L1t1c2VybmFtZV0vW2NoYXRJZF0vcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9jaGF0L1t1c2VybmFtZV0vW2NoYXRJZF1cIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2NoYXQvW3VzZXJuYW1lXS9bY2hhdElkXS9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXEhlbnJpXFxcXERlc2t0b3BcXFxcUmFmdGhvclxcXFxSYWZ0aG9ySUFcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcY2hhdFxcXFxbdXNlcm5hbWVdXFxcXFtjaGF0SWRdXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIGhlYWRlckhvb2tzLCBzdGF0aWNHZW5lcmF0aW9uQmFpbG91dCB9ID0gcm91dGVNb2R1bGU7XG5jb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvYXBpL2NoYXQvW3VzZXJuYW1lXS9bY2hhdElkXS9yb3V0ZVwiO1xuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBoZWFkZXJIb29rcywgc3RhdGljR2VuZXJhdGlvbkJhaWxvdXQsIG9yaWdpbmFsUGF0aG5hbWUsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Froute&page=%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Froute.ts&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/[username]/[chatId]/route.ts":
/*!*******************************************************!*\
  !*** ./src/app/api/chat/[username]/[chatId]/route.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/storage */ \"(rsc)/./node_modules/firebase/storage/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/firebase */ \"(rsc)/./src/lib/firebase.ts\");\n\n\n\nasync function GET(request, { params }) {\n    try {\n        const { username, chatId } = params;\n        // Validar parâmetros\n        if (!username || !chatId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Username e chatId s\\xe3o obrigat\\xf3rios\"\n            }, {\n                status: 400\n            });\n        }\n        // Referência para o arquivo chat.json no Firebase Storage\n        const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_1__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.storage, `usuarios/${username}/conversas/${chatId}/chat.json`);\n        try {\n            // Obter URL de download\n            const downloadUrl = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_1__.getDownloadURL)(chatJsonRef);\n            // Fazer fetch do arquivo\n            const response = await fetch(downloadUrl);\n            if (!response.ok) {\n                throw new Error(`Erro ao buscar arquivo: ${response.statusText}`);\n            }\n            const chatData = await response.json();\n            // Retornar dados com headers CORS apropriados\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(chatData, {\n                headers: {\n                    \"Access-Control-Allow-Origin\": \"*\",\n                    \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n                    \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n                }\n            });\n        } catch (storageError) {\n            console.error(\"Erro ao acessar Firebase Storage:\", storageError);\n            // Se o arquivo não existe, retornar estrutura vazia\n            const emptyChatData = {\n                id: chatId,\n                name: \"Chat\",\n                messages: [],\n                createdAt: new Date().toISOString(),\n                lastUpdated: new Date().toISOString()\n            };\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(emptyChatData, {\n                headers: {\n                    \"Access-Control-Allow-Origin\": \"*\",\n                    \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n                    \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n                }\n            });\n        }\n    } catch (error) {\n        console.error(\"Erro na API de chat:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Erro interno do servidor\"\n        }, {\n            status: 500,\n            headers: {\n                \"Access-Control-Allow-Origin\": \"*\",\n                \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n                \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n            }\n        });\n    }\n}\n// Suporte para preflight requests (OPTIONS)\nasync function OPTIONS() {\n    return new next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"](null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/[username]/[chatId]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   functions: () => (/* binding */ functions),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(rsc)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(rsc)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(rsc)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(rsc)/./node_modules/firebase/storage/dist/index.mjs\");\n/* harmony import */ var firebase_functions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/functions */ \"(rsc)/./node_modules/firebase/functions/dist/index.mjs\");\n\n\n\n\n\n// Configuração do Firebase - novo app criado - ATUALIZADO\nconst firebaseConfig = {\n    apiKey: \"AIzaSyA4ojPmlKBkDDl2hcfNPDXG23tEgolgCv8\",\n    authDomain: \"rafthor-0001.firebaseapp.com\",\n    projectId: \"rafthor-0001\",\n    storageBucket: \"rafthor-0001.firebasestorage.app\",\n    messagingSenderId: \"863587500028\",\n    appId: \"1:863587500028:web:ea161ddd3a1a024a7f3c79\"\n};\n// Verificar se a configuração está correta\nif (!firebaseConfig.apiKey || firebaseConfig.apiKey.length < 30) {\n    throw new Error(\"Firebase API Key inv\\xe1lida ou n\\xe3o configurada\");\n}\n// Inicializar Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n// Inicializar serviços\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\nconst functions = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_4__.getFunctions)(app);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/firebase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@firebase","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/idb"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Froute&page=%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2F%5Busername%5D%2F%5BchatId%5D%2Froute.ts&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();